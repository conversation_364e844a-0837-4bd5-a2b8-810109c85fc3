import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { getBatchJobDetails } from '@/lib/api';
import { BatchJobContent } from '@/components/features/batch/batch-job-content';
import { BatchJobSkeleton } from '@/components/features/batch/batch-job-skeleton';
import { ErrorDisplay } from '@/components/shared/error-display';
import { Suspense } from 'react';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ batchJobId: string }>;
}): Promise<Metadata> {
  const { batchJobId } = await params;

  try {
    const batchJob = await getBatchJobDetails(batchJobId);
    if (batchJob && batchJob.sourceInfo?.name) {
      return {
        title: `批量下载 ${batchJob.sourceInfo.name} - YTDownloader`,
        description: `批量下载任务"${batchJob.sourceInfo.name}"的进度和详情，包含${batchJob.stats.totalSelected}个视频。`,
      };
    }
  } catch {
    // 忽略错误，使用默认metadata
  }

  return {
    title: '批量下载 - YTDownloader',
    description: '查看批量下载任务的进度和详情，支持播放列表和频道批量下载。',
  };
}

export default async function BatchJobPage({
  params,
}: {
  params: Promise<{ batchJobId: string }>;
}) {
  const { batchJobId } = await params;

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <Suspense fallback={<BatchJobSkeleton />}>
          <BatchJobContentWrapper batchJobId={batchJobId} />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}

async function BatchJobContentWrapper({ batchJobId }: { batchJobId: string }) {
  const batchJobData = await getBatchJobDetails(batchJobId);

  // 如果 batchJobData 为 null，渲染错误组件
  if (!batchJobData) {
    return (
      <ErrorDisplay
        title="无法加载批量任务"
        description="该批量任务可能不存在、已被删除，或您没有访问权限。请检查链接是否正确。"
      />
    );
  }

  return <BatchJobContent batchJobData={batchJobData} />;
}
