'use client';

import { useState } from 'react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { Check, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Language {
  name: string;
  nativeName: string;
}

// 按语言流行度和使用人数排序（全球范围）
const languages: Language[] = [
  { name: 'English', nativeName: 'English' }, // 15亿使用者，国际通用语
  { name: 'Chinese (Simplified)', nativeName: '简体中文' }, // 11亿使用者
  { name: 'Hindi', nativeName: 'हिन्दी' }, // 6亿使用者
  { name: 'Spanish', nativeName: 'Español' }, // 5亿使用者
  { name: 'Arabic', nativeName: 'العربية' }, // 4.2亿使用者
  { name: 'Bengali', nativeName: 'বাংলা' }, // 2.7亿使用者
  { name: 'Portuguese', nativeName: 'Português' }, // 2.6亿使用者
  { name: 'Russian', nativeName: 'Русский' }, // 2.6亿使用者
  { name: 'Japanese', nativeName: '日本語' }, // 1.25亿使用者
  { name: 'Urdu', nativeName: 'اردو' }, // 2.3亿使用者
  { name: 'Indonesian', nativeName: 'Bahasa Indonesia' }, // 2亿使用者
  { name: 'German', nativeName: 'Deutsch' }, // 1.3亿使用者
  { name: 'French', nativeName: 'Français' }, // 2.8亿使用者
  { name: 'Turkish', nativeName: 'Türkçe' }, // 8800万使用者
  { name: 'Korean', nativeName: '한국어' }, // 7700万使用者
  { name: 'Vietnamese', nativeName: 'Tiếng Việt' }, // 7600万使用者
  { name: 'Italian', nativeName: 'Italiano' }, // 6500万使用者
  { name: 'Tamil', nativeName: 'தமிழ்' }, // 7500万使用者
  { name: 'Marathi', nativeName: 'मराठी' }, // 8300万使用者
  { name: 'Telugu', nativeName: 'తెలుగు' }, // 8100万使用者
  { name: 'Thai', nativeName: 'ไทย' }, // 6000万使用者
  { name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ' }, // 1.1亿使用者
  { name: 'Polish', nativeName: 'Polski' }, // 4500万使用者
  { name: 'Ukrainian', nativeName: 'Українська' }, // 4000万使用者
  { name: 'Dutch', nativeName: 'Nederlands' }, // 2400万使用者
  { name: 'Swahili', nativeName: 'Kiswahili' }, // 2000万使用者
];

export function LanguageSwitcher() {
  const [currentLang, setCurrentLang] = useState<Language>(
    languages.find((lang) => lang.nativeName === '简体中文') || languages[0],
  );
  const [value, setValue] = useState<string>('');

  const handleLanguageChange = (language: Language) => {
    setCurrentLang(language);
    setValue(''); // 关闭菜单
    // 这里可以添加实际的语言切换逻辑
    console.log('Language changed to:', language.nativeName);
  };

  return (
    <NavigationMenu value={value} onValueChange={setValue} viewport={false}>
      <NavigationMenuList>
        <NavigationMenuItem value="language">
          <NavigationMenuTrigger className="gap-2 px-3 py-2 h-auto [&>svg:last-child]:ml-0">
            <Globe className="h-4 w-4" />
            <span className="hidden font-normal text-sm sm:block">{currentLang.nativeName}</span>
            <span className="sr-only">切换语言</span>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="w-56 max-h-96 overflow-y-auto p-2">
              <div className="grid gap-1">
                {languages.map((language) => (
                  <button
                    key={language.nativeName}
                    onClick={() => handleLanguageChange(language)}
                    className={cn(
                      'flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors',
                      'hover:bg-accent hover:text-accent-foreground',
                      'focus:bg-accent focus:text-accent-foreground focus:outline-none',
                      currentLang.nativeName === language.nativeName &&
                        'bg-accent text-accent-foreground',
                    )}>
                    <div className="flex-1 min-w-0">
                      <span className="text-sm font-normal truncate">{language.nativeName}</span>
                    </div>
                    {currentLang.nativeName === language.nativeName && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}
