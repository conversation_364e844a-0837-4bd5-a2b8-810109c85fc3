﻿using Api.Services;
using Microsoft.AspNetCore.Mvc;

namespace Api.Endpoints;

public static class YouTubeEndpoints
{
    public static void MapYouTubeEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/youtube").WithTags("YouTube");

        // 获取视频元数据 - 需要用户身份追踪
        group.MapGet("/metadata", async ([FromQuery] string videoId, UserService userService, YouTubeService youTubeService, HttpContext httpContext) =>
        {
            if (string.IsNullOrEmpty(videoId)) return Results.BadRequest(new { error = "VIDEO_ID_REQUIRED" });

            // 确保用户身份存在（这会触发匿名用户创建）
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 获取视频元数据
            var videoData = await youTubeService.GetVideoMetadataAsync(videoId);

            return Results.Ok(videoData);
        }).WithSummary("获取视频元数据").WithDescription("获取YouTube视频的详细信息，包括标题、时长、可用格式等");

        // 创建下载任务 - 需要用户身份追踪
        group.MapPost("/download", async (
            [FromBody] CreateDownloadRequest request, UserService userService, YouTubeService youTubeService, HttpContext httpContext) =>
        {
            // 确保用户身份存在
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 创建下载任务
            var taskData = await youTubeService.CreateDownloadTaskAsync(request.VideoId, request.Format, request.Quality);

            return Results.Ok(new { taskData, userId = userIdentity.UserId, isAnonymous = userIdentity.IsAnonymous });
        }).WithSummary("创建下载任务").WithDescription("创建视频下载任务");

        // 获取播放列表信息 - 需要用户身份追踪
        group.MapGet("/playlist/{playlistId}", async (string playlistId, UserService userService, YouTubeService youTubeService, HttpContext httpContext) =>
        {
            if (string.IsNullOrEmpty(playlistId)) return Results.BadRequest(new { error = "PLAYLIST_ID_REQUIRED" });

            // 确保用户身份存在
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 获取播放列表信息
            var playlistData = await youTubeService.GetPlaylistInfoAsync(playlistId);

            return Results.Ok(playlistData);
        }).WithSummary("获取播放列表信息").WithDescription("获取YouTube播放列表的详细信息，包括视频列表");

        // 获取频道信息 - 需要用户身份追踪
        group.MapGet("/channel/{channelId}", async (string channelId, UserService userService, YouTubeService youTubeService, HttpContext httpContext) =>
        {
            if (string.IsNullOrEmpty(channelId)) return Results.BadRequest(new { error = "CHANNEL_ID_REQUIRED" });

            // 确保用户身份存在
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 获取频道信息
            var channelData = await youTubeService.GetChannelInfoAsync(channelId);

            return Results.Ok(channelData);
        }).WithSummary("获取频道信息").WithDescription("获取YouTube频道的详细信息，包括最新视频列表");
    }

    // DTOs
    public record CreateDownloadRequest(string VideoId, string Format, string Quality);
}