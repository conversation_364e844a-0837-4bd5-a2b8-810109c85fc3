import NextAuth from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { z } from 'zod';

// 登录表单验证 schema，用于前后端统一验证
export const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
});

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    // 配置凭据（邮箱+密码）认证提供者
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: '邮箱', type: 'email' },
        password: { label: '密码', type: 'password' },
      },
      // 核心授权逻辑：当 signIn('credentials', ...) 被调用时执行
      async authorize(credentials) {
        try {
          // 1. 使用 Zod 安全地验证输入数据
          const validatedFields = loginSchema.safeParse(credentials);
          if (!validatedFields.success) {
            return null; // 验证失败
          }

          const { email, password } = validatedFields.data;

          // 2. 调用后端 API 进行实际的凭据验证
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            return null; // 后端验证失败
          }

          const data = await response.json();

          // 3. 如果后端返回了用户信息和JWT token，则认证成功
          if (data.user && data.accessToken) {
            // 返回的用户对象将用于创建会话，同时保存后端JWT token
            return {
              id: data.user.id,
              email: data.user.email,
              name: data.user.username || data.user.email,
              subscriptionTier: data.user.subscriptionTier,
              emailVerified: data.user.emailVerified,
              backendToken: data.accessToken, // 保存后端签发的access token
              refreshToken: data.refreshToken, // 保存refresh token
              tokenExpiresAt: data.expiresAt, // 保存token过期时间
            };
          }

          return null; // 未找到用户或凭据无效
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  // 配置会话管理策略
  session: {
    strategy: 'jwt', // 使用 JWT 进行会话管理，不依赖数据库
    maxAge: 30 * 24 * 60 * 60, // 会话有效期为 30 天
  },
  // 配置安全的Cookie设置
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true, // 防止XSS攻击
        sameSite: 'lax', // CSRF保护
        path: '/',
        secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS
      },
    },
  },
  // 回调函数，用于控制认证流程中的行为
  callbacks: {
    // `session` 回调：在每次访问会话时调用，用于将自定义数据从 token 同步到 session.user
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.sub!; // 从 token 的 sub 字段获取用户 ID
        // 将自定义字段从 token 添加到 session 中，使其在客户端可用
        session.user.subscriptionTier = (token.subscriptionTier as string) || 'free';
        session.user.emailVerified = token.emailVerified as boolean;
        // 将后端JWT token添加到session中，供API调用使用
        session.user.backendToken = token.backendToken as string;
        session.user.refreshToken = token.refreshToken as string;
        session.user.tokenExpiresAt = token.tokenExpiresAt as string;
      }
      return session;
    },
    // `jwt` 回调：在创建或更新 JWT 时调用，用于将自定义数据添加到 token 中
    async jwt({ token, user }) {
      // 首次登录时（user 对象存在），将后端返回的用户信息添加到 token 中
      if (user) {
        const u = user as {
          subscriptionTier?: string;
          emailVerified?: boolean;
          backendToken?: string;
          refreshToken?: string;
          tokenExpiresAt?: string;
        };
        token.subscriptionTier = u.subscriptionTier;
        token.emailVerified = u.emailVerified;
        token.backendToken = u.backendToken; // 保存后端JWT token
        token.refreshToken = u.refreshToken; // 保存refresh token
        token.tokenExpiresAt = u.tokenExpiresAt; // 保存token过期时间
      }
      return token;
    },
    // `redirect` 回调：控制登录或登出后的重定向行为
    async redirect({ url, baseUrl }) {
      if (url.startsWith('/')) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  // 自定义认证页面
  pages: {
    signIn: '/login', // 指定登录页面的路径
    error: '/auth/error', // 指定认证错误页面的路径
  },
  // 事件监听器，用于记录认证相关的事件
  events: {
    async signIn({ user, account }) {
      console.log('User signed in:', { user: user.email, account: account?.provider });
    },
    async signOut() {
      console.log('User signed out');
    },
  },
  // 在开发环境中启用调试模式
  debug: process.env.NODE_ENV === 'development',
});

// 通过模块扩展（Module Augmentation）为 NextAuth 的默认类型添加自定义字段
declare module 'next-auth' {
  interface User {
    subscriptionTier?: string;
    emailVerified?: boolean;
    backendToken?: string;
    refreshToken?: string;
    tokenExpiresAt?: string;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      subscriptionTier?: string;
      emailVerified?: boolean;
      backendToken?: string;
      refreshToken?: string;
      tokenExpiresAt?: string;
    };
  }
}

declare module '@auth/core/jwt' {
  interface JWT {
    subscriptionTier?: string;
    emailVerified?: boolean;
    backendToken?: string;
    refreshToken?: string;
    tokenExpiresAt?: string;
  }
}

// ===== 统一认证管理 =====

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL!;

// 匿名用户token的本地存储key
const ANONYMOUS_ACCESS_TOKEN_KEY = 'anonymous_access_token';
const ANONYMOUS_REFRESH_TOKEN_KEY = 'anonymous_refresh_token';

/**
 * Token对接口
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
}

/**
 * 匿名用户Token管理器
 * 单例模式，管理匿名用户的JWT token
 */
export class AnonymousTokenManager {
  private static instance: AnonymousTokenManager;
  private currentAccessToken: string | null = null;
  private currentRefreshToken: string | null = null;

  private constructor() {
    this.loadTokensFromStorage();
  }

  static getInstance(): AnonymousTokenManager {
    if (!AnonymousTokenManager.instance) {
      AnonymousTokenManager.instance = new AnonymousTokenManager();
    }
    return AnonymousTokenManager.instance;
  }

  /**
   * 获取当前access token
   */
  getToken(): string | null {
    return this.currentAccessToken;
  }

  /**
   * 获取当前refresh token
   */
  getRefreshToken(): string | null {
    return this.currentRefreshToken;
  }

  /**
   * 设置token对
   */
  setTokenPair(tokenPair: TokenPair): void {
    this.currentAccessToken = tokenPair.accessToken;
    this.currentRefreshToken = tokenPair.refreshToken;

    if (typeof window !== 'undefined') {
      localStorage.setItem(ANONYMOUS_ACCESS_TOKEN_KEY, tokenPair.accessToken);
      localStorage.setItem(ANONYMOUS_REFRESH_TOKEN_KEY, tokenPair.refreshToken);
    }
  }

  /**
   * 设置单个token（向后兼容）
   */
  setToken(token: string): void {
    this.currentAccessToken = token;

    if (typeof window !== 'undefined') {
      localStorage.setItem(ANONYMOUS_ACCESS_TOKEN_KEY, token);
    }
  }

  /**
   * 清除所有tokens
   */
  clearToken(): void {
    this.currentAccessToken = null;
    this.currentRefreshToken = null;

    if (typeof window !== 'undefined') {
      localStorage.removeItem(ANONYMOUS_ACCESS_TOKEN_KEY);
      localStorage.removeItem(ANONYMOUS_REFRESH_TOKEN_KEY);
    }
  }

  /**
   * 创建新的匿名用户token
   */
  async createToken(): Promise<string | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/anonymous/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.accessToken && data.refreshToken) {
          this.setTokenPair({
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
            expiresAt: data.expiresAt,
          });
          return data.accessToken;
        }
        // 向后兼容
        if (data.token) {
          this.setToken(data.token);
          return data.token;
        }
      }
    } catch (error) {
      console.error('Failed to create anonymous token:', error);
    }
    return null;
  }

  /**
   * 获取或创建匿名用户token
   */
  async getOrCreateToken(): Promise<string | null> {
    if (this.currentAccessToken) {
      // 验证token是否仍然有效
      if (await this.validateToken(this.currentAccessToken)) {
        return this.currentAccessToken;
      }

      // 如果access token过期，尝试使用refresh token刷新
      if (this.currentRefreshToken) {
        const newToken = await this.refreshToken();
        if (newToken) {
          return newToken;
        }
      }
    }

    // 创建新token
    return await this.createToken();
  }

  /**
   * 使用refresh token刷新access token
   */
  async refreshToken(): Promise<string | null> {
    if (!this.currentRefreshToken) return null;

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: this.currentRefreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.accessToken && data.refreshToken) {
          this.setTokenPair({
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
            expiresAt: data.expiresAt,
          });
          return data.accessToken;
        }
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }

    // 刷新失败，清除token
    this.clearToken();
    return null;
  }

  /**
   * 从localStorage加载tokens
   */
  private loadTokensFromStorage(): void {
    if (typeof window !== 'undefined') {
      this.currentAccessToken = localStorage.getItem(ANONYMOUS_ACCESS_TOKEN_KEY);
      this.currentRefreshToken = localStorage.getItem(ANONYMOUS_REFRESH_TOKEN_KEY);
    }
  }

  /**
   * 验证token是否有效
   */
  private async validateToken(token: string): Promise<boolean> {
    try {
      // 简单的token格式验证
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // 解析payload检查过期时间
      const payload = JSON.parse(atob(parts[1]));
      const now = Math.floor(Date.now() / 1000);

      return payload.exp > now;
    } catch {
      return false;
    }
  }
}

/**
 * 统一的认证头获取函数
 */
export async function getUnifiedAuthHeaders(): Promise<Record<string, string>> {
  const headers: Record<string, string> = {};

  // 检查是否在客户端环境
  if (typeof window !== 'undefined') {
    // 1. 尝试从session获取已登录用户的token
    try {
      const sessionResponse = await fetch('/api/auth/session');
      if (sessionResponse.ok) {
        const session = await sessionResponse.json();
        if (session?.user?.backendToken) {
          // 检查token是否即将过期
          if (isTokenNearExpiry(session.user.backendToken)) {
            // 尝试刷新token
            const refreshedToken = await refreshSessionToken(session.user.refreshToken);
            if (refreshedToken) {
              headers['Authorization'] = `Bearer ${refreshedToken}`;
              return headers;
            }
          } else {
            headers['Authorization'] = `Bearer ${session.user.backendToken}`;
            return headers;
          }
        }
      }
    } catch (error) {
      console.warn('Failed to get session:', error);
    }

    // 2. 如果没有登录用户session，使用匿名用户token
    const anonymousManager = AnonymousTokenManager.getInstance();
    const anonymousToken = await anonymousManager.getOrCreateToken();

    if (anonymousToken) {
      headers['Authorization'] = `Bearer ${anonymousToken}`;
    }
  }

  return headers;
}

/**
 * 检查token是否即将过期（在过期前30分钟）
 */
function isTokenNearExpiry(token: string, minutesBeforeExpiry: number = 30): boolean {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return true;

    const payload = JSON.parse(atob(parts[1]));
    const expiryTime = new Date(payload.exp * 1000);
    const warningTime = new Date(Date.now() + minutesBeforeExpiry * 60 * 1000);

    return expiryTime <= warningTime;
  } catch {
    return true; // 如果无法解析token，认为需要刷新
  }
}

/**
 * 刷新session中的token
 */
async function refreshSessionToken(refreshToken: string): Promise<string | null> {
  if (!refreshToken) return null;

  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.accessToken) {
        // 这里应该更新session，但由于NextAuth的限制，我们只能返回新token
        // 实际应用中可能需要重新登录或使用其他方式更新session
        return data.accessToken;
      }
    }
  } catch (error) {
    console.error('Failed to refresh session token:', error);
  }

  return null;
}

/**
 * 用户登录后清理匿名token
 */
export function clearAnonymousTokenOnLogin(): void {
  const anonymousManager = AnonymousTokenManager.getInstance();
  anonymousManager.clearToken();
}

/**
 * 用户登出后恢复匿名token
 */
export async function restoreAnonymousTokenOnLogout(): Promise<void> {
  const anonymousManager = AnonymousTokenManager.getInstance();
  await anonymousManager.getOrCreateToken();
}

/**
 * 迁移匿名用户数据到注册用户
 */
export async function migrateAnonymousData(): Promise<boolean> {
  const anonymousManager = AnonymousTokenManager.getInstance();
  const anonymousToken = anonymousManager.getToken();

  if (!anonymousToken) return true; // 没有匿名数据需要迁移

  try {
    // 从匿名token中提取anonymousId
    const payload = JSON.parse(atob(anonymousToken.split('.')[1]));
    const anonymousId = payload.anonymousId;

    if (!anonymousId) return true;

    // 调用迁移API
    const response = await fetch(`${API_BASE_URL}/api/anonymous/migrate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${anonymousToken}`,
      },
      body: JSON.stringify({ anonymousId }),
    });

    if (response.ok) {
      // 迁移成功，清理匿名token
      anonymousManager.clearToken();
      return true;
    }
  } catch (error) {
    console.error('Failed to migrate anonymous data:', error);
  }

  return false;
}
