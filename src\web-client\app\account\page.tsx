import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { AccountContent } from '@/components/features/account/account-content';

export const metadata: Metadata = {
  title: '账户设置 - YTDownloader',
  description: '管理您的 YTDownloader 账户设置',
};

export default function AccountPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <AccountContent />
      </main>
      <Footer />
    </div>
  );
}
