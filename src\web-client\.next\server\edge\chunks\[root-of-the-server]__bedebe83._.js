(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push(["chunks/[root-of-the-server]__bedebe83._.js",
"[externals]/node:buffer [external] (node:buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}),
"[project]/lib/auth.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AnonymousTokenManager",
    ()=>AnonymousTokenManager,
    "auth",
    ()=>auth,
    "clearAnonymousTokenOnLogin",
    ()=>clearAnonymousTokenOnLogin,
    "getUnifiedAuthHeaders",
    ()=>getUnifiedAuthHeaders,
    "handlers",
    ()=>handlers,
    "loginSchema",
    ()=>loginSchema,
    "migrateAnonymousData",
    ()=>migrateAnonymousData,
    "restoreAnonymousTokenOnLogout",
    ()=>restoreAnonymousTokenOnLogout,
    "signIn",
    ()=>signIn,
    "signOut",
    ()=>signOut
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [middleware-edge] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [middleware-edge] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/core/providers/credentials.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [middleware-edge] (ecmascript) <export * as z>");
;
;
;
const loginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email('请输入有效的邮箱地址'),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(6, '密码至少需要6个字符')
});
const { handlers, auth, signIn, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])({
    providers: [
        // 配置凭据（邮箱+密码）认证提供者
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: '邮箱',
                    type: 'email'
                },
                password: {
                    label: '密码',
                    type: 'password'
                }
            },
            // 核心授权逻辑：当 signIn('credentials', ...) 被调用时执行
            async authorize (credentials) {
                try {
                    // 1. 使用 Zod 安全地验证输入数据
                    const validatedFields = loginSchema.safeParse(credentials);
                    if (!validatedFields.success) {
                        return null; // 验证失败
                    }
                    const { email, password } = validatedFields.data;
                    // 2. 调用后端 API 进行实际的凭据验证
                    const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5243")}/api/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password
                        })
                    });
                    if (!response.ok) {
                        return null; // 后端验证失败
                    }
                    const data = await response.json();
                    // 3. 如果后端返回了用户信息和JWT token，则认证成功
                    if (data.user && data.accessToken) {
                        // 返回的用户对象将用于创建会话，同时保存后端JWT token
                        return {
                            id: data.user.id,
                            email: data.user.email,
                            name: data.user.username || data.user.email,
                            subscriptionTier: data.user.subscriptionTier,
                            emailVerified: data.user.emailVerified,
                            backendToken: data.accessToken,
                            refreshToken: data.refreshToken,
                            tokenExpiresAt: data.expiresAt
                        };
                    }
                    return null; // 未找到用户或凭据无效
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    // 配置会话管理策略
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    // 配置安全的Cookie设置
    cookies: {
        sessionToken: {
            name: `next-auth.session-token`,
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: ("TURBOPACK compile-time value", "development") === 'production'
            }
        }
    },
    // 回调函数，用于控制认证流程中的行为
    callbacks: {
        // `session` 回调：在每次访问会话时调用，用于将自定义数据从 token 同步到 session.user
        async session ({ session, token }) {
            if (session.user && token) {
                session.user.id = token.sub; // 从 token 的 sub 字段获取用户 ID
                // 将自定义字段从 token 添加到 session 中，使其在客户端可用
                session.user.subscriptionTier = token.subscriptionTier || 'free';
                session.user.emailVerified = token.emailVerified;
                // 将后端JWT token添加到session中，供API调用使用
                session.user.backendToken = token.backendToken;
                session.user.refreshToken = token.refreshToken;
                session.user.tokenExpiresAt = token.tokenExpiresAt;
            }
            return session;
        },
        // `jwt` 回调：在创建或更新 JWT 时调用，用于将自定义数据添加到 token 中
        async jwt ({ token, user }) {
            // 首次登录时（user 对象存在），将后端返回的用户信息添加到 token 中
            if (user) {
                const u = user;
                token.subscriptionTier = u.subscriptionTier;
                token.emailVerified = u.emailVerified;
                token.backendToken = u.backendToken; // 保存后端JWT token
                token.refreshToken = u.refreshToken; // 保存refresh token
                token.tokenExpiresAt = u.tokenExpiresAt; // 保存token过期时间
            }
            return token;
        },
        // `redirect` 回调：控制登录或登出后的重定向行为
        async redirect ({ url, baseUrl }) {
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        }
    },
    // 自定义认证页面
    pages: {
        signIn: '/login',
        error: '/auth/error'
    },
    // 事件监听器，用于记录认证相关的事件
    events: {
        async signIn ({ user, account }) {
            console.log('User signed in:', {
                user: user.email,
                account: account?.provider
            });
        },
        async signOut () {
            console.log('User signed out');
        }
    },
    // 在开发环境中启用调试模式
    debug: ("TURBOPACK compile-time value", "development") === 'development'
});
// ===== 统一认证管理 =====
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5243");
// 匿名用户token的本地存储key
const ANONYMOUS_ACCESS_TOKEN_KEY = 'anonymous_access_token';
const ANONYMOUS_REFRESH_TOKEN_KEY = 'anonymous_refresh_token';
class AnonymousTokenManager {
    static instance;
    currentAccessToken = null;
    currentRefreshToken = null;
    constructor(){
        this.loadTokensFromStorage();
    }
    static getInstance() {
        if (!AnonymousTokenManager.instance) {
            AnonymousTokenManager.instance = new AnonymousTokenManager();
        }
        return AnonymousTokenManager.instance;
    }
    /**
   * 获取当前access token
   */ getToken() {
        return this.currentAccessToken;
    }
    /**
   * 获取当前refresh token
   */ getRefreshToken() {
        return this.currentRefreshToken;
    }
    /**
   * 设置token对
   */ setTokenPair(tokenPair) {
        this.currentAccessToken = tokenPair.accessToken;
        this.currentRefreshToken = tokenPair.refreshToken;
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 设置单个token（向后兼容）
   */ setToken(token) {
        this.currentAccessToken = token;
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 清除所有tokens
   */ clearToken() {
        this.currentAccessToken = null;
        this.currentRefreshToken = null;
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 创建新的匿名用户token
   */ async createToken() {
        try {
            const response = await fetch(`${API_BASE_URL}/api/anonymous/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.accessToken && data.refreshToken) {
                    this.setTokenPair({
                        accessToken: data.accessToken,
                        refreshToken: data.refreshToken,
                        expiresAt: data.expiresAt
                    });
                    return data.accessToken;
                }
                // 向后兼容
                if (data.token) {
                    this.setToken(data.token);
                    return data.token;
                }
            }
        } catch (error) {
            console.error('Failed to create anonymous token:', error);
        }
        return null;
    }
    /**
   * 获取或创建匿名用户token
   */ async getOrCreateToken() {
        if (this.currentAccessToken) {
            // 验证token是否仍然有效
            if (await this.validateToken(this.currentAccessToken)) {
                return this.currentAccessToken;
            }
            // 如果access token过期，尝试使用refresh token刷新
            if (this.currentRefreshToken) {
                const newToken = await this.refreshToken();
                if (newToken) {
                    return newToken;
                }
            }
        }
        // 创建新token
        return await this.createToken();
    }
    /**
   * 使用refresh token刷新access token
   */ async refreshToken() {
        if (!this.currentRefreshToken) return null;
        try {
            const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refreshToken: this.currentRefreshToken
                })
            });
            if (response.ok) {
                const data = await response.json();
                if (data.accessToken && data.refreshToken) {
                    this.setTokenPair({
                        accessToken: data.accessToken,
                        refreshToken: data.refreshToken,
                        expiresAt: data.expiresAt
                    });
                    return data.accessToken;
                }
            }
        } catch (error) {
            console.error('Failed to refresh token:', error);
        }
        // 刷新失败，清除token
        this.clearToken();
        return null;
    }
    /**
   * 从localStorage加载tokens
   */ loadTokensFromStorage() {
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 验证token是否有效
   */ async validateToken(token) {
        try {
            // 简单的token格式验证
            const parts = token.split('.');
            if (parts.length !== 3) return false;
            // 解析payload检查过期时间
            const payload = JSON.parse(atob(parts[1]));
            const now = Math.floor(Date.now() / 1000);
            return payload.exp > now;
        } catch  {
            return false;
        }
    }
}
async function getUnifiedAuthHeaders() {
    const headers = {};
    // 检查是否在客户端环境
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    return headers;
}
/**
 * 检查token是否即将过期（在过期前30分钟）
 */ function isTokenNearExpiry(token, minutesBeforeExpiry = 30) {
    try {
        const parts = token.split('.');
        if (parts.length !== 3) return true;
        const payload = JSON.parse(atob(parts[1]));
        const expiryTime = new Date(payload.exp * 1000);
        const warningTime = new Date(Date.now() + minutesBeforeExpiry * 60 * 1000);
        return expiryTime <= warningTime;
    } catch  {
        return true; // 如果无法解析token，认为需要刷新
    }
}
/**
 * 刷新session中的token
 */ async function refreshSessionToken(refreshToken) {
    if (!refreshToken) return null;
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                refreshToken
            })
        });
        if (response.ok) {
            const data = await response.json();
            if (data.accessToken) {
                // 这里应该更新session，但由于NextAuth的限制，我们只能返回新token
                // 实际应用中可能需要重新登录或使用其他方式更新session
                return data.accessToken;
            }
        }
    } catch (error) {
        console.error('Failed to refresh session token:', error);
    }
    return null;
}
function clearAnonymousTokenOnLogin() {
    const anonymousManager = AnonymousTokenManager.getInstance();
    anonymousManager.clearToken();
}
async function restoreAnonymousTokenOnLogout() {
    const anonymousManager = AnonymousTokenManager.getInstance();
    await anonymousManager.getOrCreateToken();
}
async function migrateAnonymousData() {
    const anonymousManager = AnonymousTokenManager.getInstance();
    const anonymousToken = anonymousManager.getToken();
    if (!anonymousToken) return true; // 没有匿名数据需要迁移
    try {
        // 从匿名token中提取anonymousId
        const payload = JSON.parse(atob(anonymousToken.split('.')[1]));
        const anonymousId = payload.anonymousId;
        if (!anonymousId) return true;
        // 调用迁移API
        const response = await fetch(`${API_BASE_URL}/api/anonymous/migrate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${anonymousToken}`
            },
            body: JSON.stringify({
                anonymousId
            })
        });
        if (response.ok) {
            // 迁移成功，清理匿名token
            anonymousManager.clearToken();
            return true;
        }
    } catch (error) {
        console.error('Failed to migrate anonymous data:', error);
    }
    return false;
}
}),
"[project]/middleware.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "config",
    ()=>config,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/exports/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth.ts [middleware-edge] (ecmascript)");
;
;
// 定义需要登录才能访问的受保护路径
const protectedPaths = [
    '/account',
    '/my-downloads'
];
// 定义登录后不应访问的认证路径
const authPaths = [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password'
];
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["auth"])((req)=>{
    const { nextUrl } = req;
    // 通过检查 req.auth 对象是否存在来判断用户是否登录
    const isLoggedIn = !!req.auth;
    // 检查当前请求是否为受保护路径
    const isProtectedPath = protectedPaths.some((path)=>nextUrl.pathname.startsWith(path));
    // 检查当前请求是否为认证路径
    const isAuthPath = authPaths.some((path)=>nextUrl.pathname.startsWith(path));
    // 如果用户未登录且访问受保护路径，则重定向到登录页并附带原始URL
    if (isProtectedPath && !isLoggedIn) {
        const redirectUrl = new URL('/login', nextUrl.origin);
        redirectUrl.searchParams.set('redirect', nextUrl.pathname);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(redirectUrl);
    }
    // 如果用户已登录且访问认证路径，则重定向到首页
    if (isAuthPath && isLoggedIn) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/', nextUrl.origin));
    }
    // 直接返回响应，不再处理匿名Cookie
    // 匿名用户身份现在通过JWT token在客户端管理
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
});
const config = {
    matcher: [
        // 匹配所有请求路径，但排除 API、Next.js 静态文件、图片、图标和公共资源
        '/((?!api|_next/static|_next/image|favicon.ico|public).*)'
    ]
};
}),
]);

//# sourceMappingURL=%5Broot-of-the-server%5D__bedebe83._.js.map