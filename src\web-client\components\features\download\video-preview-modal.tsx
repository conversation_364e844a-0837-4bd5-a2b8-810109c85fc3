'use client';

import { useEffect } from 'react';

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { VideoPageData } from '@/lib/types';

interface VideoPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoData: VideoPageData;
}

export function VideoPreviewModal({ isOpen, onClose, videoData }: VideoPreviewModalProps) {
  // 构建YouTube嵌入URL
  const embedUrl = `https://www.youtube.com/embed/${videoData.id}?autoplay=0&controls=1&rel=0&modestbranding=1`;

  // 处理键盘事件
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden">
        <VisuallyHidden>
          <DialogTitle>{videoData.title}</DialogTitle>
        </VisuallyHidden>
        <div className="aspect-video bg-black" style={{ maxHeight: '90vh' }}>
          <iframe
            src={embedUrl}
            className="w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={videoData.title}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
