import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

// 定义需要登录才能访问的受保护路径
const protectedPaths = ['/account', '/my-downloads'];

// 定义登录后不应访问的认证路径
const authPaths = ['/login', '/register', '/forgot-password', '/reset-password'];

export default auth((req) => {
  const { nextUrl } = req;
  // 通过检查 req.auth 对象是否存在来判断用户是否登录
  const isLoggedIn = !!req.auth;

  // 检查当前请求是否为受保护路径
  const isProtectedPath = protectedPaths.some((path) => nextUrl.pathname.startsWith(path));

  // 检查当前请求是否为认证路径
  const isAuthPath = authPaths.some((path) => nextUrl.pathname.startsWith(path));

  // 如果用户未登录且访问受保护路径，则重定向到登录页并附带原始URL
  if (isProtectedPath && !isLoggedIn) {
    const redirectUrl = new URL('/login', nextUrl.origin);
    redirectUrl.searchParams.set('redirect', nextUrl.pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // 如果用户已登录且访问认证路径，则重定向到首页
  if (isAuthPath && isLoggedIn) {
    return NextResponse.redirect(new URL('/', nextUrl.origin));
  }

  // 直接返回响应，不再处理匿名Cookie
  // 匿名用户身份现在通过JWT token在客户端管理
  return NextResponse.next();
});

// 配置中间件的执行路径
export const config = {
  matcher: [
    // 匹配所有请求路径，但排除 API、Next.js 静态文件、图片、图标和公共资源
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
