import { notFound } from 'next/navigation';
import type { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { getAllTools, getToolConfig } from '@/lib/tools-config';
import { ToolHeroSection } from '@/components/features/tools/tool-hero-section';
import { ToolFeaturesSection } from '@/components/features/tools/tool-features-section';

import { ToolTipsSection } from '@/components/features/tools/tool-tips-section';
import { ToolQualitiesSection } from '@/components/features/tools/tool-qualities-section';
import { ToolFaqSection } from '@/components/features/tools/tool-faq-section';
import { ToolCtaSection } from '@/components/features/tools/tool-cta-section';

interface ToolPageProps {
  params: Promise<{
    'tool-slug': string;
  }>;
}

export async function generateStaticParams() {
  const tools = getAllTools();
  return tools.map((tool) => ({
    'tool-slug': tool.slug,
  }));
}

export async function generateMetadata({ params }: ToolPageProps): Promise<Metadata> {
  const { 'tool-slug': toolSlug } = await params;
  const toolConfig = getToolConfig(toolSlug);

  if (!toolConfig) {
    return {
      title: 'Tool Not Found',
      description: 'The requested tool could not be found.',
    };
  }

  return {
    title: toolConfig.metaTitle,
    description: toolConfig.metaDescription,
    openGraph: {
      title: toolConfig.metaTitle,
      description: toolConfig.metaDescription,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: toolConfig.metaTitle,
      description: toolConfig.metaDescription,
    },
  };
}

export default async function ToolPage({ params }: ToolPageProps) {
  const { 'tool-slug': toolSlug } = await params;
  const toolConfig = getToolConfig(toolSlug);

  if (!toolConfig) {
    notFound();
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow">
        <ToolHeroSection
          title={toolConfig.title}
          description={toolConfig.description}
          category={toolConfig.category}
          inputPlaceholder={toolConfig.inputPlaceholder}
        />

        <ToolFeaturesSection
          features={toolConfig.features}
          supportedFormats={toolConfig.supportedFormats}
        />

        <ToolTipsSection title={toolConfig.title} category={toolConfig.category} />

        <ToolQualitiesSection category={toolConfig.category} />

        <ToolFaqSection />

        <ToolCtaSection />
      </main>
      <Footer />
    </div>
  );
}
