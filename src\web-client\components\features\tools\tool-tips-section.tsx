import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Lightbulb, Shield, Star, Target, Zap } from 'lucide-react';

interface ToolTipsSectionProps {
  title: string;
  category: string;
}

const getTipsByCategory = (category: string) => {
  const commonTips = [
    {
      icon: <Zap className="h-5 w-5" />,
      title: '提高下载速度',
      description: '选择较低分辨率可以显著提高下载速度，特别是在网络较慢的情况下。',
      tips: ['选择720p而非1080p', '避免高峰时段', '使用有线网络连接'],
      level: '基础',
      color: 'from-yellow-500 to-orange-500',
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: '选择合适格式',
      description: '根据用途选择最适合的格式，可以节省存储空间并提高兼容性。',
      tips: ['视频观看选MP4', '音乐收听选MP3', '专业编辑选高质量格式'],
      level: '进阶',
      color: 'from-blue-500 to-purple-500',
    },
    {
      icon: <Clock className="h-5 w-5" />,
      title: '批量处理技巧',
      description: '处理多个链接时，合理安排顺序可以提高整体效率。',
      tips: ['短视频优先处理', '相同格式批量处理', '分批次处理大量链接'],
      level: '高级',
      color: 'from-green-500 to-teal-500',
    },
  ];

  const categorySpecificTips = {
    downloader: [
      {
        icon: <Shield className="h-5 w-5" />,
        title: '下载质量优化',
        description: '了解不同质量选项的区别，选择最适合您需求的下载质量。',
        tips: ['4K适合大屏观看', '1080p适合一般用途', '720p适合移动设备'],
        level: '基础',
        color: 'from-red-500 to-pink-500',
      },
    ],
    converter: [
      {
        icon: <Star className="h-5 w-5" />,
        title: '转换参数设置',
        description: '正确设置转换参数可以获得更好的输出质量和文件大小平衡。',
        tips: ['音频比特率选择320kbps', '视频编码选择H.264', '根据用途调整分辨率'],
        level: '进阶',
        color: 'from-indigo-500 to-purple-500',
      },
    ],
    tool: [
      {
        icon: <Lightbulb className="h-5 w-5" />,
        title: '工具使用技巧',
        description: '掌握一些小技巧可以让您更高效地使用我们的工具。',
        tips: ['使用键盘快捷键', '收藏常用设置', '定期清理下载文件'],
        level: '实用',
        color: 'from-cyan-500 to-blue-500',
      },
    ],
  };

  return [...commonTips, ...(categorySpecificTips[category] || [])];
};

const getLevelColor = (level: string) => {
  switch (level) {
    case '基础':
      return 'bg-green-100 text-green-800';
    case '进阶':
      return 'bg-blue-100 text-blue-800';
    case '高级':
      return 'bg-purple-100 text-purple-800';
    case '实用':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function ToolTipsSection({ title, category }: ToolTipsSectionProps) {
  const tips = getTipsByCategory(category);

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50/30">
      <div className="container mx-auto px-4">
        {/* 标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-yellow-100 text-yellow-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Lightbulb className="h-4 w-4" />
            使用技巧
          </div>
          <h2 className="text-3xl font-bold mb-4">专业使用技巧</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            掌握这些技巧，让您更高效地使用{title}，获得更好的使用体验
          </p>
        </div>

        {/* 技巧网格 */}
        <div className="grid gap-6 md:grid-cols-2 max-w-5xl mx-auto">
          {tips.map((tip, index) => (
            <div key={index}>
              <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div
                        className={`p-2 bg-gradient-to-r ${tip.color} rounded-lg text-white group-hover:scale-110 transition-transform duration-300`}>
                        {tip.icon}
                      </div>
                      <CardTitle className="text-lg">{tip.title}</CardTitle>
                    </div>
                    <Badge className={getLevelColor(tip.level)}>{tip.level}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">{tip.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700 mb-3">具体建议：</h4>
                    {tip.tips.map((tipItem, tipIndex) => (
                      <div
                        key={tipIndex}
                        className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm text-gray-700 leading-relaxed">{tipItem}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* 底部提示 */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white max-w-3xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Star className="h-6 w-6" />
              <h3 className="text-xl font-bold">专业提示</h3>
            </div>
            <p className="leading-relaxed">
              遇到问题时，请先检查网络连接和链接有效性。大部分问题都可以通过刷新页面或重新粘贴链接来解决。
              如果问题持续存在，请联系我们的技术支持团队。
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
