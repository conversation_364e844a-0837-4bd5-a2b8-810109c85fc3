import { BatchJobDetails } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Calendar, PlayCircle, Users } from 'lucide-react';

interface BatchJobHeaderProps {
  jobData: BatchJobDetails;
}

export function BatchJobHeader({ jobData }: BatchJobHeaderProps) {
  const getSourceTypeLabel = (sourceType: string) => {
    switch (sourceType) {
      case 'PLAYLIST':
        return '播放列表';
      case 'CHANNEL':
        return '频道';
      case 'MULTI_VIDEO_LIST':
        return '自定义任务';
      default:
        return '未知类型';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">待开始</Badge>;
      case 'running':
        return (
          <Badge variant="default" className="bg-blue-500">
            运行中
          </Badge>
        );
      case 'partially_completed':
        return (
          <Badge variant="default" className="bg-orange-500">
            部分完成
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-500">
            已完成
          </Badge>
        );
      case 'failed':
        return <Badge variant="destructive">失败</Badge>;
      case 'cancelled':
        return <Badge variant="outline">已取消</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card className="border-2 border-muted/20 bg-gradient-to-br from-background to-muted/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <PlayCircle className="h-5 w-5 text-primary" />
            任务源信息
          </CardTitle>
          {getStatusBadge(jobData.status)}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <h3 className="text-lg font-semibold line-clamp-2">{jobData.sourceInfo.name}</h3>
            <p className="text-sm text-muted-foreground">
              {getSourceTypeLabel(jobData.sourceType)}
            </p>
          </div>

          {/* 描述 */}
          {jobData.sourceInfo.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {jobData.sourceInfo.description}
            </p>
          )}

          {/* 统计信息 */}
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>共 {jobData.sourceInfo.totalVideos} 个视频</span>
            </div>

            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>创建于 {new Date(jobData.createdAt).toLocaleDateString()}</span>
            </div>

            {jobData.startedAt && (
              <div className="flex items-center gap-1">
                <PlayCircle className="h-4 w-4 text-muted-foreground" />
                <span>开始于 {new Date(jobData.startedAt).toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* 重要提示 */}
          <div className="flex items-start gap-2 rounded-lg bg-muted/50 p-3">
            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-amber-700 dark:text-amber-400">注意事项</p>
              <p className="text-muted-foreground">
                系统最多处理前 1000 个视频。每个用户同时只能进行一个批量任务。
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
