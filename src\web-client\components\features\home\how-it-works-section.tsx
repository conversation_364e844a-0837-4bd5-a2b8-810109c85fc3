import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowDownToLine, CheckCircle, Copy, Settings, Zap } from 'lucide-react';

const steps = [
  {
    icon: <Copy className="h-10 w-10" />,
    title: '粘贴链接',
    description: '将任何 YouTube 视频、播放列表或频道链接粘贴到输入框中。',
    time: '< 5秒',
    color: 'from-blue-500 to-cyan-500',
    features: ['支持所有链接类型', '自动识别格式', '批量粘贴'],
  },
  {
    icon: <Settings className="h-10 w-10" />,
    title: '配置选项',
    description: '选择你需要的格式、分辨率、字幕语言或其他高级处理选项。',
    time: '< 10秒',
    color: 'from-purple-500 to-pink-500',
    features: ['多种格式选择', '质量自定义', '高级设置'],
  },
  {
    icon: <ArrowDownToLine className="h-10 w-10" />,
    title: '开始下载',
    description: '点击下载按钮，获取你的内容。批量任务将在后台自动完成。',
    time: '即时开始',
    color: 'from-green-500 to-emerald-500',
    features: ['极速处理', '后台下载', '进度跟踪'],
  },
];

export function HowItWorksSection() {
  return (
    <section
      id="how-it-works"
      className="relative bg-gradient-to-br from-muted/30 via-background to-muted/30 py-20">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />

      <div className="container relative mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold md:text-4xl mb-4">三步轻松下载</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            简单直观的操作流程，让您在几分钟内完成任何下载任务
          </p>
        </div>

        <div className="mx-auto grid max-w-6xl grid-cols-1 items-start gap-8 md:grid-cols-3 md:gap-12">
          {steps.map((step, index) => (
            <div key={step.title} className="relative flex flex-col items-center">
              <div className="transform transition-all duration-300 hover:scale-105">
                <Card className="group w-full border-0 bg-background/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-500">
                  <CardContent className="p-8">
                    {/* 步骤编号 */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-8 h-8 rounded-full bg-gradient-to-r ${step.color} text-white text-sm font-bold flex items-center justify-center`}>
                          {index + 1}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {step.time}
                        </Badge>
                      </div>
                      <div
                        className={`p-3 rounded-xl bg-gradient-to-br ${step.color} text-white shadow-lg transition-transform duration-200 group-hover:scale-110 group-hover:rotate-6`}>
                        {step.icon}
                      </div>
                    </div>

                    <h3 className="text-xl font-bold mb-3">{step.title}</h3>
                    <p className="text-muted-foreground mb-6 leading-relaxed">{step.description}</p>

                    {/* 功能特点 */}
                    <div className="space-y-2">
                      {step.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span className="text-muted-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 连接箭头 */}
              {index < steps.length - 1 && (
                <div className="absolute right-0 top-1/2 hidden translate-x-1/2 transform md:block z-10">
                  <div className="flex items-center justify-center w-12 h-12 bg-background rounded-full shadow-lg border hover:shadow-xl transition-shadow duration-300">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 底部提示 */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full">
            <CheckCircle className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">平均处理时间：30秒内完成</span>
          </div>
        </div>
      </div>
    </section>
  );
}
