'use client';

import Link from 'next/link';
import { BatchJobDetails } from '@/lib/types';
import { Batch<PERSON><PERSON><PERSON>eader } from './batch-job-header';
import { BatchJobConfig } from './batch-job-config';
import { VideoListSection } from './video-list-section';
import { BatchJobProgress } from './batch-job-progress';
import { useBatchJob } from '@/hooks/use-batch-job';
import { useWebSocket } from '@/hooks/use-websocket';

interface BatchJobContentProps {
  batchJobData: BatchJobDetails;
}

export function BatchJobContent({ batchJobData }: BatchJobContentProps) {
  const {
    jobData,
    isLoading,
    error,
    updateJobData,
    updateVideoData,
    refreshJobData,
    isRealTimeActive,
    toggleRealTime,
  } = useBatchJob({
    batchJobId: batchJobData.id,
    initialData: batchJobData,
    enableRealTimeUpdates: true,
  });

  // WebSocket连接用于实时更新
  const { isConnected, sendMessage } = useWebSocket({
    url: `${process.env.NEXT_PUBLIC_WS_URL}/batch-jobs/${batchJobData.id}`,
    onMessage: (message) => {
      switch (message.type) {
        case 'JOB_STATUS_UPDATE':
          updateJobData(message.payload);
          break;
        case 'VIDEO_STATUS_UPDATE':
          updateVideoData(message.payload.videoId, message.payload.updates);
          break;
        case 'PROGRESS_UPDATE':
          updateJobData({ progress: message.payload.progress });
          break;
      }
    },
    enabled: jobData?.status === 'running' || jobData?.status === 'partially_completed',
  });

  const isJobStarted =
    jobData?.status === 'running' ||
    jobData?.status === 'partially_completed' ||
    jobData?.status === 'completed';

  const handleJobStart = () => {
    updateJobData({
      status: 'running',
      startedAt: new Date().toISOString(),
    });
  };

  // 如果数据加载失败，显示错误信息
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-destructive">{error}</p>
          <button onClick={refreshJobData} className="mt-4 underline">
            重试
          </button>
        </div>
      </div>
    );
  }

  // 如果没有数据，显示加载状态
  if (!jobData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 页面头部信息条 */}
      <div className="border-b bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-primary transition-colors">
              首页
            </Link>
            <span>/</span>
            <span>批量下载</span>
            <span>/</span>
            <span className="text-foreground font-medium truncate max-w-xs">
              {jobData.sourceInfo.name}
            </span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* 实时进度监控 (仅在任务运行时显示) */}
        {isJobStarted && (
          <BatchJobProgress
            jobData={jobData}
            onJobUpdate={updateJobData}
            isConnected={isConnected}
            isRealTimeActive={isRealTimeActive}
            onToggleRealTime={toggleRealTime}
          />
        )}

        <div className="grid grid-cols-1 gap-12 lg:grid-cols-10">
          {/* 左侧区域: 任务源信息和全局配置 */}
          <aside className="lg:col-span-3">
            <div className="sticky top-24 space-y-6">
              {/* 任务源信息 */}
              <BatchJobHeader jobData={jobData} />

              {/* 全局配置 */}
              <BatchJobConfig
                jobData={jobData}
                isJobStarted={isJobStarted}
                onJobStart={handleJobStart}
                onJobUpdate={updateJobData}
              />
            </div>
          </aside>

          {/* 右侧区域: 视频列表 */}
          <main className="lg:col-span-7">
            <div className="space-y-6">
              {/* 视频列表区 */}
              <VideoListSection
                jobData={jobData}
                isJobStarted={isJobStarted}
                onJobUpdate={updateJobData}
                onVideoUpdate={updateVideoData}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
}
