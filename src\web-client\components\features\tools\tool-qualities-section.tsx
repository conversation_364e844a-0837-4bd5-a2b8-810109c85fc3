import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { HardDrive, Headphones, Monitor, Smartphone, Star, Wifi } from 'lucide-react';

interface ToolQualitiesSectionProps {
  category: string;
}

const videoQualities = [
  {
    name: '4K Ultra HD',
    resolution: '3840×2160',
    fileSize: '~100-200MB/分钟',
    quality: 95,
    icon: <Monitor className="h-5 w-5" />,
    description: '最高画质，适合大屏幕观看和专业用途',
    pros: ['极致画质', '细节丰富', '专业级别'],
    cons: ['文件较大', '下载较慢', '占用存储多'],
    recommended: '大屏电视、专业编辑',
    badge: '最高',
    badgeColor: 'bg-purple-100 text-purple-800',
  },
  {
    name: '1080p Full HD',
    resolution: '1920×1080',
    fileSize: '~50-100MB/分钟',
    quality: 85,
    icon: <Monitor className="h-5 w-5" />,
    description: '高清画质，日常观看的最佳选择',
    pros: ['画质清晰', '文件适中', '兼容性好'],
    cons: ['比4K略差', '仍需较好网络'],
    recommended: '电脑、平板观看',
    badge: '推荐',
    badgeColor: 'bg-green-100 text-green-800',
  },
  {
    name: '720p HD',
    resolution: '1280×720',
    fileSize: '~25-50MB/分钟',
    quality: 70,
    icon: <Smartphone className="h-5 w-5" />,
    description: '标准高清，移动设备的理想选择',
    pros: ['下载快速', '节省流量', '兼容性强'],
    cons: ['画质一般', '大屏效果差'],
    recommended: '手机、移动观看',
    badge: '平衡',
    badgeColor: 'bg-blue-100 text-blue-800',
  },
  {
    name: '480p SD',
    resolution: '854×480',
    fileSize: '~10-25MB/分钟',
    quality: 50,
    icon: <Wifi className="h-5 w-5" />,
    description: '标准画质，网络较慢时的选择',
    pros: ['文件小', '下载极快', '节省空间'],
    cons: ['画质较差', '细节缺失'],
    recommended: '网络较慢、预览用途',
    badge: '节省',
    badgeColor: 'bg-orange-100 text-orange-800',
  },
];

const audioQualities = [
  {
    name: '320kbps',
    quality: '高品质',
    fileSize: '~2.4MB/分钟',
    description: '接近CD音质，音乐爱好者首选',
    icon: <Headphones className="h-5 w-5" />,
    badge: '最佳',
    badgeColor: 'bg-purple-100 text-purple-800',
  },
  {
    name: '192kbps',
    quality: '标准品质',
    fileSize: '~1.4MB/分钟',
    description: '平衡音质与文件大小的理想选择',
    icon: <Headphones className="h-5 w-5" />,
    badge: '推荐',
    badgeColor: 'bg-green-100 text-green-800',
  },
  {
    name: '128kbps',
    quality: '基础品质',
    fileSize: '~1MB/分钟',
    description: '适合语音内容和网络较慢的情况',
    icon: <Headphones className="h-5 w-5" />,
    badge: '节省',
    badgeColor: 'bg-blue-100 text-blue-800',
  },
];

export function ToolQualitiesSection({ category }: ToolQualitiesSectionProps) {
  const showVideoQualities = category === 'downloader' || category === 'converter';
  const showAudioQualities = category === 'downloader' || category === 'converter';

  return (
    <section className="py-16 bg-gradient-to-br from-slate-50 to-gray-50">
      <div className="container mx-auto px-4">
        {/* 标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Star className="h-4 w-4" />
            质量选项
          </div>
          <h2 className="text-3xl font-bold mb-4">质量选择指南</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            了解不同质量选项的特点，根据您的需求选择最合适的下载质量
          </p>
        </div>

        {/* 视频质量选项 */}
        {showVideoQualities && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-center mb-8">视频质量选项</h3>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 max-w-7xl mx-auto">
              {videoQualities.map((quality, index) => (
                <div key={index}>
                  <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white">
                            {quality.icon}
                          </div>
                          <Badge className={quality.badgeColor}>{quality.badge}</Badge>
                        </div>
                      </div>
                      <CardTitle className="text-lg mb-2">{quality.name}</CardTitle>
                      <p className="text-sm text-muted-foreground mb-3">{quality.description}</p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>画质评分</span>
                          <span className="font-medium">{quality.quality}%</span>
                        </div>
                        <Progress value={quality.quality} className="h-2" />
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">分辨率:</span>
                          <span className="font-medium">{quality.resolution}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">文件大小:</span>
                          <span className="font-medium">{quality.fileSize}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">推荐用途:</span>
                          <span className="font-medium text-right">{quality.recommended}</span>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-green-700 mb-2">优点:</h5>
                          <ul className="text-xs space-y-1">
                            {quality.pros.map((pro, proIndex) => (
                              <li key={proIndex} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                                {pro}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-orange-700 mb-2">注意:</h5>
                          <ul className="text-xs space-y-1">
                            {quality.cons.map((con, conIndex) => (
                              <li key={conIndex} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                                {con}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频质量选项 */}
        {showAudioQualities && (
          <div>
            <h3 className="text-2xl font-bold text-center mb-8">音频质量选项</h3>
            <div className="grid gap-6 md:grid-cols-3 max-w-4xl mx-auto">
              {audioQualities.map((quality, index) => (
                <div key={index}>
                  <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="p-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-white">
                            {quality.icon}
                          </div>
                          <Badge className={quality.badgeColor}>{quality.badge}</Badge>
                        </div>
                      </div>
                      <CardTitle className="text-lg mb-2">{quality.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{quality.description}</p>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">音质等级:</span>
                          <span className="font-medium">{quality.quality}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">文件大小:</span>
                          <span className="font-medium">{quality.fileSize}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 选择建议 */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-6 text-white max-w-4xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-4">
              <HardDrive className="h-6 w-6" />
              <h3 className="text-xl font-bold">选择建议</h3>
            </div>
            <div className="grid gap-4 md:grid-cols-3 text-sm">
              <div className="bg-white/10 rounded-lg p-4">
                <h4 className="font-semibold mb-2">日常观看</h4>
                <p>推荐1080p视频 + 192kbps音频，平衡质量与文件大小</p>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <h4 className="font-semibold mb-2">移动设备</h4>
                <p>推荐720p视频 + 128kbps音频，节省流量和存储空间</p>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <h4 className="font-semibold mb-2">专业用途</h4>
                <p>推荐4K视频 + 320kbps音频，获得最佳质量效果</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
