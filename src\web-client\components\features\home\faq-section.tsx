import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

const faqs = [
  {
    question: '如何下载 YouTube 播放列表中的所有视频？',
    answer:
      '只需将播放列表的链接粘贴到主页的输入框中，点击搜索。系统会自动识别并引导你进入批量下载页面，你可以在那里选择要下载的视频和全局配置。',
  },
  {
    question: '下载视频是免费的吗？',
    answer:
      '是的，我们提供强大的免费服务。免费版支持下载最高 1080p 的视频、192kbps 的音频，以及批量处理最多 20 个视频。对于需要更高质量或更大规模下载的用户，我们提供专业的 Pro 版本。',
  },
  {
    question: '我可以下载受年龄限制或私有的视频吗？',
    answer:
      '不可以。本平台严格遵守 YouTube 的服务条款，无法访问或下载任何私有视频或需要登录才能观看的受限内容。',
  },
  {
    question: '下载的文件安全吗？',
    answer:
      '绝对安全。所有下载处理都在我们的服务器上完成，你通过安全的签名链接获取文件。我们不会存储你的文件，临时文件也会在固定时间后被自动删除。',
  },
  {
    question: '支持哪些操作系统？',
    answer:
      'YTDownloader 是一个网页应用，您无需安装任何软件。只要有现代的网页浏览器（如 Chrome, Firefox, Safari, Edge），就可以在 Windows, macOS, Linux, Android 和 iOS 等任何操作系统上使用我们的服务。',
  },
  {
    question: '如果下载失败或中断了怎么办？',
    answer:
      '请首先检查您的网络连接和输入的链接是否正确。如果问题仍然存在，您可以尝试清除浏览器缓存或更换浏览器。对于批量任务，我们会记录每个视频的下载状态，您可以方便地对失败的任务进行重试。如果需要进一步帮助，请随时联系我们的客服。',
  },
  {
    question: 'Pro 版本和免费版有什么具体区别？',
    answer:
      'Pro 版本专为有更高要求的用户设计，提供无限制的批量处理数量、更快的下载速度、4K/8K 分辨率支持、更高比特率的音频提取以及优先的客户支持服务。',
  },
  {
    question: '我可以将下载的内容用于商业用途吗？',
    answer:
      '不可以。根据我们的服务条款，所有通过本平台下载的内容仅限用于个人学习、研究或存档等非商业目的。您必须遵守 YouTube 的版权政策和您所在地的法律法规，任何因滥用下载内容而导致的版权纠纷需由您个人承担。',
  },
];

export function FaqSection() {
  return (
    <section id="faq" className="bg-gradient-to-br from-muted/30 via-background to-muted/30 py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold md:text-4xl mb-4">常见问题解答</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            快速找到您关心的问题答案，如有其他疑问请随时联系我们
          </p>
        </div>

        <div className="mx-auto max-w-4xl">
          <Accordion type="single" collapsible className="w-full space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="border-0 bg-background/80 backdrop-blur-sm rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                <AccordionTrigger className="px-6 py-4 text-base text-left hover:no-underline">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4 text-base text-muted-foreground leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
