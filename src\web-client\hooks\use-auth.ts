'use client';

import { signIn, signOut, useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { AuthErrorCode, RetryHandler } from '@/lib/errors';
import {
  AnonymousTokenManager,
  clearAnonymousTokenOnLogin,
  migrateAnonymousData,
  restoreAnonymousTokenOnLogout,
} from '@/lib/auth';

export function useAuth() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const { handleAsyncError } = useErrorHandler();

  // 统一认证状态管理
  const [anonymousToken, setAnonymousToken] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  const isLoading = status === 'loading' || isInitializing;
  const isAuthenticated = status === 'authenticated';
  const user = session?.user;

  // 初始化匿名token
  useEffect(() => {
    const initializeAnonymousToken = async () => {
      if (!isAuthenticated) {
        const manager = AnonymousTokenManager.getInstance();
        const token = await manager.getOrCreateToken();
        setAnonymousToken(token);
      }
      setIsInitializing(false);
    };

    initializeAnonymousToken();
  }, [isAuthenticated]);

  const login = useCallback(
    async (email: string, password: string, redirectTo?: string) => {
      const result = await handleAsyncError(
        async () => {
          const signInResult = await RetryHandler.withRetry(async () => {
            return await signIn('credentials', {
              email,
              password,
              redirect: false,
            });
          });

          if (signInResult?.error) {
            // 根据错误类型抛出相应的错误
            if (signInResult.error === 'CredentialsSignin') {
              throw { error: 'INVALID_CREDENTIALS' };
            } else {
              throw { error: signInResult.error };
            }
          }

          if (signInResult?.ok) {
            // 登录成功后的处理
            try {
              // 1. 迁移匿名用户数据
              await migrateAnonymousData();

              // 2. 清理匿名token
              clearAnonymousTokenOnLogin();
            } catch (error) {
              console.warn('Failed to migrate anonymous data:', error);
            }

            toast({
              title: '登录成功',
              description: `欢迎回来！`,
            });

            // 重定向到指定页面或首页
            router.push(redirectTo || '/');
            return true;
          }

          throw { error: 'UNKNOWN_ERROR' };
        },
        {
          onError: (error) => {
            // 特殊处理会话过期错误
            if (error.code === AuthErrorCode.SESSION_EXPIRED) {
              router.push('/login');
            }
          },
        },
      );

      return result !== null;
    },
    [router, toast, handleAsyncError],
  );

  const logout = useCallback(async () => {
    await handleAsyncError(
      async () => {
        await signOut({ redirect: false });

        // 登出后恢复匿名token
        try {
          await restoreAnonymousTokenOnLogout();
        } catch (error) {
          console.warn('Failed to restore anonymous token:', error);
        }

        toast({
          title: '已退出登录',
          description: '您已成功退出登录',
        });

        router.push('/');
        return true;
      },
      {
        customMessage: '退出登录时发生错误，请刷新页面重试',
      },
    );
  }, [router, toast, handleAsyncError]);

  const register = useCallback(
    async (data: { email: string; password: string; username?: string; anonymousId?: string }) => {
      const result = await handleAsyncError(async () => {
        const response = await RetryHandler.withRetry(async () => {
          return await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/register`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });
        });

        const responseData = await response.json();

        if (!response.ok) {
          // 抛出带有错误代码的错误
          throw {
            error: responseData.error || 'UNKNOWN_ERROR',
            response: { status: response.status, data: responseData },
          };
        }

        // 注册成功后自动登录
        const loginSuccess = await login(data.email, data.password);

        if (loginSuccess) {
          toast({
            title: '注册成功',
            description: '欢迎加入！您已自动登录',
          });

          // 如果有数据迁移信息，显示迁移结果
          if (responseData.migration) {
            const { migratedTasksCount, migratedBatchJobsCount } = responseData.migration;
            if (migratedTasksCount > 0 || migratedBatchJobsCount > 0) {
              toast({
                title: '数据迁移成功',
                description: `已迁移 ${migratedTasksCount} 个任务和 ${migratedBatchJobsCount} 个批量作业`,
              });
            }
          }
        }

        return loginSuccess;
      });

      return result !== null;
    },
    [login, toast, handleAsyncError],
  );

  const refreshSession = useCallback(async () => {
    await handleAsyncError(
      async () => {
        await update();
        return true;
      },
      {
        showToast: false, // 会话刷新失败不显示提示
        logError: true,
      },
    );
  }, [update, handleAsyncError]);

  // 统一认证方法
  const getCurrentToken = useCallback(() => {
    if (isAuthenticated && session?.user?.backendToken) {
      return session.user.backendToken;
    }
    return anonymousToken;
  }, [isAuthenticated, session?.user?.backendToken, anonymousToken]);

  const getUserType = useCallback(() => {
    if (isAuthenticated) return 'registered';
    if (anonymousToken) return 'anonymous';
    return 'none';
  }, [isAuthenticated, anonymousToken]);

  const createAnonymousToken = useCallback(async () => {
    if (isAuthenticated) return null;

    const manager = AnonymousTokenManager.getInstance();
    const token = await manager.createToken();
    setAnonymousToken(token);
    return token;
  }, [isAuthenticated]);

  const clearAnonymousToken = useCallback(() => {
    const manager = AnonymousTokenManager.getInstance();
    manager.clearToken();
    setAnonymousToken(null);
  }, []);

  const refreshAnonymousToken = useCallback(async () => {
    if (isAuthenticated) return null;

    const manager = AnonymousTokenManager.getInstance();
    const token = await manager.getOrCreateToken();
    setAnonymousToken(token);
    return token;
  }, [isAuthenticated]);

  const getAuthHeaders = useCallback(() => {
    const token = getCurrentToken();
    if (token) {
      return { Authorization: `Bearer ${token}` };
    }
    return {};
  }, [getCurrentToken]);

  const migrateData = useCallback(async () => {
    if (!anonymousToken) return { success: true };

    const result = await handleAsyncError(
      async () => {
        const success = await migrateAnonymousData();
        if (success) {
          clearAnonymousToken();
          return { success: true };
        }
        throw new Error('Migration failed');
      },
      {
        customMessage: '数据迁移失败，您的历史数据可能无法保留',
      },
    );

    return result || { success: false };
  }, [anonymousToken, handleAsyncError, clearAnonymousToken]);

  const hasValidAuth = useCallback(() => {
    return !!(isAuthenticated || anonymousToken);
  }, [isAuthenticated, anonymousToken]);

  return {
    // 基础认证状态
    user,
    isLoading,
    isAuthenticated,
    session,

    // 统一认证状态
    anonymousToken,
    userType: getUserType(),
    hasValidAuth: hasValidAuth(),

    // 基础认证方法
    login,
    logout,
    register,
    refreshSession,

    // 统一认证方法
    getCurrentToken,
    getAuthHeaders,
    createAnonymousToken,
    clearAnonymousToken,
    refreshAnonymousToken,
    migrateData,

    // 向后兼容性方法（用于替换旧的useAnonymousUser和useUnifiedAuth）
    anonymousId: anonymousToken ? 'unified-jwt-token' : null,
    createAnonymousUser: createAnonymousToken,
    migrateAnonymousData: migrateData,
    clearAnonymousCookie: clearAnonymousToken,
    ensureAnonymousUser: refreshAnonymousToken,
  };
}

// 向后兼容的导出
export const useUnifiedAuth = useAuth;

export function useAnonymousUser() {
  const auth = useAuth();

  return {
    anonymousId: auth.anonymousToken ? 'jwt-token-present' : null,
    isLoading: auth.isLoading,
    createAnonymousUser: auth.createAnonymousToken,
    migrateAnonymousData: auth.migrateData,
    clearAnonymousCookie: auth.clearAnonymousToken,
    ensureAnonymousUser: auth.refreshAnonymousToken,
  };
}
