'use client';

import { motion } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProgressStep {
  id: string;
  label: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep?: string;
  progress?: number;
  className?: string;
}

export function ProgressIndicator({
  steps,
  currentStep,
  progress = 0,
  className,
}: ProgressIndicatorProps) {
  const currentStepIndex = currentStep ? steps.findIndex((step) => step.id === currentStep) : -1;
  const overallProgress =
    currentStepIndex >= 0 ? ((currentStepIndex + 1) / steps.length) * 100 : progress;

  const getStepIcon = (step: ProgressStep, index: number) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return (
          <div
            className={cn(
              'h-4 w-4 rounded-full border-2',
              index <= currentStepIndex ? 'border-blue-600 bg-blue-600' : 'border-gray-300',
            )}
          />
        );
    }
  };

  const getStepStatus = (step: ProgressStep, index: number) => {
    if (step.status !== 'pending') return step.status;
    if (index < currentStepIndex) return 'completed';
    if (index === currentStepIndex) return 'processing';
    return 'pending';
  };

  return (
    <motion.div
      className={cn('w-full space-y-4', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}>
      {/* 总体进度条 */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="font-medium">处理进度</span>
          <span className="text-muted-foreground">{Math.round(overallProgress)}%</span>
        </div>
        <Progress value={overallProgress} className="h-2" />
      </div>

      {/* 步骤列表 - 简化为一行显示 */}
      <div className="flex items-center justify-center gap-4">
        {steps.map((step, index) => {
          const status = getStepStatus(step, index);
          const isActive = index === currentStepIndex;

          return (
            <motion.div
              key={step.id}
              className="flex items-center gap-2"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}>
              {/* 步骤图标 */}
              <div className="flex-shrink-0">{getStepIcon(step, index)}</div>

              {/* 步骤标签 */}
              <span
                className={cn(
                  'text-sm font-medium',
                  status === 'completed' && 'text-green-700',
                  status === 'processing' && 'text-blue-700',
                  status === 'error' && 'text-red-700',
                  status === 'pending' && 'text-gray-600',
                )}>
                {step.label}
              </span>

              {/* 连接线 */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    'w-8 h-0.5 mx-2',
                    index < currentStepIndex ? 'bg-green-500' : 'bg-gray-300',
                  )}
                />
              )}
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
}

// 预定义的处理步骤
export const defaultProcessingSteps: ProgressStep[] = [
  {
    id: 'validate',
    label: '验证链接格式',
    status: 'pending',
  },
  {
    id: 'analyze',
    label: '解析视频信息',
    status: 'pending',
  },
  {
    id: 'fetch',
    label: '获取下载选项',
    status: 'pending',
  },
  {
    id: 'redirect',
    label: '跳转到下载页面',
    status: 'pending',
  },
];

export const batchProcessingSteps: ProgressStep[] = [
  {
    id: 'validate',
    label: '验证批量链接',
    status: 'pending',
  },
  {
    id: 'create',
    label: '创建批量任务',
    status: 'pending',
  },
  {
    id: 'process',
    label: '处理视频列表',
    status: 'pending',
  },
  {
    id: 'redirect',
    label: '跳转到批量页面',
    status: 'pending',
  },
];
