'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, ChevronDown, ChevronUp, Copy, Download, FileText } from 'lucide-react';

interface VideoDescriptionProps {
  description: string;
}

export function VideoDescription({ description }: VideoDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const handleCopyDescription = async () => {
    try {
      await navigator.clipboard.writeText(description);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy description:', error);
    }
  };

  const handleDownloadDescription = () => {
    const blob = new Blob([description], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'video-description.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const wordCount = description.split(/\s+/).length;
  const charCount = description.length;

  return (
    <Card className="border-2 border-muted/20 bg-gradient-to-br from-background to-muted/20">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-base">
          <FileText className="h-4 w-4 text-primary" />
          视频描述
          <div className="flex gap-1 ml-auto">
            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
              {wordCount} 词
            </Badge>
            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
              {charCount} 字符
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="relative">
          <motion.div
            className="text-sm leading-tight"
            initial={false}
            animate={{ height: isExpanded ? 'auto' : '2rem' }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            style={{ overflow: 'hidden' }}>
            <p className="whitespace-pre-wrap text-muted-foreground">{description}</p>
          </motion.div>

          {!isExpanded && (
            <div className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-t from-background to-transparent" />
          )}
        </div>

        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-primary hover:text-primary/80 h-7 px-2 text-xs">
            {isExpanded ? (
              <>
                <ChevronUp className="mr-1 h-3 w-3" />
                收起
              </>
            ) : (
              <>
                <ChevronDown className="mr-1 h-3 w-3" />
                展开
              </>
            )}
          </Button>
        </div>

        <div className="flex gap-2 pt-1 border-t">
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="flex-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyDescription}
              disabled={isCopied}
              className="w-full">
              <AnimatePresence mode="wait">
                {isCopied ? (
                  <motion.div
                    key="copied"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-600" />
                    已复制
                  </motion.div>
                ) : (
                  <motion.div
                    key="copy"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center">
                    <Copy className="mr-2 h-4 w-4" />
                    复制描述
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="flex-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadDescription}
              className="w-full">
              <Download className="mr-2 h-4 w-4" />
              下载 TXT
            </Button>
          </motion.div>
        </div>
      </CardContent>
    </Card>
  );
}
