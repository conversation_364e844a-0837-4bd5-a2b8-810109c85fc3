import {
  BatchJobDetails,
  ChangePasswordRequest,
  CreateBatchJobRequest,
  CreateBatchJobResponse,
  DeleteAccountRequest,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  ResetPasswordRequest,
  StartBatchJobRequest,
  Subscription,
  TaskHistoryItem,
  UpdateUserRequest,
  User,
  VideoPageData,
} from '@/lib/types';
import { getUnifiedAuthHeaders } from '@/lib/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL!;

// 通用 API 请求函数，处理所有 HTTP 请求的统一入口
export async function fetchApi<T>(endpoint: string, options?: RequestInit): Promise<T | null> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const headers = new Headers(options?.headers);
    headers.set('Content-Type', 'application/json');

    // 统一使用Bearer Token认证方式
    const authHeaders = await getUnifiedAuthHeaders();
    Object.entries(authHeaders).forEach(([key, value]) => {
      headers.set(key, value);
    });

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      console.error(`API Error: ${response.status} ${response.statusText} for URL: ${url}`);
      return null;
    }

    return response.json() as Promise<T>;
  } catch (error) {
    console.error(`Network or fetch error for URL: ${url}`, error);
    return null;
  }
}

// 获取视频元数据，包括可用的下载流、字幕等
export async function getVideoData(videoId: string): Promise<VideoPageData | null> {
  return fetchApi<VideoPageData>(`/youtube/metadata?videoId=${videoId}`, {
    next: { tags: ['video-metadata', videoId] },
  });
}

// 创建批量作业，根据播放列表、频道或视频列表创建新的批量下载作业
export async function createBatchJob(
  request: CreateBatchJobRequest,
): Promise<CreateBatchJobResponse | null> {
  return fetchApi<CreateBatchJobResponse>('/batch-jobs', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 获取批量作业详情，包括视频列表、进度状态等
export async function getBatchJobDetails(batchJobId: string): Promise<BatchJobDetails | null> {
  return fetchApi<BatchJobDetails>(`/batch-jobs/${batchJobId}`, {
    next: { tags: ['batch-job', batchJobId] },
  });
}

// 启动批量作业，开始执行批量下载任务
export async function startBatchJob(
  batchJobId: string,
  request: StartBatchJobRequest,
): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>(`/batch-jobs/${batchJobId}/start`, {
    method: 'POST',
    body: JSON.stringify(request),
  });
  return response?.success ?? false;
}

// 暂停批量作业
export async function pauseBatchJob(batchJobId: string): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>(`/batch-jobs/${batchJobId}/pause`, {
    method: 'POST',
  });
  return response?.success ?? false;
}

// 取消批量作业，停止所有未完成的视频处理
export async function cancelBatchJob(batchJobId: string): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>(`/batch-jobs/${batchJobId}/cancel`, {
    method: 'POST',
  });
  return response?.success ?? false;
}

// 重试失败的视频
export async function retryFailedVideo(batchJobId: string, videoId: string): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>(
    `/batch-jobs/${batchJobId}/videos/${videoId}/retry`,
    {
      method: 'POST',
    },
  );
  return response?.success ?? false;
}

// 取消单个视频的处理
export async function cancelVideoProcessing(batchJobId: string, videoId: string): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>(
    `/batch-jobs/${batchJobId}/videos/${videoId}/cancel`,
    {
      method: 'POST',
    },
  );
  return response?.success ?? false;
}

// 用户登录，使用邮箱和密码进行身份验证
export async function login(request: LoginRequest): Promise<LoginResponse | null> {
  return fetchApi<LoginResponse>('/auth/login', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 用户注册，创建新的用户账户
export async function register(request: RegisterRequest): Promise<RegisterResponse | null> {
  return fetchApi<RegisterResponse>('/auth/register', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 用户登出，注销当前用户会话
export async function logout(): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/auth/logout', {
    method: 'POST',
  });
  return response?.success ?? false;
}

// 获取当前用户信息
export async function getCurrentUser(): Promise<User | null> {
  return fetchApi<User>('/auth/me', {
    next: { tags: ['current-user'] },
  });
}

// 刷新访问令牌，延长会话有效期
export async function refreshToken(): Promise<LoginResponse | null> {
  return fetchApi<LoginResponse>('/auth/refresh', {
    method: 'POST',
  });
}

// 忘记密码，发送密码重置邮件到用户邮箱
export async function forgotPassword(
  request: ForgotPasswordRequest,
): Promise<ForgotPasswordResponse | null> {
  return fetchApi<ForgotPasswordResponse>('/auth/forgot-password', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 重置密码，使用重置令牌设置新密码
export async function resetPassword(request: ResetPasswordRequest): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/auth/reset-password', {
    method: 'POST',
    body: JSON.stringify(request),
  });
  return response?.success ?? false;
}

// 更新用户信息，如用户名、头像等
export async function updateUser(request: UpdateUserRequest): Promise<User | null> {
  return fetchApi<User>('/user/profile', {
    method: 'PUT',
    body: JSON.stringify(request),
  });
}

// 修改密码，需要提供当前密码验证
export async function changePassword(request: ChangePasswordRequest): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/user/change-password', {
    method: 'POST',
    body: JSON.stringify(request),
  });
  return response?.success ?? false;
}

// 获取用户订阅信息，包括订阅状态、等级和付费历史
export async function getUserSubscription(): Promise<Subscription | null> {
  return fetchApi<Subscription>('/user/subscription', {
    next: { tags: ['user-subscription'] },
  });
}

// 获取用户任务历史，分页获取下载任务历史记录
export async function getUserTaskHistory(
  page: number = 1,
  limit: number = 20,
): Promise<{
  items: TaskHistoryItem[];
  total: number;
  page: number;
  limit: number;
} | null> {
  return fetchApi<{
    items: TaskHistoryItem[];
    total: number;
    page: number;
    limit: number;
  }>(`/user/task-history?page=${page}&limit=${limit}`, {
    next: { tags: ['user-task-history'] },
  });
}

// 发送邮箱验证邮件
export async function sendEmailVerification(): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/user/send-email-verification', {
    method: 'POST',
  });
  return response?.success ?? false;
}

// 验证邮箱，使用验证令牌完成邮箱地址验证
export async function verifyEmail(token: string): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/user/verify-email', {
    method: 'POST',
    body: JSON.stringify({ token }),
  });
  return response?.success ?? false;
}

// 删除账户，永久删除用户账户及所有相关数据
export async function deleteAccount(request: DeleteAccountRequest): Promise<boolean> {
  const response = await fetchApi<{ success: boolean }>('/user/delete-account', {
    method: 'DELETE',
    body: JSON.stringify(request),
  });
  return response?.success ?? false;
}
