'use client';

import { useState } from 'react';
import { BatchJobDetails, BatchVideoItem } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckSquare, List, Square } from 'lucide-react';
import { VideoListItem } from './video-list-item';
import { VideoListFilters } from './video-list-filters';

interface VideoListSectionProps {
  jobData: BatchJobDetails;
  isJobStarted: boolean;
  onJobUpdate: (data: Partial<BatchJobDetails>) => void;
  onVideoUpdate: (videoId: string, updates: Partial<BatchVideoItem>) => void;
}

export function VideoListSection({
  jobData,
  isJobStarted,
  onJobUpdate,
  onVideoUpdate,
}: VideoListSectionProps) {
  const [filteredVideos, setFilteredVideos] = useState(jobData.videos);

  const selectedCount = filteredVideos.filter((v) => v.selected).length;
  const totalCount = filteredVideos.length;

  const handleSelectAll = () => {
    const allSelected = selectedCount === totalCount;
    const updatedVideos = jobData.videos.map((video) => {
      const isInFiltered = filteredVideos.some((fv) => fv.id === video.id);
      return isInFiltered ? { ...video, selected: !allSelected } : video;
    });

    onJobUpdate({
      videos: updatedVideos,
      stats: calculateStats(updatedVideos),
    });
  };

  const handleVideoSelect = (videoId: string, selected: boolean) => {
    const updatedVideos = jobData.videos.map((video) =>
      video.id === videoId ? { ...video, selected } : video,
    );

    onJobUpdate({
      videos: updatedVideos,
      stats: calculateStats(updatedVideos),
    });
  };

  const handleVideoUpdate = (videoId: string, updates: Partial<BatchVideoItem>) => {
    // 使用新的 onVideoUpdate 函数来更新单个视频
    onVideoUpdate(videoId, updates);
  };

  const handleFiltersApply = (filtered: BatchVideoItem[]) => {
    setFilteredVideos(filtered);
  };

  const calculateStats = (videos: BatchVideoItem[]) => {
    const selected = videos.filter((v) => v.selected);
    return {
      totalSelected: selected.length,
      completed: selected.filter((v) => v.status === 'completed').length,
      processing: selected.filter((v) => v.status === 'processing').length,
      failed: selected.filter((v) => v.status === 'failed').length,
      queued: selected.filter((v) => v.status === 'queued').length,
      pending: selected.filter((v) => v.status === 'pending').length,
    };
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">待处理</Badge>;
      case 'queued':
        return <Badge variant="outline">排队中</Badge>;
      case 'processing':
        return <Badge className="bg-blue-500">处理中</Badge>;
      case 'completed':
        return <Badge className="bg-green-500">已完成</Badge>;
      case 'failed':
        return <Badge variant="destructive">失败</Badge>;
      case 'cancelled':
        return <Badge variant="outline">已取消</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <List className="h-5 w-5 text-primary" />
            视频列表
          </CardTitle>
          <div className="flex items-center gap-2">
            {/* 批量选择按钮 */}
            <Button variant="outline" size="sm" onClick={handleSelectAll} disabled={isJobStarted}>
              {selectedCount === totalCount ? (
                <>
                  <Square className="mr-1 h-4 w-4" />
                  全不选
                </>
              ) : (
                <>
                  <CheckSquare className="mr-1 h-4 w-4" />
                  全选
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            已选中 {selectedCount}/{totalCount} 个视频
          </span>
          {isJobStarted && (
            <div className="flex items-center gap-4">
              <span>已完成: {jobData.stats.completed}</span>
              <span>处理中: {jobData.stats.processing}</span>
              <span>失败: {jobData.stats.failed}</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 筛选器 (仅对播放列表/频道显示) */}
        {(jobData.sourceType === 'PLAYLIST' || jobData.sourceType === 'CHANNEL') && (
          <VideoListFilters videos={jobData.videos} onFiltersApply={handleFiltersApply} />
        )}

        {/* 整体进度 (仅在任务运行时显示) */}
        {isJobStarted && jobData.progress > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>整体进度</span>
              <span>{jobData.progress}%</span>
            </div>
            <Progress value={jobData.progress} className="h-2" />
          </div>
        )}

        {/* 视频列表 */}
        <div className="space-y-2">
          {filteredVideos.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <List className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>没有找到符合条件的视频</p>
            </div>
          ) : (
            filteredVideos.map((video, index) => (
              <VideoListItem
                key={video.id}
                video={video}
                index={index + 1}
                isJobStarted={isJobStarted}
                batchJobId={jobData.id}
                onSelect={handleVideoSelect}
                onUpdate={handleVideoUpdate}
              />
            ))
          )}
        </div>

        {/* 加载更多提示 (如果需要) */}
        {totalCount >= 1000 && (
          <div className="text-center py-4 text-sm text-muted-foreground">
            <p>已显示前 1000 个视频，更多视频将被忽略</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
