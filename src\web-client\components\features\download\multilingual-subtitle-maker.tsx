'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Download, Eye, Languages, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { VideoPageData } from '@/lib/types';

interface MultilingualSubtitleMakerProps {
  videoData: VideoPageData;
  subtitles: Array<{
    language: string;
    languageCode: string;
    isAutoGenerated: boolean;
    url: string;
  }>;
  onGenerate: (
    primaryLang: string,
    secondaryLang?: string,
    tertiaryLang?: string,
    format: string,
  ) => void;
}

interface PreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  primaryLang: string;
  secondaryLang?: string;
  tertiaryLang?: string;
  sampleContent: string;
}

function PreviewModal({
  isOpen,
  onClose,
  primaryLang,
  secondaryLang,
  tertiaryLang,
  sampleContent,
}: PreviewModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">多语言字幕预览</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
          <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
            <Languages className="h-4 w-4" />
            <span>主要语言: {primaryLang}</span>
            {secondaryLang && secondaryLang !== 'none' && <span>• 第二语言: {secondaryLang}</span>}
            {tertiaryLang && tertiaryLang !== 'none' && <span>• 第三语言: {tertiaryLang}</span>}
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-96">
          <div className="space-y-4 font-mono text-sm">
            {/* 示例多语言字幕内容 */}
            <div className="border rounded p-3 bg-gray-50">
              <div className="text-gray-500 text-xs mb-2">00:00:01,000 - 00:00:04,000</div>
              <div className="space-y-1">
                <div className="font-medium">{primaryLang}: Hello, welcome to our channel!</div>
                {secondaryLang && (
                  <div className="text-blue-600">{secondaryLang}: 你好，欢迎来到我们的频道！</div>
                )}
                {tertiaryLang && (
                  <div className="text-green-600">
                    {tertiaryLang}: Hola, ¡bienvenidos a nuestro canal!
                  </div>
                )}
              </div>
            </div>

            <div className="border rounded p-3 bg-gray-50">
              <div className="text-gray-500 text-xs mb-2">00:00:05,000 - 00:00:08,000</div>
              <div className="space-y-1">
                <div className="font-medium">
                  {primaryLang}: Today we&apos;re going to learn something new.
                </div>
                {secondaryLang && (
                  <div className="text-blue-600">{secondaryLang}: 今天我们要学习一些新东西。</div>
                )}
                {tertiaryLang && (
                  <div className="text-green-600">
                    {tertiaryLang}: Hoy vamos a aprender algo nuevo.
                  </div>
                )}
              </div>
            </div>

            <div className="border rounded p-3 bg-gray-50">
              <div className="text-gray-500 text-xs mb-2">00:00:09,000 - 00:00:12,000</div>
              <div className="space-y-1">
                <div className="font-medium">{primaryLang}: Let&apos;s get started!</div>
                {secondaryLang && (
                  <div className="text-blue-600">{secondaryLang}: 让我们开始吧！</div>
                )}
                {tertiaryLang && <div className="text-green-600">{tertiaryLang}: ¡Empecemos!</div>}
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-t bg-gray-50">
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
            <Button onClick={() => navigator.clipboard.writeText(sampleContent)}>复制内容</Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export function MultilingualSubtitleMaker({
  videoData,
  subtitles,
  onGenerate,
}: MultilingualSubtitleMakerProps) {
  const [primaryLanguage, setPrimaryLanguage] = useState<string>('');
  const [secondaryLanguage, setSecondaryLanguage] = useState<string>('none');
  const [tertiaryLanguage, setTertiaryLanguage] = useState<string>('none');
  const [outputFormat, setOutputFormat] = useState<'srt' | 'vtt' | 'txt'>('srt');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isGenerated, setIsGenerated] = useState(false);

  // 获取可用的字幕语言选项
  const availableLanguages = subtitles.map((sub) => ({
    value: sub.languageCode,
    label: sub.language,
    isAutoGenerated: sub.isAutoGenerated,
  }));

  // 验证配置是否有效
  const isConfigValid = primaryLanguage !== '';

  // 获取验证错误信息
  const getValidationError = (): string | null => {
    if (!primaryLanguage) return '请选择主要语言';
    if (secondaryLanguage !== 'none' && secondaryLanguage === primaryLanguage)
      return '第二语言不能与主要语言相同';
    if (
      tertiaryLanguage !== 'none' &&
      (tertiaryLanguage === primaryLanguage || tertiaryLanguage === secondaryLanguage)
    ) {
      return '第三语言不能与其他语言相同';
    }
    return null;
  };

  // 生成多语言字幕
  const handleGenerate = async () => {
    if (!isConfigValid) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setIsGenerated(false);

    try {
      // 模拟生成过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 200));
        setGenerationProgress(i);
      }

      // 调用回调函数
      onGenerate(
        primaryLanguage,
        secondaryLanguage === 'none' ? undefined : secondaryLanguage,
        tertiaryLanguage === 'none' ? undefined : tertiaryLanguage,
        outputFormat,
      );
      setIsGenerated(true);
    } catch (error) {
      console.error('多语言字幕生成失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // 预览多语言字幕
  const handlePreview = () => {
    if (!isConfigValid) return;
    setIsPreviewOpen(true);
  };

  const validationError = getValidationError();

  return (
    <>
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Languages className="h-5 w-5 text-primary" />
            多语言字幕制作
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            将原始字幕与翻译版本合并，生成多语言对照字幕文件
          </p>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 语言选择 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 主要语言 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-1">
                主要语言 <span className="text-red-500">*</span>
              </Label>
              <Select value={primaryLanguage} onValueChange={setPrimaryLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择主要语言" />
                </SelectTrigger>
                <SelectContent>
                  {availableLanguages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      <div className="flex items-center gap-2">
                        <span>{lang.label}</span>
                        {lang.isAutoGenerated && (
                          <Badge variant="outline" className="text-xs">
                            自动
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 第二语言 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">第二语言 (可选)</Label>
              <Select value={secondaryLanguage} onValueChange={setSecondaryLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择第二语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">不选择</SelectItem>
                  {availableLanguages
                    .filter((lang) => lang.value !== primaryLanguage)
                    .map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        <div className="flex items-center gap-2">
                          <span>{lang.label}</span>
                          {lang.isAutoGenerated && (
                            <Badge variant="outline" className="text-xs">
                              自动
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {/* 第三语言 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">第三语言 (可选)</Label>
              <Select value={tertiaryLanguage} onValueChange={setTertiaryLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择第三语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">不选择</SelectItem>
                  {availableLanguages
                    .filter(
                      (lang) => lang.value !== primaryLanguage && lang.value !== secondaryLanguage,
                    )
                    .map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        <div className="flex items-center gap-2">
                          <span>{lang.label}</span>
                          {lang.isAutoGenerated && (
                            <Badge variant="outline" className="text-xs">
                              自动
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 输出格式 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">输出格式</Label>
            <Select
              value={outputFormat}
              onValueChange={(value: 'srt' | 'vtt' | 'txt') => setOutputFormat(value)}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="srt">SRT (推荐)</SelectItem>
                <SelectItem value="vtt">VTT (Web)</SelectItem>
                <SelectItem value="txt">TXT (纯文本)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 配置预览 */}
          {primaryLanguage && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-blue-700">配置预览</span>
              </div>
              <div className="text-sm text-blue-600 space-y-1">
                <div>
                  主要语言: {availableLanguages.find((l) => l.value === primaryLanguage)?.label}
                </div>
                {secondaryLanguage !== 'none' && (
                  <div>
                    第二语言: {availableLanguages.find((l) => l.value === secondaryLanguage)?.label}
                  </div>
                )}
                {tertiaryLanguage !== 'none' && (
                  <div>
                    第三语言: {availableLanguages.find((l) => l.value === tertiaryLanguage)?.label}
                  </div>
                )}
                <div>输出格式: {outputFormat.toUpperCase()}</div>
              </div>
            </div>
          )}

          {/* 验证错误提示 */}
          {validationError && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
              <span className="text-sm text-red-700">{validationError}</span>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={handlePreview}
              disabled={!isConfigValid || isGenerating}
              className="flex-1">
              <Eye className="h-4 w-4 mr-2" />
              预览效果
            </Button>

            <Button
              onClick={handleGenerate}
              disabled={!isConfigValid || isGenerating}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  生成并下载
                </>
              )}
            </Button>
          </div>

          {/* 生成进度 */}
          {isGenerating && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>生成进度</span>
                <span>{generationProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${generationProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* 生成成功提示 */}
          {isGenerated && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700">多语言字幕生成完成！文件已开始下载。</span>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* 预览模态窗口 */}
      <AnimatePresence>
        {isPreviewOpen && (
          <PreviewModal
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            primaryLang={availableLanguages.find((l) => l.value === primaryLanguage)?.label || ''}
            secondaryLang={
              secondaryLanguage !== 'none'
                ? availableLanguages.find((l) => l.value === secondaryLanguage)?.label
                : undefined
            }
            tertiaryLang={
              tertiaryLanguage !== 'none'
                ? availableLanguages.find((l) => l.value === tertiaryLanguage)?.label
                : undefined
            }
            sampleContent="示例多语言字幕内容"
          />
        )}
      </AnimatePresence>
    </>
  );
}
