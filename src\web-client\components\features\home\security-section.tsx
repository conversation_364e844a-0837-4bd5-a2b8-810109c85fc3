import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Eye, Lock, Server, Shield, Zap } from 'lucide-react';

const securityFeatures = [
  {
    icon: <Shield className="h-8 w-8" />,
    title: '数据安全保护',
    description: '采用端到端加密技术，确保您的数据在传输过程中完全安全，我们不会存储任何个人信息。',
    features: ['SSL/TLS加密', '零日志政策', '自动数据清理'],
    color: 'from-blue-500 to-cyan-500',
  },
  {
    icon: <Lock className="h-8 w-8" />,
    title: '隐私保护承诺',
    description: '严格遵守隐私保护法规，不收集、不存储、不分享您的任何个人信息和使用数据。',
    features: ['匿名使用', '无需注册', 'GDPR合规'],
    color: 'from-green-500 to-emerald-500',
  },
  {
    icon: <Eye className="h-8 w-8" />,
    title: '透明化运营',
    description: '开放透明的服务机制，所有处理过程可追踪，让您清楚了解每一步操作。',
    features: ['开源组件', '处理日志', '状态透明'],
    color: 'from-purple-500 to-pink-500',
  },
  {
    icon: <Server className="h-8 w-8" />,
    title: '基础设施安全',
    description: '采用企业级云服务基础设施，多重安全防护，确保服务稳定可靠。',
    features: ['云端防护', '备份机制', '监控告警'],
    color: 'from-orange-500 to-red-500',
  },
];

const certifications = [
  { name: 'SSL证书', status: '已认证', color: 'bg-green-100 text-green-800' },
  { name: '安全扫描', status: '无威胁', color: 'bg-blue-100 text-blue-800' },
  { name: '隐私合规', status: 'GDPR', color: 'bg-purple-100 text-purple-800' },
  { name: '数据保护', status: '零存储', color: 'bg-orange-100 text-orange-800' },
];

export function SecuritySection() {
  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50">
      <div className="container mx-auto px-4">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Shield className="h-4 w-4" />
            安全保障
          </div>
          <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            您的安全，我们的承诺
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            采用银行级安全标准，全方位保护您的隐私和数据安全，让您安心使用我们的服务
          </p>
        </div>

        {/* 安全认证徽章 */}
        <div className="flex flex-wrap items-center justify-center gap-4 mb-16">
          {certifications.map((cert, index) => (
            <Badge key={index} className={`${cert.color} px-4 py-2 text-sm font-medium`}>
              <CheckCircle className="h-4 w-4 mr-2" />
              {cert.name}: {cert.status}
            </Badge>
          ))}
        </div>

        {/* 安全功能网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {securityFeatures.map((feature, index) => (
            <div key={index}>
              <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div
                      className={`p-3 bg-gradient-to-r ${feature.color} rounded-xl text-white group-hover:scale-110 transition-transform duration-300`}>
                      {feature.icon}
                    </div>
                    <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {feature.description}
                  </p>
                  <div className="space-y-3">
                    {feature.features.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center gap-3">
                        <div className="p-1 bg-green-100 rounded-full">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <span className="text-sm font-medium">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* 安全承诺声明 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="p-3 bg-white/20 rounded-full">
                <Zap className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold">安全承诺</h3>
            </div>
            <p className="text-lg leading-relaxed mb-6">
              我们承诺永远不会存储您的个人信息、下载历史或任何敏感数据。
              所有处理都在安全的云端环境中进行，处理完成后立即清理所有临时文件。
            </p>
            <div className="flex flex-wrap items-center justify-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                <span>零数据存储</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                <span>端到端加密</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                <span>自动清理</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                <span>隐私保护</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
