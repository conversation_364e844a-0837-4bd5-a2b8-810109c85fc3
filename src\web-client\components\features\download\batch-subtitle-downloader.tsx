'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Download, FileText, Globe, Loader2, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface BatchSubtitleDownloaderProps {
  subtitles: Array<{
    language: string;
    languageCode: string;
    isAutoGenerated: boolean;
    url: string;
  }>;
  onBatchDownload: (
    selectedLanguages: string[],
    format: string,
    includeTranslations: boolean,
  ) => void;
}

export function BatchSubtitleDownloader({
  subtitles,
  onBatchDownload,
}: BatchSubtitleDownloaderProps) {
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);
  const [outputFormat, setOutputFormat] = useState<'srt' | 'vtt' | 'txt'>('srt');
  const [includeTranslations, setIncludeTranslations] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);

  // 分组字幕：官方字幕和自动生成字幕
  const officialSubtitles = subtitles.filter((sub) => !sub.isAutoGenerated);
  const autoGeneratedSubtitles = subtitles.filter((sub) => sub.isAutoGenerated);

  // 处理语言选择
  const handleLanguageToggle = (languageCode: string, checked: boolean) => {
    if (checked) {
      setSelectedLanguages((prev) => [...prev, languageCode]);
    } else {
      setSelectedLanguages((prev) => prev.filter((code) => code !== languageCode));
    }
  };

  // 全选/全不选
  const handleSelectAll = (subtitleGroup: typeof subtitles, checked: boolean) => {
    const languageCodes = subtitleGroup.map((sub) => sub.languageCode);
    if (checked) {
      setSelectedLanguages((prev) => [...new Set([...prev, ...languageCodes])]);
    } else {
      setSelectedLanguages((prev) => prev.filter((code) => !languageCodes.includes(code)));
    }
  };

  // 批量下载
  const handleBatchDownload = async () => {
    if (selectedLanguages.length === 0) return;

    setIsDownloading(true);
    setDownloadProgress(0);
    setIsCompleted(false);

    try {
      // 模拟下载过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setDownloadProgress(i);
      }

      // 调用回调函数
      onBatchDownload(selectedLanguages, outputFormat, includeTranslations);
      setIsCompleted(true);
    } catch (error) {
      console.error('批量下载失败:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // 计算预估文件数量
  const getEstimatedFileCount = () => {
    let count = selectedLanguages.length;
    if (includeTranslations) {
      // 假设每种语言可以翻译成其他所有可用语言
      count *= subtitles.length;
    }
    return count;
  };

  const SubtitleGroup = ({
    title,
    subtitles: groupSubtitles,
    icon,
  }: {
    title: string;
    subtitles: typeof subtitles;
    icon: React.ReactNode;
  }) => {
    if (groupSubtitles.length === 0) return null;

    const allSelected = groupSubtitles.every((sub) => selectedLanguages.includes(sub.languageCode));
    const someSelected = groupSubtitles.some((sub) => selectedLanguages.includes(sub.languageCode));

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {icon}
            <h4 className="font-medium text-sm">{title}</h4>
            <Badge variant="outline" className="text-xs">
              {groupSubtitles.length} 种语言
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              checked={allSelected}
              ref={(el) => {
                if (el) el.indeterminate = someSelected && !allSelected;
              }}
              onCheckedChange={(checked) => handleSelectAll(groupSubtitles, checked as boolean)}
            />
            <Label className="text-xs text-muted-foreground">全选</Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          {groupSubtitles.map((subtitle) => (
            <div
              key={subtitle.languageCode}
              className={`flex items-center space-x-2 p-2 rounded border transition-colors ${
                selectedLanguages.includes(subtitle.languageCode)
                  ? 'bg-blue-50 border-blue-200'
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}>
              <Checkbox
                checked={selectedLanguages.includes(subtitle.languageCode)}
                onCheckedChange={(checked) =>
                  handleLanguageToggle(subtitle.languageCode, checked as boolean)
                }
              />
              <Label className="text-sm cursor-pointer flex-1">{subtitle.language}</Label>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className="border-2 border-muted/20">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Package className="h-5 w-5 text-primary" />
          批量字幕下载
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          选择多种语言的字幕进行批量下载，支持打包为ZIP文件
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 字幕选择 */}
        <div className="space-y-4">
          <SubtitleGroup
            title="官方字幕"
            subtitles={officialSubtitles}
            icon={<FileText className="h-4 w-4 text-green-600" />}
          />

          <SubtitleGroup
            title="自动生成字幕"
            subtitles={autoGeneratedSubtitles}
            icon={<Globe className="h-4 w-4 text-blue-600" />}
          />
        </div>

        {/* 下载选项 */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 输出格式 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">输出格式</Label>
              <Select
                value={outputFormat}
                onValueChange={(value: 'srt' | 'vtt' | 'txt') => setOutputFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="srt">SRT (推荐)</SelectItem>
                  <SelectItem value="vtt">VTT (Web)</SelectItem>
                  <SelectItem value="txt">TXT (纯文本)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 包含翻译 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">翻译选项</Label>
              <div className="flex items-center space-x-2 h-10">
                <Checkbox
                  id="include-translations"
                  checked={includeTranslations}
                  onCheckedChange={(checked) => setIncludeTranslations(checked as boolean)}
                />
                <Label htmlFor="include-translations" className="text-sm">
                  包含所有可用翻译版本
                </Label>
              </div>
            </div>
          </div>

          {/* 选择统计 */}
          {selectedLanguages.length > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-blue-700">下载配置</span>
              </div>
              <div className="text-sm text-blue-600 space-y-1">
                <div>已选择语言: {selectedLanguages.length} 种</div>
                <div>输出格式: {outputFormat.toUpperCase()}</div>
                <div>预估文件数: {getEstimatedFileCount()} 个</div>
                {includeTranslations && (
                  <div className="text-xs text-blue-500">* 包含翻译版本会显著增加文件数量</div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 下载按钮 */}
        <div className="space-y-3">
          <Button
            onClick={handleBatchDownload}
            disabled={selectedLanguages.length === 0 || isDownloading}
            className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            size="lg">
            {isDownloading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                打包下载中...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                一键下载所有选中字幕 (.zip)
              </>
            )}
          </Button>

          {/* 下载进度 */}
          {isDownloading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>打包进度</span>
                <span>{downloadProgress}%</span>
              </div>
              <Progress value={downloadProgress} className="w-full" />
              <div className="text-xs text-gray-500 text-center">
                正在打包 {selectedLanguages.length} 种语言的字幕文件...
              </div>
            </div>
          )}

          {/* 完成提示 */}
          {isCompleted && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700">批量下载完成！ZIP文件已开始下载。</span>
            </motion.div>
          )}
        </div>

        {/* 使用说明 */}
        <div className="text-xs text-gray-500 space-y-1 border-t pt-4">
          <div className="font-medium">使用说明：</div>
          <div>• 选择需要下载的字幕语言</div>
          <div>• 所有选中的字幕将打包为一个ZIP文件</div>
          <div>• 文件名格式：[语言代码].[格式]，如 en.srt、zh-CN.srt</div>
          <div>• 启用翻译选项将包含所有可用的翻译版本</div>
        </div>
      </CardContent>
    </Card>
  );
}
