'use client';

import { toast as sonnerToast } from 'sonner';

export interface ToastOptions {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

export function useToast() {
  const toast = ({ title, description, variant = 'default', ...props }: ToastOptions) => {
    const message = title || description || '';

    switch (variant) {
      case 'destructive':
        return sonnerToast.error(message, {
          description: title ? description : undefined,
          ...props,
        });
      default:
        return sonnerToast(message, {
          description: title ? description : undefined,
          ...props,
        });
    }
  };

  return {
    toast,
    dismiss: sonnerToast.dismiss,
  };
}
