'use client';

import { useEffect, useState } from 'react';
import { BatchJobFilters, BatchVideoItem } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Filter, RotateCcw } from 'lucide-react';

interface VideoListFiltersProps {
  videos: BatchVideoItem[];
  onFiltersApply: (filteredVideos: BatchVideoItem[]) => void;
}

export function VideoListFilters({ videos, onFiltersApply }: VideoListFiltersProps) {
  const [filters, setFilters] = useState<BatchJobFilters>({});
  const [titleKeywords, setTitleKeywords] = useState('');

  useEffect(() => {
    applyFilters();
  }, [filters, titleKeywords, videos]);

  const applyFilters = () => {
    let filtered = [...videos];

    // 时长筛选
    if (filters.duration) {
      filtered = filtered.filter((video) => {
        if (!video.duration) return true;
        const { operator, value } = filters.duration!;
        return operator === 'gt' ? video.duration > value : video.duration < value;
      });
    }

    // 上传日期筛选
    if (filters.uploadDate) {
      filtered = filtered.filter((video) => {
        if (!video.uploadDate) return true;
        const videoDate = new Date(video.uploadDate);
        const filterDate = new Date(filters.uploadDate!.value);
        const { operator } = filters.uploadDate!;
        return operator === 'after' ? videoDate > filterDate : videoDate < filterDate;
      });
    }

    // 观看次数筛选
    if (filters.viewCount) {
      filtered = filtered.filter((video) => {
        if (!video.viewCount) return true;
        const { operator, value } = filters.viewCount!;
        return operator === 'gt' ? video.viewCount > value : video.viewCount < value;
      });
    }

    // 标题关键词筛选
    if (titleKeywords.trim()) {
      const keywords = titleKeywords
        .toLowerCase()
        .split(/\s+/)
        .filter((k) => k.length > 0);
      filtered = filtered.filter((video) => {
        if (!video.title) return false;
        const title = video.title.toLowerCase();
        return keywords.every((keyword) => title.includes(keyword));
      });
    }

    onFiltersApply(filtered);
  };

  const handleDurationFilter = (operator: 'gt' | 'lt', value: string) => {
    const numValue = parseInt(value);
    if (isNaN(numValue) || numValue <= 0) {
      setFilters((prev) => ({ ...prev, duration: undefined }));
    } else {
      setFilters((prev) => ({ ...prev, duration: { operator, value: numValue } }));
    }
  };

  const handleUploadDateFilter = (operator: 'after' | 'before', value: string) => {
    if (!value) {
      setFilters((prev) => ({ ...prev, uploadDate: undefined }));
    } else {
      setFilters((prev) => ({ ...prev, uploadDate: { operator, value } }));
    }
  };

  const handleViewCountFilter = (operator: 'gt' | 'lt', value: string) => {
    const numValue = parseInt(value);
    if (isNaN(numValue) || numValue <= 0) {
      setFilters((prev) => ({ ...prev, viewCount: undefined }));
    } else {
      setFilters((prev) => ({ ...prev, viewCount: { operator, value: numValue } }));
    }
  };

  const resetFilters = () => {
    setFilters({});
    setTitleKeywords('');
  };

  const hasActiveFilters = Object.keys(filters).length > 0 || titleKeywords.trim().length > 0;

  return (
    <Card className="border-dashed">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Filter className="h-4 w-4 text-primary" />
            筛选视频
          </CardTitle>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={resetFilters} className="h-6 px-2">
              <RotateCcw className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-4">
          {/* 标题关键词筛选 */}
          <div className="space-y-1">
            <Label htmlFor="title-keywords" className="text-xs font-medium">
              标题关键词
            </Label>
            <Input
              id="title-keywords"
              placeholder="输入关键词"
              value={titleKeywords}
              onChange={(e) => setTitleKeywords(e.target.value)}
              className="h-7"
            />
          </div>

          {/* 时长筛选 */}
          <div className="space-y-1">
            <Label className="text-xs font-medium">视频时长</Label>
            <div className="grid grid-cols-2 gap-1">
              <Select
                value={filters.duration?.operator || ''}
                onValueChange={(value: 'gt' | 'lt') => {
                  const currentValue = filters.duration?.value || 0;
                  handleDurationFilter(value, currentValue.toString());
                }}>
                <SelectTrigger className="h-7">
                  <SelectValue placeholder="条件" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gt">大于</SelectItem>
                  <SelectItem value="lt">小于</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="number"
                placeholder="秒"
                value={filters.duration?.value || ''}
                onChange={(e) => {
                  const operator = filters.duration?.operator || 'gt';
                  handleDurationFilter(operator, e.target.value);
                }}
                className="h-7"
              />
            </div>
          </div>

          {/* 上传日期筛选 */}
          <div className="space-y-1">
            <Label className="text-xs font-medium">上传日期</Label>
            <div className="grid grid-cols-2 gap-1">
              <Select
                value={filters.uploadDate?.operator || ''}
                onValueChange={(value: 'after' | 'before') => {
                  const currentValue = filters.uploadDate?.value || '';
                  handleUploadDateFilter(value, currentValue);
                }}>
                <SelectTrigger className="h-7">
                  <SelectValue placeholder="条件" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="after">晚于</SelectItem>
                  <SelectItem value="before">早于</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="date"
                value={filters.uploadDate?.value ? filters.uploadDate.value.split('T')[0] : ''}
                onChange={(e) => {
                  const operator = filters.uploadDate?.operator || 'after';
                  const dateValue = e.target.value ? `${e.target.value}T00:00:00Z` : '';
                  handleUploadDateFilter(operator, dateValue);
                }}
                className="h-7"
              />
            </div>
          </div>

          {/* 观看次数筛选 */}
          <div className="space-y-1">
            <Label className="text-xs font-medium">观看次数</Label>
            <div className="grid grid-cols-2 gap-1">
              <Select
                value={filters.viewCount?.operator || ''}
                onValueChange={(value: 'gt' | 'lt') => {
                  const currentValue = filters.viewCount?.value || 0;
                  handleViewCountFilter(value, currentValue.toString());
                }}>
                <SelectTrigger className="h-7">
                  <SelectValue placeholder="条件" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gt">大于</SelectItem>
                  <SelectItem value="lt">小于</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="number"
                placeholder="次数"
                value={filters.viewCount?.value || ''}
                onChange={(e) => {
                  const operator = filters.viewCount?.operator || 'gt';
                  handleViewCountFilter(operator, e.target.value);
                }}
                className="h-7"
              />
            </div>
          </div>
        </div>

        {/* 筛选结果提示 */}
        {hasActiveFilters && (
          <div className="text-xs text-muted-foreground text-center">
            筛选条件已应用，所有条件之间为 AND 关系
          </div>
        )}
      </CardContent>
    </Card>
  );
}
