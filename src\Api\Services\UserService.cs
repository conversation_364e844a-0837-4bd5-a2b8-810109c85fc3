using System.Security.Claims;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace Api.Services;

/// <summary>
///     用户管理服务
///     处理用户相关的业务逻辑，包括注册用户和匿名用户的管理
/// </summary>
public class UserService(AppDbContext context)
{
    #region 注册用户管理

    /// <summary>
    ///     根据ID获取注册用户
    /// </summary>
    public async Task<User?> GetRegisteredUserByIdAsync(Guid userId)
    {
        return await context.Users.FirstOrDefaultAsync(u => u.Id == userId && u.Type == UserType.Registered && u.IsActive);
    }

    /// <summary>
    ///     根据邮箱获取注册用户
    /// </summary>
    public async Task<User?> GetRegisteredUserByEmailAsync(string email)
    {
        return await context.Users.FirstOrDefaultAsync(u => u.Email == email && u.Type == UserType.Registered && u.IsActive);
    }

    /// <summary>
    ///     创建注册用户
    /// </summary>
    public async Task<User> CreateRegisteredUserAsync(string email, string passwordHash, string? username = null)
    {
        var user = new User { Type = UserType.Registered, Email = email, Username = username, PasswordHash = passwordHash };

        context.Users.Add(user);
        await context.SaveChangesAsync();

        return user;
    }

    /// <summary>
    ///     检查邮箱是否已存在
    /// </summary>
    public async Task<bool> IsEmailExistsAsync(string email)
    {
        return await context.Users.AnyAsync(u => u.Type == UserType.Registered && u.Email == email);
    }

    /// <summary>
    ///     更新用户信息
    /// </summary>
    public async Task<User?> UpdateUserAsync(Guid userId, string? username = null, string? avatarUrl = null)
    {
        var user = await GetRegisteredUserByIdAsync(userId);
        if (user == null) return null;

        if (!string.IsNullOrEmpty(username))
            user.Username = username;

        if (!string.IsNullOrEmpty(avatarUrl))
            user.AvatarUrl = avatarUrl;

        user.UpdatedAt = DateTime.UtcNow;
        await context.SaveChangesAsync();

        return user;
    }

    /// <summary>
    ///     更新用户最后登录时间
    /// </summary>
    public async Task UpdateLastLoginAsync(Guid userId)
    {
        var user = await GetRegisteredUserByIdAsync(userId);
        if (user != null)
        {
            user.LastLoginAt = DateTime.UtcNow;
            user.LastActiveAt = DateTime.UtcNow;
            await context.SaveChangesAsync();
        }
    }

    #endregion

    #region 匿名用户管理

    /// <summary>
    ///     根据匿名ID获取匿名用户
    /// </summary>
    public async Task<User?> GetAnonymousUserAsync(string anonymousId)
    {
        return await context.Users.FirstOrDefaultAsync(u => u.Type == UserType.Anonymous && u.AnonymousId == anonymousId);
    }

    /// <summary>
    ///     创建匿名用户
    /// </summary>
    public async Task<User> CreateAnonymousUserAsync(string anonymousId)
    {
        var anonymousUser = new User { Type = UserType.Anonymous, AnonymousId = anonymousId };

        context.Users.Add(anonymousUser);
        await context.SaveChangesAsync();

        return anonymousUser;
    }

    /// <summary>
    ///     更新匿名用户活跃时间
    /// </summary>
    public async Task UpdateAnonymousUserActivityAsync(string anonymousId)
    {
        var anonymousUser = await GetAnonymousUserAsync(anonymousId);
        if (anonymousUser != null)
        {
            anonymousUser.LastActiveAt = DateTime.UtcNow;
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    ///     迁移匿名用户数据到注册用户
    /// </summary>
    public async Task<(bool Success, int TasksCount, int BatchJobsCount)> MigrateAnonymousDataAsync(string anonymousId, Guid registeredUserId)
    {
        using var transaction = await context.Database.BeginTransactionAsync();

        try
        {
            var anonymousUser = await GetAnonymousUserAsync(anonymousId);
            if (anonymousUser == null || anonymousUser.MigratedFromUserId != null)
                return (false, 0, 0);

            // 统计要迁移的数据
            var tasksCount = await context.WorkTasks.CountAsync(wt => wt.UserId == anonymousUser.Id);
            var batchJobsCount = await context.BatchJobs.CountAsync(bj => bj.UserId == anonymousUser.Id);

            // 迁移 WorkTasks
            await context.WorkTasks.Where(wt => wt.UserId == anonymousUser.Id).ExecuteUpdateAsync(wt => wt.SetProperty(w => w.UserId, registeredUserId));

            // 迁移 BatchJobs
            await context.BatchJobs.Where(bj => bj.UserId == anonymousUser.Id).ExecuteUpdateAsync(bj => bj.SetProperty(b => b.UserId, registeredUserId));

            // 标记匿名用户为已迁移
            anonymousUser.MigratedFromUserId = registeredUserId;
            anonymousUser.MigratedAt = DateTime.UtcNow;
            anonymousUser.IsActive = false;

            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            return (true, tasksCount, batchJobsCount);
        }
        catch
        {
            await transaction.RollbackAsync();
            return (false, 0, 0);
        }
    }

    /// <summary>
    ///     清理过期的匿名用户
    /// </summary>
    public async Task<int> CleanupExpiredAnonymousUsersAsync(int expiredDays = 180)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-expiredDays);

        var expiredUsers = await context.Users.Where(u => u.Type == UserType.Anonymous && u.LastActiveAt < cutoffDate && u.MigratedFromUserId == null)
            .ToListAsync();

        if (expiredUsers.Count > 0)
        {
            context.Users.RemoveRange(expiredUsers);
            await context.SaveChangesAsync();
        }

        return expiredUsers.Count;
    }

    #endregion

    #region 通用用户操作

    /// <summary>
    ///     根据ID获取用户（任何类型）
    /// </summary>
    public async Task<User?> GetUserByIdAsync(Guid userId)
    {
        return await context.Users.FirstOrDefaultAsync(u => u.Id == userId && u.IsActive);
    }

    /// <summary>
    ///     获取用户统计信息
    /// </summary>
    public async Task<UserStatistics> GetUserStatisticsAsync()
    {
        var totalUsers = await context.Users.CountAsync(u => u.IsActive);
        var registeredUsers = await context.Users.CountAsync(u => u.Type == UserType.Registered && u.IsActive);
        var anonymousUsers = await context.Users.CountAsync(u => u.Type == UserType.Anonymous && u.IsActive);
        var activeToday = await context.Users.CountAsync(u => u.LastActiveAt >= DateTime.UtcNow.Date && u.IsActive);

        return new UserStatistics { TotalUsers = totalUsers, RegisteredUsers = registeredUsers, AnonymousUsers = anonymousUsers, ActiveToday = activeToday };
    }

    #endregion

    #region 用户身份识别

    /// <summary>
    ///     从HTTP上下文中解析用户身份
    ///     统一使用JWT token进行身份验证（支持注册用户和匿名用户）
    /// </summary>
    public async Task<UserIdentityResult> ResolveUserIdentityAsync(HttpContext httpContext)
    {
        // 1. 尝试从JWT token获取用户（注册用户或匿名用户）
        var user = await TryGetUserFromJwtAsync(httpContext);
        if (user != null)
            return new UserIdentityResult
            {
                UserId = user.Id,
                User = user,
                IsAuthenticated = user.Type == UserType.Registered,
                IsAnonymous = user.Type == UserType.Anonymous,
                AnonymousId = user.AnonymousId
            };

        // 2. 如果没有有效的JWT，返回空结果
        return new UserIdentityResult { UserId = null, User = null, IsAuthenticated = false, IsAnonymous = false };
    }

    /// <summary>
    ///     尝试从JWT token获取用户（注册用户或匿名用户）
    /// </summary>
    private async Task<User?> TryGetUserFromJwtAsync(HttpContext httpContext)
    {
        var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? httpContext.User.FindFirst("sub")?.Value;

        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId)) return null;

        // 从JWT中获取用户类型
        var userTypeClaim = httpContext.User.FindFirst("userType")?.Value;
        if (Enum.TryParse<UserType>(userTypeClaim, out var userType))
        {
            if (userType == UserType.Registered) return await GetRegisteredUserByIdAsync(userId);

            if (userType == UserType.Anonymous)
            {
                var anonymousIdClaim = httpContext.User.FindFirst("anonymousId")?.Value;
                if (!string.IsNullOrEmpty(anonymousIdClaim)) return await GetAnonymousUserAsync(anonymousIdClaim);
            }
        }

        return null;
    }

    /// <summary>
    ///     要求用户身份存在，如果没有则抛出异常
    /// </summary>
    public async Task<UserIdentityResult> RequireUserIdentityAsync(HttpContext httpContext)
    {
        var result = await ResolveUserIdentityAsync(httpContext);

        if (result.UserId == null) throw new UnauthorizedAccessException("此操作需要用户身份，但未找到有效的用户标识");

        return result;
    }

    #endregion
}

/// <summary>
///     用户统计信息
/// </summary>
public class UserStatistics
{
    public int TotalUsers { get; set; }
    public int RegisteredUsers { get; set; }
    public int AnonymousUsers { get; set; }
    public int ActiveToday { get; set; }
}

/// <summary>
///     用户身份解析结果
/// </summary>
public class UserIdentityResult
{
    public Guid? UserId { get; set; }
    public User? User { get; set; }
    public bool IsAuthenticated { get; set; }
    public bool IsAnonymous { get; set; }
    public string? AnonymousId { get; set; }
}