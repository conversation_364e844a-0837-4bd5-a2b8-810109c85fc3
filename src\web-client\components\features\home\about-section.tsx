import { Card, CardContent } from '@/components/ui/card';
import { Heart, Shield, Star, Users, Zap } from 'lucide-react';

export function AboutSection() {
  const values = [
    {
      icon: <Heart className="h-5 w-5" />,
      title: '用户至上',
      description: '始终将用户体验放在首位',
      color: 'from-red-500 to-pink-500',
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: '安全可靠',
      description: '保护用户隐私和数据安全',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: '高效便捷',
      description: '提供快速稳定的下载服务',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: <Star className="h-5 w-5" />,
      title: '持续创新',
      description: '不断优化功能和用户体验',
      color: 'from-purple-500 to-indigo-500',
    },
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Users className="h-4 w-4" />
              关于我们
            </div>
            <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              让内容保存变得简单
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              我们致力于为用户提供最优质的YouTube内容下载体验
            </p>
          </div>

          <div className="grid gap-12 lg:grid-cols-2 items-center mb-16">
            {/* 左侧：故事内容 */}
            <div className="space-y-6">
              <div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">我们的使命</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  在数字化时代，优质内容无处不在，但保存和管理这些内容却常常面临各种限制。我们深知用户在保存YouTube视频时遇到的困扰：
                  复杂的操作流程、不稳定的下载速度、安全性担忧，以及格式兼容性问题。
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">我们的承诺</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  因此，我们开发了这个专业的下载平台，致力于提供简单、安全、高效的解决方案。
                  我们相信，每个人都应该能够轻松保存自己喜爱的内容，无论是学习资料、娱乐视频还是珍贵回忆。
                  我们的目标是让技术服务于人，而不是让人适应技术。
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900">持续改进</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  我们不断优化技术架构，提升下载速度和稳定性，同时严格保护用户隐私。
                  每一次更新都基于用户反馈和实际需求，确保我们的服务始终贴近用户期望。
                  未来，我们将继续扩展功能，支持更多平台和格式，为用户创造更大价值。
                </p>
              </div>
            </div>

            {/* 右侧：核心价值观 */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold mb-6 text-gray-900 text-center">核心价值观</h3>
              <div className="grid gap-4">
                {values.map((value, index) => (
                  <Card
                    key={index}
                    className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4">
                        <div
                          className={`p-3 bg-gradient-to-r ${value.color} rounded-xl text-white group-hover:scale-110 transition-transform duration-300`}>
                          {value.icon}
                        </div>
                        <div>
                          <h4 className="font-bold text-lg mb-1">{value.title}</h4>
                          <p className="text-muted-foreground">{value.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>

          {/* 底部统计 */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold mb-6">我们的成果</h3>
              <div className="grid gap-8 md:grid-cols-3">
                <div>
                  <div className="text-3xl font-bold mb-2">10万+</div>
                  <p className="text-blue-100">满意用户</p>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">100万+</div>
                  <p className="text-blue-100">成功下载</p>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">99.9%</div>
                  <p className="text-blue-100">服务可用性</p>
                </div>
              </div>
              <div className="mt-8 pt-6 border-t border-white/20">
                <p className="text-lg leading-relaxed opacity-90">
                  感谢每一位用户的信任与支持。我们将继续努力，为您提供更好的服务体验。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
