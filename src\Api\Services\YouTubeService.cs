﻿namespace Api.Services;

public class YouTubeService
{
    /// <summary>
    ///     获取YouTube视频的详细元数据，包括可用的视频流、音频流和字幕
    /// </summary>
    /// <param name="videoId">YouTube视频ID</param>
    /// <returns>包含完整视频信息的对象</returns>
    public async Task<object> GetVideoMetadataAsync(string videoId)
    {
        // 模拟API调用延迟
        await Task.Delay(500);

        // 根据videoId生成不同的模拟数据
        var videoData = GenerateVideoData(videoId);

        return videoData;
    }

    /// <summary>
    ///     创建下载任务
    /// </summary>
    /// <param name="videoId">视频ID</param>
    /// <param name="format">下载格式</param>
    /// <param name="quality">质量设置</param>
    /// <returns>下载任务信息</returns>
    public async Task<object> CreateDownloadTaskAsync(string videoId, string format, string quality)
    {
        // 模拟API调用延迟
        await Task.Delay(200);

        return new
        {
            taskId = Guid.NewGuid(),
            videoId,
            format,
            quality,
            status = "created",
            estimatedTime = "30-60 seconds",
            createdAt = DateTime.UtcNow
        };
    }

    /// <summary>
    ///     获取播放列表信息
    /// </summary>
    /// <param name="playlistId">播放列表ID</param>
    /// <returns>播放列表详细信息</returns>
    public async Task<object> GetPlaylistInfoAsync(string playlistId)
    {
        // 模拟API调用延迟
        await Task.Delay(800);

        return GeneratePlaylistData(playlistId);
    }

    /// <summary>
    ///     获取频道信息
    /// </summary>
    /// <param name="channelId">频道ID</param>
    /// <returns>频道详细信息</returns>
    public async Task<object> GetChannelInfoAsync(string channelId)
    {
        // 模拟API调用延迟
        await Task.Delay(600);

        return GenerateChannelData(channelId);
    }

    private object GenerateVideoData(string videoId)
    {
        // 预定义的示例视频数据
        var sampleVideos = new List<dynamic>
        {
            new
            {
                id = "dQw4w9WgXcQ",
                title = "Rick Astley - Never Gonna Give You Up (Official Video)",
                description =
                    "The official video for \"Never Gonna Give You Up\" by Rick Astley. Taken from the album 'Whenever You Need Somebody' – deluxe 2CD and digital deluxe out 6th May 2022 Pre-order here – https://RickAstley.lnk.to/WYNS2022ID\n\n\"Never Gonna Give You Up\" was a global smash on its release in July 1987, topping the charts in 25 countries including Rick's native UK and the US Billboard Hot 100.  It also won the Brit Award for Best single in 1988. Stock Aitken and Waterman wrote and produced the track which was the lead-off single and lead track from Rick's debut LP \"Whenever You Need Somebody\".  The album was itself a UK number one and would go on to sell over 15 million copies worldwide.\n\nThe legendary video was directed by Simon West – who later went on to make Hollywood blockbusters such as Con Air, Lara Croft – Tomb Raider and The Expendables 2.  The video passed the 1 billion views milestone on 28 July 2021.",
                duration = 212,
                channelName = "Rick Astley",
                channelUrl = "https://www.youtube.com/@RickAstleyYT",
                uploadDate = "2009-10-25T00:00:00Z",
                viewCount = 1500000000,
                commentCount = 3200000,
                commentsDisabled = false,
                thumbnailUrl = "https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg"
            },
            new
            {
                id = "9bZkp7q19f0",
                title = "PSY - GANGNAM STYLE(강남스타일) M/V",
                description =
                    "PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0\n\nPSY - 'I LUV IT' M/V @ https://youtu.be/Xvjnoagk6GU\nPSY - 'New Face' M/V @ https://youtu.be/OwJPPaEyqhI\nPSY - 'I LUV IT' Performance Video @ https://youtu.be/10y9yeQHOW0\n\nMore about PSY@\nhttp://www.psypark.com/\nhttp://www.youtube.com/officialpsy\n\nWhat does the fox say? http://youtu.be/jofNR_WkoCE\n\n#PSY #강남스타일 #GangnamStyle",
                duration = 253,
                channelName = "officialpsy",
                channelUrl = "https://www.youtube.com/@officialpsy",
                uploadDate = "2012-07-15T00:00:00Z",
                viewCount = 4800000000,
                commentCount = 11000000,
                commentsDisabled = false,
                thumbnailUrl = "https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg"
            }
        };

        // 如果是预定义的视频ID，返回对应数据
        var predefinedVideo = sampleVideos.FirstOrDefault(v => v.id == videoId);
        if (predefinedVideo != null)
            return CreateVideoResponse(predefinedVideo.id, predefinedVideo.title, predefinedVideo.description, predefinedVideo.duration,
                predefinedVideo.channelName, predefinedVideo.channelUrl, predefinedVideo.uploadDate, predefinedVideo.viewCount, predefinedVideo.commentCount,
                predefinedVideo.commentsDisabled, predefinedVideo.thumbnailUrl);

        // 为其他videoId生成随机数据
        var random = new Random(videoId.GetHashCode());
        var titles = new[]
        {
            "Amazing Nature Documentary - Wildlife in 4K", "Learn Programming in 30 Minutes - Complete Tutorial",
            "Cooking Masterclass: Perfect Pasta Every Time", "Travel Vlog: Exploring Hidden Gems in Japan", "Tech Review: Latest Smartphone Features",
            "Fitness Workout: 20-Minute Full Body Exercise", "Music Performance: Live Concert Highlights", "Educational Content: Science Explained Simply",
            "Gaming Walkthrough: Epic Boss Battle", "Art Tutorial: Digital Painting Techniques"
        };

        var channels = new[]
        {
            "NatureWorld", "CodeAcademy", "ChefMaster", "TravelExplorer", "TechReviewer", "FitnessGuru", "MusicLive", "ScienceHub", "GameMaster",
            "ArtStudio"
        };

        var selectedTitle = titles[random.Next(titles.Length)];
        var selectedChannel = channels[random.Next(channels.Length)];
        var duration = random.Next(60, 3600); // 1分钟到1小时
        var viewCount = random.Next(1000, 10000000);
        var commentCount = random.Next(10, viewCount / 100);

        return CreateVideoResponse(videoId, selectedTitle,
            "This is a sample video description with detailed information about the content. " +
            "The video covers various topics and provides valuable insights for viewers. " + "Don't forget to like and subscribe for more amazing content!",
            duration, selectedChannel, $"https://www.youtube.com/@{selectedChannel}",
            DateTime.UtcNow.AddDays(-random.Next(1, 365)).ToString("yyyy-MM-ddTHH:mm:ssZ"), viewCount, commentCount, false,
            $"https://i.ytimg.com/vi/{videoId}/maxresdefault.jpg");
    }

    private object CreateVideoResponse(string id, string title, string description, int duration, string channelName, string channelUrl, string uploadDate,
        int viewCount, int commentCount, bool commentsDisabled, string thumbnailUrl)
    {
        return new
        {
            id,
            title,
            description,
            duration,
            channelName,
            channelUrl,
            uploadDate,
            viewCount,
            commentCount,
            commentsDisabled,
            thumbnailUrl,
            thumbnails = new[]
            {
                new { format = "jpg", url = $"https://i.ytimg.com/vi/{id}/default.jpg" },
                new { format = "jpg", url = $"https://i.ytimg.com/vi/{id}/mqdefault.jpg" },
                new { format = "jpg", url = $"https://i.ytimg.com/vi/{id}/hqdefault.jpg" },
                new { format = "jpg", url = $"https://i.ytimg.com/vi/{id}/maxresdefault.jpg" }
            },
            videoStreams = GenerateVideoStreams(id),
            audioStreams = GenerateAudioStreams(id),
            subtitles = GenerateSubtitles(id)
        };
    }

    private object[] GenerateVideoStreams(string videoId)
    {
        var random = new Random(videoId.GetHashCode());
        var streams = new List<object>();

        // 4K视频流
        if (random.Next(100) < 30) // 30%概率有4K
            streams.Add(new
            {
                qualityLabel = "2160p",
                resolution = "3840x2160",
                fps = 60,
                fileSize = random.Next(800000000, 1500000000), // 800MB-1.5GB
                format = "mp4",
                bitrate = random.Next(40000, 60000),
                downloadId = $"{videoId}_2160p60_mp4"
            });

        // 1080p视频流
        streams.Add(new
        {
            qualityLabel = "1080p",
            resolution = "1920x1080",
            fps = 60,
            fileSize = random.Next(200000000, 500000000), // 200MB-500MB
            format = "mp4",
            bitrate = random.Next(8000, 15000),
            downloadId = $"{videoId}_1080p60_mp4"
        });

        streams.Add(new
        {
            qualityLabel = "1080p",
            resolution = "1920x1080",
            fps = 30,
            fileSize = random.Next(150000000, 300000000), // 150MB-300MB
            format = "mp4",
            bitrate = random.Next(5000, 8000),
            downloadId = $"{videoId}_1080p30_mp4"
        });

        // 720p视频流
        streams.Add(new
        {
            qualityLabel = "720p",
            resolution = "1280x720",
            fps = 60,
            fileSize = random.Next(100000000, 200000000), // 100MB-200MB
            format = "mp4",
            bitrate = random.Next(4000, 6000),
            downloadId = $"{videoId}_720p60_mp4"
        });

        streams.Add(new
        {
            qualityLabel = "720p",
            resolution = "1280x720",
            fps = 30,
            fileSize = random.Next(80000000, 150000000), // 80MB-150MB
            format = "mp4",
            bitrate = random.Next(2500, 4000),
            downloadId = $"{videoId}_720p30_mp4"
        });

        // 480p视频流
        streams.Add(new
        {
            qualityLabel = "480p",
            resolution = "854x480",
            fps = 30,
            fileSize = random.Next(40000000, 80000000), // 40MB-80MB
            format = "mp4",
            bitrate = random.Next(1000, 2000),
            downloadId = $"{videoId}_480p30_mp4"
        });

        // 360p视频流
        streams.Add(new
        {
            qualityLabel = "360p",
            resolution = "640x360",
            fps = 30,
            fileSize = random.Next(20000000, 40000000), // 20MB-40MB
            format = "mp4",
            bitrate = random.Next(500, 1000),
            downloadId = $"{videoId}_360p30_mp4"
        });

        // WebM格式
        streams.Add(new
        {
            qualityLabel = "1080p",
            resolution = "1920x1080",
            fps = 30,
            fileSize = random.Next(120000000, 250000000),
            format = "webm",
            bitrate = random.Next(4000, 7000),
            downloadId = $"{videoId}_1080p30_webm"
        });

        streams.Add(new
        {
            qualityLabel = "720p",
            resolution = "1280x720",
            fps = 30,
            fileSize = random.Next(70000000, 120000000),
            format = "webm",
            bitrate = random.Next(2000, 3500),
            downloadId = $"{videoId}_720p30_webm"
        });

        return streams.ToArray();
    }

    private object[] GenerateAudioStreams(string videoId)
    {
        var random = new Random(videoId.GetHashCode() + 1);
        var streams = new List<object>();

        // 高质量音频流
        streams.Add(new
        {
            qualityLabel = "320 kbps",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(8000000, 15000000), // 8MB-15MB
            format = "mp3",
            bitrate = 320,
            downloadId = $"{videoId}_audio_320_mp3"
        });

        streams.Add(new
        {
            qualityLabel = "256 kbps",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(6000000, 12000000), // 6MB-12MB
            format = "aac",
            bitrate = 256,
            downloadId = $"{videoId}_audio_256_aac"
        });

        streams.Add(new
        {
            qualityLabel = "192 kbps",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(5000000, 9000000), // 5MB-9MB
            format = "mp3",
            bitrate = 192,
            downloadId = $"{videoId}_audio_192_mp3"
        });

        streams.Add(new
        {
            qualityLabel = "128 kbps",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(3000000, 6000000), // 3MB-6MB
            format = "mp3",
            bitrate = 128,
            downloadId = $"{videoId}_audio_128_mp3"
        });

        streams.Add(new
        {
            qualityLabel = "96 kbps",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(2000000, 4000000), // 2MB-4MB
            format = "aac",
            bitrate = 96,
            downloadId = $"{videoId}_audio_96_aac"
        });

        // FLAC无损音频（偶尔提供）
        if (random.Next(100) < 20) // 20%概率有FLAC
            streams.Add(new
            {
                qualityLabel = "FLAC",
                resolution = "audio only",
                fps = 0,
                fileSize = random.Next(25000000, 50000000), // 25MB-50MB
                format = "flac",
                bitrate = 1411,
                downloadId = $"{videoId}_audio_flac"
            });

        // WAV格式
        streams.Add(new
        {
            qualityLabel = "WAV",
            resolution = "audio only",
            fps = 0,
            fileSize = random.Next(30000000, 60000000), // 30MB-60MB
            format = "wav",
            bitrate = 1411,
            downloadId = $"{videoId}_audio_wav"
        });

        return streams.ToArray();
    }

    private object[] GenerateSubtitles(string videoId)
    {
        var random = new Random(videoId.GetHashCode() + 2);
        var subtitles = new List<object>();

        // 常见语言的字幕
        var commonLanguages = new[]
        {
            new { langCode = "en", langName = "English", isAutoGenerated = false },
            new { langCode = "zh-CN", langName = "中文（简体）", isAutoGenerated = false },
            new { langCode = "zh-TW", langName = "中文（繁體）", isAutoGenerated = false }, new { langCode = "ja", langName = "日本語", isAutoGenerated = false },
            new { langCode = "ko", langName = "한국어", isAutoGenerated = false }, new { langCode = "es", langName = "Español", isAutoGenerated = false },
            new { langCode = "fr", langName = "Français", isAutoGenerated = false }, new { langCode = "de", langName = "Deutsch", isAutoGenerated = false },
            new { langCode = "ru", langName = "Русский", isAutoGenerated = false },
            new { langCode = "pt", langName = "Português", isAutoGenerated = false },
            new { langCode = "ar", langName = "العربية", isAutoGenerated = false }, new { langCode = "hi", langName = "हिन्दी", isAutoGenerated = false }
        };

        // 自动生成字幕的语言
        var autoGeneratedLanguages = new[]
        {
            new { langCode = "en-auto", langName = "English (auto-generated)", isAutoGenerated = true },
            new { langCode = "zh-CN-auto", langName = "中文（简体）(auto-generated)", isAutoGenerated = true },
            new { langCode = "ja-auto", langName = "日本語 (auto-generated)", isAutoGenerated = true },
            new { langCode = "ko-auto", langName = "한국어 (auto-generated)", isAutoGenerated = true },
            new { langCode = "es-auto", langName = "Español (auto-generated)", isAutoGenerated = true },
            new { langCode = "fr-auto", langName = "Français (auto-generated)", isAutoGenerated = true },
            new { langCode = "de-auto", langName = "Deutsch (auto-generated)", isAutoGenerated = true },
            new { langCode = "ru-auto", langName = "Русский (auto-generated)", isAutoGenerated = true }
        };

        // 随机添加一些官方字幕
        var officialCount = random.Next(2, 6); // 2-5个官方字幕
        var selectedOfficial = commonLanguages.OrderBy(x => random.Next()).Take(officialCount);
        subtitles.AddRange(selectedOfficial);

        // 随机添加一些自动生成字幕
        var autoCount = random.Next(3, 8); // 3-7个自动生成字幕
        var selectedAuto = autoGeneratedLanguages.OrderBy(x => random.Next()).Take(autoCount);
        subtitles.AddRange(selectedAuto);

        return subtitles.ToArray();
    }

    private object GeneratePlaylistData(string playlistId)
    {
        var random = new Random(playlistId.GetHashCode());

        var playlistTitles = new[]
        {
            "Best Music Hits 2024", "Programming Tutorials Collection", "Travel Adventures Around the World", "Cooking Masterclass Series",
            "Fitness Workout Routines", "Educational Science Videos", "Gaming Highlights Compilation", "Art and Design Inspiration",
            "Technology Reviews and News", "Nature Documentary Collection"
        };

        var channelNames = new[]
        {
            "MusicHub", "CodeLearning", "TravelWorld", "ChefAcademy", "FitLife", "ScienceExplorer", "GameZone", "CreativeStudio", "TechNews", "NatureFilms"
        };

        var selectedTitle = playlistTitles[random.Next(playlistTitles.Length)];
        var selectedChannel = channelNames[random.Next(channelNames.Length)];
        var videoCount = random.Next(10, 100);

        // 生成播放列表中的视频
        var videos = new List<object>();
        for (var i = 0; i < Math.Min(videoCount, 20); i++) // 最多返回20个视频作为示例
        {
            var videoId = GenerateRandomVideoId();
            videos.Add(new
            {
                id = videoId,
                title = $"Video {i + 1}: {GenerateRandomVideoTitle()}",
                duration = random.Next(60, 1800), // 1分钟到30分钟
                thumbnailUrl = $"https://i.ytimg.com/vi/{videoId}/mqdefault.jpg",
                channelName = selectedChannel,
                uploadDate = DateTime.UtcNow.AddDays(-random.Next(1, 365)).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                viewCount = random.Next(1000, 1000000),
                selected = true // 默认选中
            });
        }

        return new
        {
            id = playlistId,
            title = selectedTitle,
            description =
                $"A curated collection of {selectedTitle.ToLower()} featuring the best content from {selectedChannel}. " +
                "This playlist is regularly updated with new and exciting videos.",
            channelName = selectedChannel,
            channelUrl = $"https://www.youtube.com/@{selectedChannel}",
            videoCount,
            totalDuration = videos.Sum(v => (int)v.GetType().GetProperty("duration")!.GetValue(v)!),
            thumbnailUrl = $"https://i.ytimg.com/vi/{videos.FirstOrDefault()?.GetType().GetProperty("id")?.GetValue(videos.First())}/mqdefault.jpg",
            createdAt = DateTime.UtcNow.AddDays(-random.Next(30, 365)).ToString("yyyy-MM-ddTHH:mm:ssZ"),
            updatedAt = DateTime.UtcNow.AddDays(-random.Next(1, 30)).ToString("yyyy-MM-ddTHH:mm:ssZ"),
            videos = videos.ToArray()
        };
    }

    private object GenerateChannelData(string channelId)
    {
        var random = new Random(channelId.GetHashCode());

        var channelNames = new[]
        {
            "TechReview Pro", "Cooking Masters", "Travel Explorer", "Fitness Guru", "Science Hub", "Gaming Zone", "Art Studio", "Music World",
            "Education Plus", "Nature Films"
        };

        var channelDescriptions = new[]
        {
            "Welcome to our channel! We create amazing content for our viewers.", "Join us on this incredible journey of discovery and learning.",
            "Your go-to source for the latest and greatest content.", "Bringing you high-quality videos every week.",
            "Subscribe for daily updates and exclusive content."
        };

        var selectedName = channelNames[random.Next(channelNames.Length)];
        var selectedDescription = channelDescriptions[random.Next(channelDescriptions.Length)];
        var subscriberCount = random.Next(10000, 5000000);
        var videoCount = random.Next(50, 1000);

        // 生成频道的最新视频
        var recentVideos = new List<object>();
        for (var i = 0; i < Math.Min(videoCount, 15); i++) // 最多返回15个最新视频
        {
            var videoId = GenerateRandomVideoId();
            recentVideos.Add(new
            {
                id = videoId,
                title = GenerateRandomVideoTitle(),
                duration = random.Next(120, 3600), // 2分钟到1小时
                thumbnailUrl = $"https://i.ytimg.com/vi/{videoId}/mqdefault.jpg",
                uploadDate = DateTime.UtcNow.AddDays(-random.Next(1, 180)).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                viewCount = random.Next(1000, 500000),
                selected = true // 默认选中
            });
        }

        return new
        {
            id = channelId,
            name = selectedName,
            handle = $"@{selectedName.Replace(" ", "").ToLower()}",
            description = selectedDescription,
            subscriberCount,
            videoCount,
            totalViews = random.Next(1000000, 100000000),
            joinedDate = DateTime.UtcNow.AddDays(-random.Next(365, 3650)).ToString("yyyy-MM-ddTHH:mm:ssZ"), // 1-10年前
            avatarUrl = $"https://yt3.ggpht.com/channel/{channelId}/avatar.jpg",
            bannerUrl = $"https://yt3.ggpht.com/channel/{channelId}/banner.jpg",
            verified = random.Next(100) < 30, // 30%概率是认证频道
            recentVideos = recentVideos.ToArray()
        };
    }

    private string GenerateRandomVideoId()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 11).Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private string GenerateRandomVideoTitle()
    {
        var random = new Random();
        var adjectives = new[] { "Amazing", "Incredible", "Ultimate", "Best", "Top", "Epic", "Awesome", "Perfect", "Complete", "Advanced" };
        var nouns = new[] { "Tutorial", "Guide", "Tips", "Tricks", "Review", "Compilation", "Collection", "Masterclass", "Workshop", "Course" };
        var topics = new[] { "Programming", "Cooking", "Travel", "Fitness", "Music", "Art", "Gaming", "Science", "Technology", "Nature" };

        var adjective = adjectives[random.Next(adjectives.Length)];
        var noun = nouns[random.Next(nouns.Length)];
        var topic = topics[random.Next(topics.Length)];

        return $"{adjective} {topic} {noun} - {DateTime.Now.Year}";
    }
}