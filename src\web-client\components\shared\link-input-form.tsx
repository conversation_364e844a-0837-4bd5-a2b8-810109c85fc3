'use client';

import { useCallback, useState } from 'react';
import { useRouter } from 'next/navigation';
import { SingleLinkInput } from './link-input/single-link-input';
import { BatchLinkInput } from './link-input/batch-link-input';
import {
  batchProcessingSteps,
  defaultProcessingSteps,
  ProgressIndicator,
} from './progress-indicator';
import { analyzeUrl } from '@/lib/youtube-utils';
import { createBatchJob } from '@/lib/api';

// 链接格式化函数 - 清理链接参数
const formatYouTubeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);

    // 保留的参数列表
    const allowedParams = ['v', 'list', 'index', 't', 'start', 'end'];
    const newSearchParams = new URLSearchParams();

    // 只保留允许的参数
    allowedParams.forEach((param) => {
      const value = urlObj.searchParams.get(param);
      if (value) {
        newSearchParams.set(param, value);
      }
    });

    // 重建URL
    urlObj.search = newSearchParams.toString();
    return urlObj.toString();
  } catch {
    // 如果URL无效，返回原始字符串
    return url;
  }
};

interface LinkInputFormProps {
  placeholder?: string;
}

export function LinkInputForm({ placeholder }: LinkInputFormProps = {}) {
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [processingSteps, setProcessingSteps] = useState(defaultProcessingSteps);
  const [currentStep, setCurrentStep] = useState<string>('');
  const router = useRouter();

  // 输入变化处理
  const handleInputChange = useCallback(
    (value: string) => {
      // 自动格式化YouTube链接
      const formattedValue = value.trim() ? formatYouTubeUrl(value.trim()) : value;
      setInputValue(formattedValue);
      if (errorMessage) {
        setErrorMessage('');
      }
    },
    [errorMessage],
  );

  // 清空输入
  const handleClearInput = useCallback(() => {
    setInputValue('');
    setErrorMessage('');
  }, []);

  // 文件上传处理
  const handleFileUpload = useCallback(
    async (file: File) => {
      if (file.type !== 'text/plain' && !file.name.endsWith('.txt')) {
        setErrorMessage('请上传TXT格式文件');
        return;
      }

      try {
        const text = await file.text();

        // 如果当前有内容，显示确认对话框
        if (inputValue.trim() && !confirm('即将覆盖当前输入，是否继续？')) {
          return;
        }

        setInputValue(text);
        setErrorMessage('');
      } catch {
        setErrorMessage('文件读取失败，请重试');
      }
    },
    [inputValue],
  );

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      const txtFile = files.find((f) => f.type === 'text/plain' || f.name.endsWith('.txt'));

      if (txtFile) {
        handleFileUpload(txtFile);
      } else {
        setErrorMessage('请拖拽TXT格式文件');
      }
    },
    [handleFileUpload],
  );

  // 更新处理步骤
  const updateStep = useCallback((stepId: string, status: 'processing' | 'completed' | 'error') => {
    setProcessingSteps((prev) =>
      prev.map((step) => (step.id === stepId ? { ...step, status } : step)),
    );
    setCurrentStep(stepId);
  }, []);

  // 提交处理
  const handleSubmit = useCallback(async () => {
    setIsProcessing(true);
    setErrorMessage('');

    // 初始化步骤
    const steps = isBatchMode ? batchProcessingSteps : defaultProcessingSteps;
    setProcessingSteps(steps);

    try {
      // 步骤1: 验证链接
      updateStep('validate', 'processing');
      await new Promise((resolve) => setTimeout(resolve, 500)); // 模拟处理时间

      if (isBatchMode) {
        // 批量处理逻辑
        const lines = inputValue.split('\n').filter((line) => line.trim() !== '');
        const validLinks: string[] = [];

        lines.forEach((line) => {
          const res = analyzeUrl(line);
          if (res.type === 'VIDEO' || res.type === 'SHORTS') {
            validLinks.push(line);
          }
        });

        updateStep('validate', 'completed');

        // 步骤2: 创建批量任务
        updateStep('create', 'processing');
        await new Promise((resolve) => setTimeout(resolve, 500));

        const response = await createBatchJob({
          sourceType: 'MultiVideoList',
          sourceIdentifier: {
            videoIds: validLinks.map((link) => analyzeUrl(link).videoId),
          },
        });

        if (!response) {
          updateStep('create', 'error');
          throw new Error('创建批量任务失败，请稍后重试');
        }

        updateStep('create', 'completed');

        // 步骤3: 处理视频列表
        updateStep('process', 'processing');
        await new Promise((resolve) => setTimeout(resolve, 500));
        updateStep('process', 'completed');

        // 步骤4: 跳转页面
        updateStep('redirect', 'processing');
        updateStep('redirect', 'completed');

        router.push(`/batch/${response.batchJobId}`);
      } else {
        // 单次处理逻辑
        const linkAnalysis = analyzeUrl(inputValue);
        const { type, videoId, playlistId, channelId } = linkAnalysis;

        updateStep('validate', 'completed');

        // 步骤2: 解析视频信息
        updateStep('analyze', 'processing');
        await new Promise((resolve) => setTimeout(resolve, 500));

        switch (type) {
          case 'VIDEO':
          case 'SHORTS':
            updateStep('analyze', 'completed');

            // 步骤3: 获取下载选项
            updateStep('fetch', 'processing');
            await new Promise((resolve) => setTimeout(resolve, 500));
            updateStep('fetch', 'completed');

            // 步骤4: 跳转页面
            updateStep('redirect', 'processing');
            if (videoId) {
              updateStep('redirect', 'completed');
              router.push(`/download/${videoId}`);
            } else {
              updateStep('redirect', 'error');
              setErrorMessage('无法提取视频ID，请检查链接格式');
              setIsProcessing(false);
            }
            break;
          case 'PLAYLIST':
          case 'CHANNEL':
            const response = await createBatchJob({
              sourceType: type === 'PLAYLIST' ? 'Playlist' : 'Channel',
              sourceIdentifier: type === 'PLAYLIST' ? { playlistId } : { channelId },
            });

            if (!response) {
              throw new Error('创建批量任务失败，请稍后重试');
            }

            router.push(`/batch/${response.batchJobId}`);
            break;
          default:
            setErrorMessage('无效的链接格式，请检查后重试');
            setIsProcessing(false);
            break;
        }
      }
    } catch (error) {
      console.error('Submit failed:', error);
      setErrorMessage(error instanceof Error ? error.message : '处理失败，请稍后重试');
      setIsProcessing(false);
    }
  }, [isBatchMode, inputValue, router, updateStep]);

  // 模式切换
  const handleSwitchToBatch = useCallback(() => {
    setIsBatchMode(true);
    setErrorMessage('');
  }, []);

  const handleSwitchToSingle = useCallback(() => {
    setIsBatchMode(false);
    setErrorMessage('');
  }, []);

  return (
    <div className="space-y-6">
      {isBatchMode ? (
        <BatchLinkInput
          inputValue={inputValue}
          isProcessing={isProcessing}
          errorMessage={errorMessage}
          isDragOver={isDragOver}
          onInputChange={handleInputChange}
          onSubmit={handleSubmit}
          onSwitchToSingle={handleSwitchToSingle}
          onFileUpload={handleFileUpload}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        />
      ) : (
        <SingleLinkInput
          inputValue={inputValue}
          isProcessing={isProcessing}
          errorMessage={errorMessage}
          placeholder={placeholder}
          onInputChange={handleInputChange}
          onSubmit={handleSubmit}
          onSwitchToBatch={handleSwitchToBatch}
          onClearInput={handleClearInput}
        />
      )}

      {/* 进度指示器 */}
      {isProcessing && (
        <ProgressIndicator steps={processingSteps} currentStep={currentStep} className="mt-6" />
      )}
    </div>
  );
}
