'use client';

import { useEffect, useState } from 'react';
import { BatchJobDetails } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Activity, AlertCircle, CheckCircle, Clock, FileText, Pause } from 'lucide-react';

interface BatchJobProgressProps {
  jobData: BatchJobDetails;
  onJobUpdate: (data: Partial<BatchJobDetails>) => void;
  isConnected?: boolean;
  isRealTimeActive?: boolean;
  onToggleRealTime?: () => void;
}

export function BatchJobProgress({
  jobData,
  onJobUpdate,
  isConnected = false,
  isRealTimeActive = false,
  onToggleRealTime,
}: BatchJobProgressProps) {
  const [elapsedTime, setElapsedTime] = useState(0);

  // 计算已用时间
  useEffect(() => {
    if (!jobData.startedAt) return;

    const startTime = new Date(jobData.startedAt).getTime();
    const updateElapsed = () => {
      const now = Date.now();
      const elapsed = Math.floor((now - startTime) / 1000);
      setElapsedTime(elapsed);
    };

    updateElapsed();
    const interval = setInterval(updateElapsed, 1000);

    return () => clearInterval(interval);
  }, [jobData.startedAt]);

  // 模拟实时更新 (在实际项目中应该通过 WebSocket 接收)
  useEffect(() => {
    if (jobData.status !== 'running') return;

    const interval = setInterval(() => {
      // 模拟进度更新
      const currentProgress = jobData.progress;
      if (currentProgress < 100) {
        const newProgress = Math.min(100, currentProgress + Math.random() * 5);
        onJobUpdate({ progress: newProgress });
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [jobData.status, jobData.progress, onJobUpdate]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-5 w-5 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'partially_completed':
        return <Pause className="h-5 w-5 text-orange-500" />;
      default:
        return <Clock className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running':
        return '正在处理中...';
      case 'completed':
        return '全部完成';
      case 'failed':
        return '处理失败';
      case 'partially_completed':
        return '已暂停';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const downloadFailedList = () => {
    const failedVideos = jobData.videos.filter((v) => v.status === 'failed');
    const content = failedVideos
      .map((v) => `${v.id}: ${v.title || 'Unknown'} - ${v.error || 'Unknown error'}`)
      .join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `failed-videos-${jobData.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/5 to-secondary/5 mb-6">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon(jobData.status)}
          实时进度监控
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 整体进度 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">整体进度</span>
            <span className="text-sm text-muted-foreground">{jobData.progress.toFixed(1)}%</span>
          </div>
          <Progress value={jobData.progress} className="h-3" />
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{getStatusText(jobData.status)}</span>
            {jobData.startedAt && <span>已用时: {formatTime(elapsedTime)}</span>}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-3 bg-background/50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{jobData.stats.completed}</div>
            <div className="text-xs text-muted-foreground">已完成</div>
          </div>

          <div className="text-center p-3 bg-background/50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{jobData.stats.processing}</div>
            <div className="text-xs text-muted-foreground">处理中</div>
          </div>

          <div className="text-center p-3 bg-background/50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{jobData.stats.queued}</div>
            <div className="text-xs text-muted-foreground">排队中</div>
          </div>

          <div className="text-center p-3 bg-background/50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{jobData.stats.failed}</div>
            <div className="text-xs text-muted-foreground">失败</div>
          </div>

          <div className="text-center p-3 bg-background/50 rounded-lg">
            <div className="text-2xl font-bold text-muted-foreground">{jobData.stats.pending}</div>
            <div className="text-xs text-muted-foreground">待处理</div>
          </div>
        </div>

        {/* 状态详情和操作 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="px-3 py-1">
              总计: {jobData.stats.totalSelected} 个视频
            </Badge>

            {jobData.finishedAt && (
              <Badge variant="outline" className="px-3 py-1">
                完成时间: {new Date(jobData.finishedAt).toLocaleString()}
              </Badge>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2">
            {/* 实时连接状态 */}
            {onToggleRealTime && (
              <div className="flex items-center gap-2">
                <div
                  className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}
                />
                <span className="text-xs text-muted-foreground">
                  {isConnected ? '实时连接' : '连接断开'}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleRealTime}
                  className="h-6 px-2 text-xs">
                  {isRealTimeActive ? '暂停实时' : '启用实时'}
                </Button>
              </div>
            )}

            {jobData.stats.failed > 0 && (
              <Button variant="outline" size="sm" onClick={downloadFailedList}>
                <FileText className="mr-1 h-4 w-4" />
                下载失败列表
              </Button>
            )}
          </div>
        </div>

        {/* 最终总结 (仅在任务完成时显示) */}
        {(jobData.status === 'completed' || jobData.status === 'cancelled') && (
          <div className="rounded-lg bg-muted/50 p-4">
            <h4 className="font-medium mb-2">任务总结</h4>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• 成功处理: {jobData.stats.completed} 个视频</p>
              <p>• 处理失败: {jobData.stats.failed} 个视频</p>
              <p>
                • 总用时:{' '}
                {jobData.finishedAt && jobData.startedAt
                  ? formatTime(
                      Math.floor(
                        (new Date(jobData.finishedAt).getTime() -
                          new Date(jobData.startedAt).getTime()) /
                          1000,
                      ),
                    )
                  : '未知'}
              </p>
              {jobData.stats.failed > 0 && (
                <p className="text-amber-600">• 建议下载失败列表查看详细错误信息</p>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
