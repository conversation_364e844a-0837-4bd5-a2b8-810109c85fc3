import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  CheckCircle,
  CreditCard,
  Crown,
  Lock,
  Shield,
  Smartphone,
  Star,
  Zap,
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '结算页面 - YTDownloader',
  description: '完成您的专业版订阅，享受无限下载和高级功能。',
};

export default function CheckoutPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* 返回链接 */}
            <Link
              href="/pricing"
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors mb-8">
              <ArrowLeft className="h-4 w-4" />
              返回价格页面
            </Link>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 左侧：订单信息 */}
              <div className="space-y-6">
                <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-950/20 dark:to-pink-950/20">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Crown className="h-5 w-5 text-purple-600" />
                      专业版订阅
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium">YTDownloader 专业版</span>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-purple-600">¥9.9</div>
                        <div className="text-sm text-muted-foreground line-through">¥29.9</div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-red-100 to-orange-100 dark:from-red-900/30 dark:to-orange-900/30 p-3 rounded-lg">
                      <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
                        <Star className="h-4 w-4" />
                        <span className="text-sm font-medium">首月特价 - 省67%</span>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h4 className="font-medium">包含功能：</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          无限下载次数
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          4K超高清画质
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          批量处理功能
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          无广告纯净体验
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          24/7优先客服支持
                        </li>
                      </ul>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                        <Shield className="h-4 w-4" />
                        <span className="text-sm">30天无条件退款保证</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 安全保障 */}
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Lock className="h-5 w-5 text-green-600" />
                      <span className="font-medium">安全支付保障</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                      <div>✓ SSL加密传输</div>
                      <div>✓ 银行级安全</div>
                      <div>✓ 隐私保护</div>
                      <div>✓ 随时取消</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 右侧：支付表单 */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>选择支付方式</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 支付方式选择 */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-4 border-2 border-primary rounded-lg bg-primary/5">
                        <input
                          type="radio"
                          id="alipay"
                          name="payment"
                          defaultChecked
                          className="w-4 h-4 text-primary"
                        />
                        <label
                          htmlFor="alipay"
                          className="flex items-center gap-3 cursor-pointer flex-1">
                          <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                            <Smartphone className="h-4 w-4 text-white" />
                          </div>
                          <span className="font-medium">支付宝</span>
                        </label>
                      </div>

                      <div className="flex items-center space-x-3 p-4 border rounded-lg hover:border-primary/50 transition-colors">
                        <input
                          type="radio"
                          id="wechat"
                          name="payment"
                          className="w-4 h-4 text-primary"
                        />
                        <label
                          htmlFor="wechat"
                          className="flex items-center gap-3 cursor-pointer flex-1">
                          <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center">
                            <Smartphone className="h-4 w-4 text-white" />
                          </div>
                          <span className="font-medium">微信支付</span>
                        </label>
                      </div>

                      <div className="flex items-center space-x-3 p-4 border rounded-lg hover:border-primary/50 transition-colors">
                        <input
                          type="radio"
                          id="card"
                          name="payment"
                          className="w-4 h-4 text-primary"
                        />
                        <label
                          htmlFor="card"
                          className="flex items-center gap-3 cursor-pointer flex-1">
                          <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                            <CreditCard className="h-4 w-4 text-white" />
                          </div>
                          <span className="font-medium">信用卡</span>
                        </label>
                      </div>
                    </div>

                    <Separator />

                    {/* 联系信息 */}
                    <div className="space-y-4">
                      <h4 className="font-medium">联系信息</h4>
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <Label htmlFor="email">邮箱地址</Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">手机号码（可选）</Label>
                          <Input id="phone" type="tel" placeholder="13800138000" className="mt-1" />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* 总计 */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-lg">
                        <span>小计</span>
                        <span>¥9.9</span>
                      </div>
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>税费</span>
                        <span>¥0.0</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between text-xl font-bold">
                        <span>总计</span>
                        <span className="text-purple-600">¥9.9</span>
                      </div>
                    </div>

                    {/* 支付按钮 */}
                    <Button
                      className="w-full h-12 text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all duration-300"
                      size="lg">
                      <div className="flex items-center gap-2">
                        <Zap className="h-5 w-5" />
                        立即支付 ¥9.9
                        <Crown className="h-5 w-5" />
                      </div>
                    </Button>

                    <p className="text-xs text-center text-muted-foreground">
                      点击支付即表示您同意我们的
                      <Link href="/terms" className="text-primary hover:underline">
                        服务条款
                      </Link>
                      和
                      <Link href="/privacy" className="text-primary hover:underline">
                        隐私政策
                      </Link>
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
