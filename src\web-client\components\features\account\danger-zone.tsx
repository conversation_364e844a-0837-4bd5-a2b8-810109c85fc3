'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, Eye, EyeOff, Trash2 } from 'lucide-react';
import { deleteAccount } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

interface DangerZoneProps {
  user: User;
}

export function DangerZone({ user }: DangerZoneProps) {
  const [deleteForm, setDeleteForm] = useState({
    password: '',
    reason: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const router = useRouter();
  const { toast } = useToast();
  const { logout } = useAuth();

  const validateDeleteForm = () => {
    const newErrors: Record<string, string> = {};

    if (!deleteForm.password) {
      newErrors.password = '请输入密码以确认身份';
    }

    if (confirmText !== 'DELETE') {
      newErrors.confirmText = '请输入 DELETE 以确认删除';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDeleteAccount = async () => {
    if (!validateDeleteForm()) {
      return;
    }

    setIsDeleting(true);
    try {
      const success = await deleteAccount({
        password: deleteForm.password,
        reason: deleteForm.reason || undefined,
      });

      if (success) {
        toast({
          title: '账户已删除',
          description: '您的账户和所有相关数据已被永久删除',
        });

        // 登出并重定向到首页
        await logout();
        router.push('/');
      } else {
        toast({
          title: '删除失败',
          description: '密码错误或删除过程中出现问题',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: '删除失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field === 'confirmText') {
      setConfirmText(value);
    } else {
      setDeleteForm((prev) => ({ ...prev, [field]: value }));
    }

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="space-y-6">
      {/* 危险操作警告 */}
      <Card className="border-destructive/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            危险操作区域
          </CardTitle>
          <CardDescription>以下操作不可逆转，请谨慎操作</CardDescription>
        </CardHeader>
      </Card>

      {/* 删除账户 */}
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            删除账户
          </CardTitle>
          <CardDescription>永久删除您的账户和所有相关数据</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-destructive/10 p-4">
            <h4 className="font-medium text-destructive mb-2">删除账户将会：</h4>
            <ul className="text-sm text-destructive/80 space-y-1">
              <li>• 永久删除您的个人资料和账户信息</li>
              <li>• 删除所有下载历史和任务记录</li>
              <li>• 取消所有正在进行的下载任务</li>
              <li>• 如果您是专业版用户，将取消订阅</li>
              <li>• 此操作无法撤销</li>
            </ul>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deletePassword">确认密码</Label>
              <div className="relative">
                <Input
                  id="deletePassword"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入您的密码以确认身份"
                  value={deleteForm.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className={`pr-10 ${errors.password ? 'border-destructive' : ''}`}
                  disabled={isDeleting}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isDeleting}>
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="deleteReason">删除原因（可选）</Label>
              <Textarea
                id="deleteReason"
                placeholder="请告诉我们您删除账户的原因，这将帮助我们改进服务"
                value={deleteForm.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                disabled={isDeleting}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmText">
                请输入{' '}
                <code className="bg-muted px-1 py-0.5 rounded text-destructive font-mono">
                  DELETE
                </code>{' '}
                以确认删除
              </Label>
              <Input
                id="confirmText"
                placeholder="DELETE"
                value={confirmText}
                onChange={(e) => handleInputChange('confirmText', e.target.value)}
                className={errors.confirmText ? 'border-destructive' : ''}
                disabled={isDeleting}
              />
              {errors.confirmText && (
                <p className="text-sm text-destructive">{errors.confirmText}</p>
              )}
            </div>
          </div>

          <Separator />

          <div className="flex justify-end">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  disabled={isDeleting || !deleteForm.password || confirmText !== 'DELETE'}>
                  {isDeleting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      删除中...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除我的账户
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center gap-2 text-destructive">
                    <AlertTriangle className="h-5 w-5" />
                    确认删除账户
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    您即将永久删除账户 <strong>{user.email}</strong>。
                    此操作无法撤销，所有数据将被永久删除。
                    <br />
                    <br />
                    您确定要继续吗？
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAccount}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                    {isDeleting ? '删除中...' : '确认删除'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>

      {/* 数据导出（未来功能） */}
      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            数据导出
          </CardTitle>
          <CardDescription>导出您的账户数据（即将推出）</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            您可以请求导出您的所有账户数据，包括下载历史、设置和个人信息。
          </p>
          <Button variant="outline" disabled>
            请求数据导出
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
