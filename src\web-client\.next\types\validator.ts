// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../app/(auth)/forgot-password/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/forgot-password/page.js")
  handler satisfies AppPageConfig<"/forgot-password">
}

// Validate ../../app/(auth)/login/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/login/page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ../../app/(auth)/register/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/register/page.js")
  handler satisfies AppPageConfig<"/register">
}

// Validate ../../app/(auth)/reset-password/page.tsx
{
  const handler = {} as typeof import("../../app/(auth)/reset-password/page.js")
  handler satisfies AppPageConfig<"/reset-password">
}

// Validate ../../app/(legal)/disclaimer/page.tsx
{
  const handler = {} as typeof import("../../app/(legal)/disclaimer/page.js")
  handler satisfies AppPageConfig<"/disclaimer">
}

// Validate ../../app/(legal)/dmca/page.tsx
{
  const handler = {} as typeof import("../../app/(legal)/dmca/page.js")
  handler satisfies AppPageConfig<"/dmca">
}

// Validate ../../app/(legal)/privacy/page.tsx
{
  const handler = {} as typeof import("../../app/(legal)/privacy/page.js")
  handler satisfies AppPageConfig<"/privacy">
}

// Validate ../../app/(legal)/terms/page.tsx
{
  const handler = {} as typeof import("../../app/(legal)/terms/page.js")
  handler satisfies AppPageConfig<"/terms">
}

// Validate ../../app/[tool-slug]/page.tsx
{
  const handler = {} as typeof import("../../app/[tool-slug]/page.js")
  handler satisfies AppPageConfig<"/[tool-slug]">
}

// Validate ../../app/account/page.tsx
{
  const handler = {} as typeof import("../../app/account/page.js")
  handler satisfies AppPageConfig<"/account">
}

// Validate ../../app/batch/[batchJobId]/page.tsx
{
  const handler = {} as typeof import("../../app/batch/[batchJobId]/page.js")
  handler satisfies AppPageConfig<"/batch/[batchJobId]">
}

// Validate ../../app/checkout/page.tsx
{
  const handler = {} as typeof import("../../app/checkout/page.js")
  handler satisfies AppPageConfig<"/checkout">
}

// Validate ../../app/download/[videoId]/page.tsx
{
  const handler = {} as typeof import("../../app/download/[videoId]/page.js")
  handler satisfies AppPageConfig<"/download/[videoId]">
}

// Validate ../../app/my-downloads/page.tsx
{
  const handler = {} as typeof import("../../app/my-downloads/page.js")
  handler satisfies AppPageConfig<"/my-downloads">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../app/pricing/page.tsx
{
  const handler = {} as typeof import("../../app/pricing/page.js")
  handler satisfies AppPageConfig<"/pricing">
}

// Validate ../../app/api/auth/[...nextauth]/route.ts
{
  const handler = {} as typeof import("../../app/api/auth/[...nextauth]/route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/[...nextauth]">
}





// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
