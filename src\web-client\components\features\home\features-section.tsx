import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Clapperboard,
  Cloud,
  Download,
  Languages,
  Scissors,
  Shield,
  Smartphone,
  Zap,
} from 'lucide-react';

const features = [
  {
    icon: <Download className="h-8 w-8" />,
    title: '高清视频下载',
    description: '支持最高 8K 分辨率，多种格式选择，保留最佳画质与音质。',
    stats: '支持 4K/8K',
    color: 'from-blue-500 to-cyan-500',
    formats: ['MP4', 'WEBM', 'AVI'],
  },
  {
    icon: <Clapperboard className="h-8 w-8" />,
    title: '强大批量处理',
    description: '一键处理播放列表或整个频道，自动化完成批量内容获取。',
    stats: '最多 1000 个',
    color: 'from-purple-500 to-pink-500',
    formats: ['播放列表', '频道', '批量链接'],
  },
  {
    icon: <Languages className="h-8 w-8" />,
    title: '独家多语言字幕',
    description: '下载官方、自动生成及双语字幕，满足不同语言学习和观看需求。',
    stats: '100+ 语言',
    color: 'from-green-500 to-emerald-500',
    formats: ['SRT', 'VTT', 'ASS'],
  },
  {
    icon: <Scissors className="h-8 w-8" />,
    title: '在线剪辑与创作',
    description: '无需下载，在线完成视频剪辑、GIF 制作和铃声创作。',
    stats: '云端处理',
    color: 'from-orange-500 to-red-500',
    formats: ['GIF', 'MP3', '剪辑'],
  },
  {
    icon: <Shield className="h-8 w-8" />,
    title: '隐私安全保护',
    description: '无需注册登录，不存储个人信息，所有处理均在云端安全进行。',
    stats: '零存储',
    color: 'from-indigo-500 to-purple-500',
    formats: ['匿名', '加密', '安全'],
  },
  {
    icon: <Zap className="h-8 w-8" />,
    title: '极速下载体验',
    description: '采用多线程技术和CDN加速，提供业界领先的下载速度。',
    stats: '10倍提速',
    color: 'from-yellow-500 to-orange-500',
    formats: ['多线程', 'CDN', '加速'],
  },
  {
    icon: <Smartphone className="h-8 w-8" />,
    title: '全平台兼容',
    description: '完美支持手机、平板、电脑等所有设备，响应式设计适配各种屏幕。',
    stats: '全设备',
    color: 'from-teal-500 to-green-500',
    formats: ['手机', '平板', '电脑'],
  },
  {
    icon: <Cloud className="h-8 w-8" />,
    title: '智能云端处理',
    description: '强大的云端服务器集群，自动负载均衡，确保稳定高效的处理能力。',
    stats: '99.9%可用',
    color: 'from-sky-500 to-blue-500',
    formats: ['集群', '负载均衡', '高可用'],
  },
];

export function FeaturesSection() {
  return (
    <section id="features" className="container mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold md:text-4xl">强大功能，一应俱全</h2>
        <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
          从高清视频到多语言字幕，从单个文件到批量处理，满足您的所有需求
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {features.map((feature) => (
          <div
            key={feature.title}
            className="transform transition-all duration-300 hover:scale-105">
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-background to-muted/20 shadow-lg hover:shadow-xl transition-all duration-300">
              {/* 渐变背景装饰 */}
              <div
                className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${feature.color} opacity-10 rounded-full blur-2xl`}
              />

              <CardHeader className="relative">
                {/* 图标和统计 */}
                <div className="flex items-center justify-between mb-4">
                  <div
                    className={`p-3 rounded-xl bg-gradient-to-br ${feature.color} text-white shadow-lg transition-transform duration-200 group-hover:scale-110 group-hover:rotate-6`}>
                    {feature.icon}
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {feature.stats}
                  </Badge>
                </div>

                <CardTitle className="text-xl mb-3">{feature.title}</CardTitle>
                <CardDescription className="text-sm leading-relaxed mb-4">
                  {feature.description}
                </CardDescription>

                {/* 支持格式 */}
                <div className="flex flex-wrap gap-1">
                  {feature.formats.map((format, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {format}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
            </Card>
          </div>
        ))}
      </div>

      {/* 底部统计 */}
      <div className="mt-16 text-center">
        <p className="text-sm text-muted-foreground mb-4">已为全球用户提供服务</p>
        <div className="flex items-center justify-center gap-8 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span>实时在线处理</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
            <span>24/7 可用</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
            <span>无需注册</span>
          </div>
        </div>
      </div>
    </section>
  );
}
