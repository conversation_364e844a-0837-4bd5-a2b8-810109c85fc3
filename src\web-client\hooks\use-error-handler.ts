'use client';

import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { AuthError, AuthErrorAnalyzer, AuthErrorCode } from '@/lib/errors';

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  customMessage?: string;
  onError?: (error: AuthError) => void;
}

export function useErrorHandler() {
  const { toast } = useToast();

  const handleError = useCallback(
    (error: any, options: ErrorHandlerOptions = {}): AuthError => {
      const { showToast = true, logError = true, customMessage, onError } = options;

      // 分析错误
      const authError = AuthErrorAnalyzer.analyzeError(error);

      // 记录错误日志
      if (logError) {
        console.error('Auth Error:', {
          code: authError.code,
          message: authError.message,
          userMessage: authError.userMessage,
          details: authError.details,
          timestamp: new Date().toISOString(),
        });

        // 在生产环境中，这里可以发送到错误监控服务
        if (process.env.NODE_ENV === 'production') {
          // 例如：Sentry.captureException(authError)
        }
      }

      // 显示用户友好的错误提示
      if (showToast) {
        const message = customMessage || authError.userMessage;

        toast({
          title: getErrorTitle(authError.code),
          description: message,
          variant: getErrorVariant(authError.code),
          duration: getErrorDuration(authError.code),
        });
      }

      // 执行自定义错误处理
      if (onError) {
        onError(authError);
      }

      return authError;
    },
    [toast],
  );

  const handleAsyncError = useCallback(
    async <T>(
      operation: () => Promise<T>,
      options: ErrorHandlerOptions = {},
    ): Promise<T | null> => {
      try {
        return await operation();
      } catch (error) {
        handleError(error, options);
        return null;
      }
    },
    [handleError],
  );

  return {
    handleError,
    handleAsyncError,
  };
}

// 根据错误类型获取标题
function getErrorTitle(code: AuthErrorCode): string {
  switch (code) {
    case AuthErrorCode.NETWORK_ERROR:
    case AuthErrorCode.TIMEOUT_ERROR:
      return '网络错误';

    case AuthErrorCode.INVALID_CREDENTIALS:
    case AuthErrorCode.USER_NOT_FOUND:
      return '登录失败';

    case AuthErrorCode.EMAIL_ALREADY_EXISTS:
      return '注册失败';

    case AuthErrorCode.SESSION_EXPIRED:
    case AuthErrorCode.INVALID_SESSION:
      return '会话过期';

    case AuthErrorCode.VALIDATION_ERROR:
    case AuthErrorCode.WEAK_PASSWORD:
    case AuthErrorCode.INVALID_EMAIL:
      return '输入错误';

    case AuthErrorCode.SERVER_ERROR:
    case AuthErrorCode.SERVICE_UNAVAILABLE:
      return '服务器错误';

    case AuthErrorCode.MIGRATION_FAILED:
      return '数据迁移失败';

    case AuthErrorCode.ACCOUNT_DISABLED:
      return '账户问题';

    default:
      return '操作失败';
  }
}

// 根据错误类型获取提示样式
function getErrorVariant(code: AuthErrorCode): 'default' | 'destructive' {
  switch (code) {
    case AuthErrorCode.SESSION_EXPIRED:
    case AuthErrorCode.ANONYMOUS_USER_NOT_FOUND:
      return 'default'; // 这些是正常的业务流程

    default:
      return 'destructive'; // 其他都是错误
  }
}

// 根据错误类型获取显示时长
function getErrorDuration(code: AuthErrorCode): number {
  switch (code) {
    case AuthErrorCode.NETWORK_ERROR:
    case AuthErrorCode.SERVER_ERROR:
    case AuthErrorCode.SERVICE_UNAVAILABLE:
      return 6000; // 网络和服务器错误显示更久

    case AuthErrorCode.SESSION_EXPIRED:
      return 4000; // 会话过期中等时长

    default:
      return 5000; // 默认5秒
  }
}
