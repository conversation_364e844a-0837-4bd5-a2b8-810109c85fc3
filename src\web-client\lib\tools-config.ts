import {
  AudioLines,
  FileText,
  Image,
  ImagePlay,
  ListVideo,
  LucideIcon,
  MessageSquare,
  Music,
  Scissors,
  Smartphone,
  Subtitles,
  TvMinimalPlay,
  Video,
} from 'lucide-react';

export interface ToolConfig {
  slug: string;
  title: string;
  description: string;
  metaTitle: string;
  metaDescription: string;
  category: 'converter' | 'downloader' | 'tool';
  icon: string;
  iconComponent: LucideIcon;
  features: string[];
  inputPlaceholder: string;
  supportedFormats?: string[];
}

export const TOOLS_CONFIG: Record<string, ToolConfig> = {
  'youtube-to-mp4-converter': {
    slug: 'youtube-to-mp4-converter',
    title: 'YouTube to MP4 Converter',
    description: '将YouTube视频转换为高质量MP4格式，支持多种分辨率选择',
    metaTitle: 'YouTube to MP4 Converter - 免费在线视频转换器',
    metaDescription:
      '免费将YouTube视频转换为MP4格式。支持1080p、720p、480p等多种分辨率，快速下载，无需注册。',
    category: 'converter',
    icon: '🎬',
    iconComponent: Video,
    features: ['支持1080p高清画质', '快速转换处理', '保持原始音频质量', '无水印下载'],
    inputPlaceholder: '粘贴YouTube视频链接，转换为MP4格式...',
    supportedFormats: ['MP4', '1080p', '720p', '480p', '360p'],
  },
  'youtube-to-mp3-converter': {
    slug: 'youtube-to-mp3-converter',
    title: 'YouTube to MP3 Converter',
    description: '从YouTube视频中提取音频，转换为高品质MP3格式',
    metaTitle: 'YouTube to MP3 Converter - 免费音频提取工具',
    metaDescription:
      '免费从YouTube视频提取音频并转换为MP3。支持320kbps高品质音频，快速处理，支持批量转换。',
    category: 'converter',
    icon: '🎵',
    iconComponent: Music,
    features: ['320kbps高品质音频', '快速音频提取', '支持多种比特率', '保持ID3标签信息'],
    inputPlaceholder: '粘贴YouTube视频链接，提取MP3音频...',
    supportedFormats: ['MP3', '320kbps', '256kbps', '192kbps', '128kbps'],
  },
  'youtube-playlist-downloader': {
    slug: 'youtube-playlist-downloader',
    title: 'YouTube Playlist Downloader',
    description: '批量下载YouTube播放列表中的所有视频',
    metaTitle: 'YouTube播放列表下载器 - 批量视频下载工具',
    metaDescription:
      '一键下载整个YouTube播放列表。支持批量处理，自动获取播放列表中所有视频，支持多种格式和质量选择。',
    category: 'downloader',
    icon: '📋',
    iconComponent: ListVideo,
    features: ['批量下载整个播放列表', '自动获取所有视频', '支持大型播放列表', '可选择下载格式'],
    inputPlaceholder: '粘贴YouTube播放列表链接，批量下载所有视频...',
  },
  'youtube-channel-downloader': {
    slug: 'youtube-channel-downloader',
    title: 'YouTube Channel Downloader',
    description: '下载YouTube频道中的所有视频内容',
    metaTitle: 'YouTube频道下载器 - 频道视频批量下载',
    metaDescription:
      '批量下载YouTube频道的所有视频。支持频道视频筛选，可按时间、观看量等条件过滤，高效批量处理。',
    category: 'downloader',
    icon: '📺',
    iconComponent: TvMinimalPlay,
    features: ['下载频道所有视频', '支持视频筛选', '按时间范围过滤', '智能去重处理'],
    inputPlaceholder: '粘贴YouTube频道链接，下载频道所有视频...',
  },
  'youtube-subtitle-downloader': {
    slug: 'youtube-subtitle-downloader',
    title: 'YouTube Subtitle Downloader',
    description: '下载YouTube视频的字幕文件，支持多语言',
    metaTitle: 'YouTube字幕下载器 - 多语言字幕提取工具',
    metaDescription:
      '免费下载YouTube视频字幕。支持SRT、VTT等格式，多语言字幕下载，自动翻译功能，批量处理。',
    category: 'downloader',
    icon: '📝',
    iconComponent: Subtitles,
    features: ['支持多语言字幕', '自动翻译功能', '多种字幕格式', '批量字幕下载'],
    inputPlaceholder: '粘贴YouTube视频链接，下载字幕文件...',
    supportedFormats: ['SRT', 'VTT', 'TXT'],
  },
  'youtube-comment-downloader': {
    slug: 'youtube-comment-downloader',
    title: 'YouTube Comment Downloader',
    description: '下载YouTube视频的评论内容，支持多种格式导出',
    metaTitle: 'YouTube评论下载器 - 视频评论导出工具',
    metaDescription:
      '批量下载YouTube视频评论。支持Excel、CSV、TXT格式导出，可按热门度排序，支持回复评论下载。',
    category: 'downloader',
    icon: '💬',
    iconComponent: MessageSquare,
    features: ['批量下载评论', '支持回复评论', '多种导出格式', '按热门度排序'],
    inputPlaceholder: '粘贴YouTube视频链接，下载评论内容...',
    supportedFormats: ['XLSX', 'CSV', 'TXT'],
  },
  'youtube-shorts-downloader': {
    slug: 'youtube-shorts-downloader',
    title: 'YouTube Shorts Downloader',
    description: '专门下载YouTube Shorts短视频的工具',
    metaTitle: 'YouTube Shorts下载器 - 短视频下载工具',
    metaDescription:
      '专业的YouTube Shorts下载工具。保持原始竖屏比例，高清画质，快速下载，支持批量处理短视频。',
    category: 'downloader',
    icon: '📱',
    iconComponent: Smartphone,
    features: ['专为Shorts优化', '保持竖屏比例', '高清画质下载', '快速处理'],
    inputPlaceholder: '粘贴YouTube Shorts链接，下载短视频...',
  },
  'youtube-to-gif-maker': {
    slug: 'youtube-to-gif-maker',
    title: 'YouTube to GIF Maker',
    description: '将YouTube视频片段转换为GIF动图',
    metaTitle: 'YouTube GIF制作器 - 视频转GIF工具',
    metaDescription:
      '将YouTube视频转换为GIF动图。自定义时间段，调整帧率和尺寸，创建高质量GIF，支持预览功能。',
    category: 'tool',
    icon: '🎞️',
    iconComponent: ImagePlay,
    features: ['自定义时间段', '调整帧率尺寸', '实时预览效果', '高质量输出'],
    inputPlaceholder: '粘贴YouTube视频链接，制作GIF动图...',
    supportedFormats: ['GIF'],
  },
  'youtube-to-ringtone-maker': {
    slug: 'youtube-to-ringtone-maker',
    title: 'YouTube to Ringtone Maker',
    description: '从YouTube视频制作手机铃声',
    metaTitle: 'YouTube铃声制作器 - 视频转铃声工具',
    metaDescription:
      '从YouTube视频制作个性化手机铃声。支持iPhone M4R和Android MP3格式，自定义时长，淡入淡出效果。',
    category: 'tool',
    icon: '🔔',
    iconComponent: AudioLines,
    features: ['支持iPhone/Android', '自定义铃声时长', '淡入淡出效果', '高品质音频'],
    inputPlaceholder: '粘贴YouTube视频链接，制作手机铃声...',
    supportedFormats: ['M4R', 'MP3'],
  },
  'youtube-video-cutter': {
    slug: 'youtube-video-cutter',
    title: 'YouTube Video Cutter ',
    description: '剪辑YouTube视频，创建精彩片段',
    metaTitle: 'YouTube剪辑工具 - 在线视频剪辑器',
    metaDescription:
      '在线剪辑YouTube视频。精确时间控制，保持原始质量，快速生成视频片段，支持多种输出格式。',
    category: 'tool',
    icon: '✂️',
    iconComponent: Scissors,
    features: ['精确时间控制', '保持原始质量', '快速剪辑处理', '多格式输出'],
    inputPlaceholder: '粘贴YouTube视频链接，剪辑精彩片段...',
  },
  'youtube-thumbnail-downloader': {
    slug: 'youtube-thumbnail-downloader',
    title: 'YouTube Thumbnail Downloader',
    description: '下载YouTube视频的缩略图',
    metaTitle: 'YouTube缩略图下载器 - 视频封面图片下载',
    metaDescription:
      '免费下载YouTube视频缩略图。支持多种尺寸，高清画质，JPG/PNG格式，批量下载功能。',
    category: 'downloader',
    icon: '🖼️',
    iconComponent: Image,
    features: ['多种尺寸选择', '高清画质', '批量下载', 'JPG/PNG格式'],
    inputPlaceholder: '粘贴YouTube视频链接，下载缩略图...',
    supportedFormats: ['JPG', 'PNG'],
  },
  'youtube-description-downloader': {
    slug: 'youtube-description-downloader',
    title: 'YouTube Description Downloader',
    description: '下载YouTube视频的描述信息',
    metaTitle: 'YouTube描述下载器 - 视频描述文本提取',
    metaDescription:
      '提取并下载YouTube视频描述。支持TXT格式导出，批量处理，保持原始格式，快速获取视频信息。',
    category: 'downloader',
    icon: '📄',
    iconComponent: FileText,
    features: ['完整描述提取', '保持原始格式', '批量处理', 'TXT格式导出'],
    inputPlaceholder: '粘贴YouTube视频链接，下载视频描述...',
    supportedFormats: ['TXT'],
  },
};

export function getToolConfig(slug: string): ToolConfig | null {
  return TOOLS_CONFIG[slug] || null;
}

export function getAllTools(): ToolConfig[] {
  return Object.values(TOOLS_CONFIG);
}
