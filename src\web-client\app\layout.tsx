import { <PERSON><PERSON>, <PERSON><PERSON>_Mono as <PERSON><PERSON><PERSON><PERSON> } from 'next/font/google';

import { SessionProvider } from 'next-auth/react';
import { Toaster } from '@/components/ui/sonner';
import { BackToTop } from '@/components/ui/back-to-top';
import './globals.css';

const roboto = Roboto({
  variable: '--font-roboto',
  subsets: ['latin'],
  weight: ['100', '300', '400', '500', '700', '900'],
  display: 'swap',
});

const robotoMono = RobotoMono({
  variable: '--font-roboto-mono',
  subsets: ['latin'],
});

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${roboto.variable} ${robotoMono.variable} font-sans antialiased`}>
        <SessionProvider>
          {children}
          <Toaster />
          <BackToTop />
        </SessionProvider>
      </body>
    </html>
  );
}
