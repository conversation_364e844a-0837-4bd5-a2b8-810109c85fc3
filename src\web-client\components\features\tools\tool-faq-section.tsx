import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Download, Shield, TrendingUp, Zap } from 'lucide-react';

export function ToolFaqSection() {
  const faqItems = [
    {
      icon: <Zap className="h-4 w-4 text-white" />,
      title: '是否免费使用？',
      content: '基础功能完全免费。专业版提供更高质量、更多格式选择和批量处理功能。',
      gradient: 'from-emerald-500 to-teal-500',
      bgGradient: 'from-white to-emerald-50/30 dark:from-gray-900 dark:to-emerald-900/10',
    },
    {
      icon: <Clock className="h-4 w-4 text-white" />,
      title: '处理速度如何？',
      content: '通常在30秒内完成处理。具体时间取决于视频长度、选择的质量和当前服务器负载。',
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10',
    },
    {
      icon: <Shield className="h-4 w-4 text-white" />,
      title: '数据安全吗？',
      content: '我们采用端到端加密，不存储任何个人信息。处理完成后立即删除所有临时文件。',
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-white to-purple-50/30 dark:from-gray-900 dark:to-purple-900/10',
    },
    {
      icon: <Download className="h-4 w-4 text-white" />,
      title: '支持哪些格式？',
      content: '支持MP4、MP3、AVI、MOV等主流格式，以及1080p、720p、480p等多种分辨率选择。',
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-white to-orange-50/30 dark:from-gray-900 dark:to-orange-900/10',
    },
    {
      icon: <TrendingUp className="h-4 w-4 text-white" />,
      title: '有使用限制吗？',
      content: '免费用户每天可处理20个视频。注册用户可享受更高的使用限额和优先处理。',
      gradient: 'from-indigo-500 to-purple-500',
      bgGradient: 'from-white to-indigo-50/30 dark:from-gray-900 dark:to-indigo-900/10',
    },
    {
      icon: <Shield className="h-4 w-4 text-white" />,
      title: '版权问题？',
      content: '请确保您有权下载相关内容。我们建议仅下载您拥有版权或获得授权的内容。',
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-white to-green-50/30 dark:from-gray-900 dark:to-green-900/10',
    },
  ];

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          {/* 标题 */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">常见问题</h2>
            <p className="text-lg text-muted-foreground">快速了解我们的服务和功能</p>
          </div>

          {/* FAQ网格 */}
          <div className="grid gap-6 md:grid-cols-2 mb-12">
            {faqItems.map((item, index) => (
              <div key={index}>
                <Card
                  className={`group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br ${item.bgGradient} h-full`}>
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg">
                      <div
                        className={`p-2 bg-gradient-to-r ${item.gradient} rounded-lg group-hover:scale-110 transition-transform duration-300`}>
                        {item.icon}
                      </div>
                      {item.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground leading-relaxed">{item.content}</p>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
