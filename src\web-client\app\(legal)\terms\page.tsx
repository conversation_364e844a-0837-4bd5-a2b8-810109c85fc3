import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { LegalContent, LegalSection, LegalSubSection } from '@/components/shared/legal-component';

export const metadata: Metadata = {
  title: '服务条款 - Youtubear',
  description: 'Youtubear 服务条款，详细说明用户权利义务、服务使用规则、免责声明等法律条文。',
};

export default function TermsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow">
        <LegalContent
          title="服务条款"
          subtitle="请仔细阅读以下条款，使用我们的服务即表示您同意遵守这些条款"
          lastUpdated="2024年1月1日">
          <LegalSection title="1. 服务概述">
            <p>
              Youtubear（以下简称"我们"或"本服务&quot;）是一个专业的YouTube内容下载平台，
              为用户提供视频、音频、字幕等内容的下载和转换服务。
            </p>
            <p>
              本服务条款（以下简称"条款"）构成您与我们之间具有法律约束力的协议。
              通过访问或使用本服务，您确认已阅读、理解并同意受本条款约束。
            </p>
          </LegalSection>

          <LegalSection title="2. 服务使用规则">
            <LegalSubSection title="2.1 合法使用">
              <p>您承诺仅将本服务用于合法目的，包括但不限于：</p>
              <ul className="list-disc pl-6 space-y-1">
                <li>个人学习和研究</li>
                <li>备份您拥有版权的内容</li>
                <li>下载公共领域或创作共用许可的内容</li>
                <li>获得版权所有者明确授权的内容</li>
              </ul>
            </LegalSubSection>

            <LegalSubSection title="2.2 禁止行为">
              <p>您不得将本服务用于以下目的：</p>
              <ul className="list-disc pl-6 space-y-1">
                <li>侵犯他人版权、商标或其他知识产权</li>
                <li>商业用途或盈利目的</li>
                <li>大规模或自动化下载</li>
                <li>传播违法、有害或不当内容</li>
                <li>干扰或破坏服务的正常运行</li>
              </ul>
            </LegalSubSection>
          </LegalSection>

          <LegalSection title="3. 用户权利与义务">
            <LegalSubSection title="3.1 用户权利">
              <ul className="list-disc pl-6 space-y-1">
                <li>免费使用基础服务功能</li>
                <li>获得技术支持和客户服务</li>
                <li>保护个人隐私和数据安全</li>
                <li>随时停止使用服务</li>
              </ul>
            </LegalSubSection>

            <LegalSubSection title="3.2 用户义务">
              <ul className="list-disc pl-6 space-y-1">
                <li>遵守相关法律法规和本条款</li>
                <li>尊重他人的知识产权</li>
                <li>不滥用或恶意使用服务</li>
                <li>承担因使用服务产生的法律责任</li>
              </ul>
            </LegalSubSection>
          </LegalSection>

          <LegalSection title="4. 版权与知识产权">
            <p>
              我们尊重知识产权，并要求用户也这样做。用户在使用本服务时，
              必须确保拥有下载内容的合法权利或已获得版权所有者的明确授权。
            </p>
            <p>
              如果您认为您的版权受到侵犯，请通过
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
              联系我们，我们将及时处理相关投诉。
            </p>
          </LegalSection>

          <LegalSection title="5. 免责声明">
            <LegalSubSection title="5.1 服务免责">
              <p>
                本服务按"现状"提供，我们不对服务的可用性、准确性、可靠性或适用性作出任何明示或暗示的保证。
              </p>
            </LegalSubSection>

            <LegalSubSection title="5.2 内容免责">
              <p>
                我们不对用户下载的内容承担任何责任，包括但不限于内容的合法性、准确性、完整性或质量。
                用户应自行承担使用下载内容的所有风险和责任。
              </p>
            </LegalSubSection>
          </LegalSection>

          <LegalSection title="6. 服务变更与终止">
            <p>
              我们保留随时修改、暂停或终止服务的权利，恕不另行通知。
              我们将尽力提前通知重大变更，但不承担因此产生的任何责任。
            </p>
          </LegalSection>

          <LegalSection title="7. 条款修改">
            <p>
              我们可能会不时修改本条款。修改后的条款将在本页面发布，
              并标注最后更新日期。继续使用服务即表示您接受修改后的条款。
            </p>
          </LegalSection>

          <LegalSection title="8. 联系方式">
            <p>如果您对本条款有任何疑问或建议，请通过以下方式联系我们：</p>
            <ul className="list-none space-y-2 mt-4">
              <li>
                <strong>邮箱：</strong> <EMAIL>
              </li>
              <li>
                <strong>客服：</strong> <EMAIL>
              </li>
            </ul>
          </LegalSection>
        </LegalContent>
      </main>
      <Footer />
    </div>
  );
}
