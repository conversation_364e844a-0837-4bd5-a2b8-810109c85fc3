'use client';

import { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Clock, Download, Eye, FileText, Globe, X } from 'lucide-react';
import { VideoPageData } from '@/lib/types';

interface SubtitlePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoData: VideoPageData;
  selectedSubtitle?: {
    langCode: string;
    langName: string;
    isAutoGenerated: boolean;
  };
}

// 模拟字幕内容预览
const mockSubtitleContent = `1
00:00:00,000 --> 00:00:03,000
欢迎观看这个精彩的视频教程

2
00:00:03,000 --> 00:00:06,500
今天我们将学习如何构建现代化的Web应用

3
00:00:06,500 --> 00:00:10,000
首先，让我们了解一下基本的概念和原理

4
00:00:10,000 --> 00:00:14,200
React是一个用于构建用户界面的JavaScript库

5
00:00:14,200 --> 00:00:18,000
它采用组件化的开发方式，让代码更加模块化`;

export function SubtitlePreviewModal({
  isOpen,
  onClose,
  videoData,
  selectedSubtitle,
}: SubtitlePreviewModalProps) {
  const [subtitleContent, setSubtitleContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, onClose]);

  // 加载字幕内容
  useEffect(() => {
    if (isOpen && selectedSubtitle) {
      setIsLoading(true);
      // 模拟API调用
      setTimeout(() => {
        setSubtitleContent(mockSubtitleContent);
        setIsLoading(false);
      }, 1000);
    }
  }, [isOpen, selectedSubtitle]);

  // 下载字幕
  const downloadSubtitle = (format: 'srt' | 'vtt') => {
    if (selectedSubtitle) {
      console.log(`Downloading ${selectedSubtitle.langName} in ${format} format`);
    }
  };

  if (!selectedSubtitle) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <DialogHeader className="p-6 pb-4 border-b">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <DialogTitle className="text-xl font-bold line-clamp-2 mb-2">
                  <FileText className="inline h-5 w-5 mr-2 text-primary" />
                  字幕预览 - {selectedSubtitle.langName}
                </DialogTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="font-medium">{videoData.title}</span>
                  <Badge variant={selectedSubtitle.isAutoGenerated ? 'secondary' : 'default'}>
                    {selectedSubtitle.isAutoGenerated ? '自动生成' : '官方字幕'}
                  </Badge>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* 主要内容区域 */}
          <div className="flex-1 flex flex-col">
            {/* 字幕信息 */}
            <div className="p-4 border-b bg-muted/30">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">{selectedSubtitle.langName}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedSubtitle.isAutoGenerated ? '自动生成字幕' : '官方字幕'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" onClick={() => downloadSubtitle('srt')} className="gap-2">
                    <Download className="h-4 w-4" />
                    SRT
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadSubtitle('vtt')}
                    className="gap-2">
                    <Download className="h-4 w-4" />
                    VTT
                  </Button>
                </div>
              </div>
            </div>

            {/* 字幕内容预览 */}
            <div className="flex-1 p-4">
              <div className="h-full">
                <div className="flex items-center gap-2 mb-3">
                  <Eye className="h-4 w-4 text-primary" />
                  <span className="font-medium">字幕预览</span>
                  <Badge variant="outline" className="text-xs">
                    SRT 格式
                  </Badge>
                </div>

                <ScrollArea className="h-[calc(100%-2rem)] border rounded-lg">
                  <div className="p-4">
                    {isLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2 text-muted-foreground">加载中...</span>
                      </div>
                    ) : (
                      <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed">
                        {subtitleContent}
                      </pre>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          </div>

          {/* 底部操作栏 */}
          <div className="p-4 border-t bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>支持 SRT、VTT 格式下载</span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>{selectedSubtitle.langName} 字幕</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" onClick={onClose}>
                  关闭
                </Button>
                <Button onClick={() => downloadSubtitle('srt')} className="gap-2">
                  <Download className="h-4 w-4" />
                  下载字幕
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
