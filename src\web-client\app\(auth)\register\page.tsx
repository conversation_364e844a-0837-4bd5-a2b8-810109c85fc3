import { Metadata } from 'next';
import { RegisterForm } from '@/components/features/auth/register-form';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '注册 - YTDownloader',
  description: '创建您的 YTDownloader 账户',
};

export default function RegisterPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">创建账户</CardTitle>
                <p className="text-muted-foreground">注册 YTDownloader 账户，享受更多功能</p>
              </CardHeader>
              <CardContent>
                <RegisterForm />
                <div className="mt-6 pt-6 border-t text-center text-sm text-muted-foreground">
                  <p>已有账户？</p>
                  <Link
                    href="/login"
                    className="font-medium text-primary hover:text-primary/80 transition-colors">
                    立即登录
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
