# 后端开发规范 (ASP.NET Core / C# 12 / EF Core)

## 1. 核心理念与原则
- **目标**: 构建高性能、简洁、现代化的 ASP.NET Core Minimal API 应用。
- **代码质量**: 确保代码健壮、可维护、安全，并遵循微软最新的最佳实践。
- **抽象原则**: 优先使用具体类。仅在为了支持测试、实现多态行为或解耦关键组件时才引入接口。
- **分层设计**: 强制执行严格的逻辑分层。实体（数据/领域层）与 DTO（应用/表示层）必须明确分离，严禁跨层直接引用不属于该层的数据结构。

## 2. 项目设置与代码风格
### 2.1. C# 语言特性与风格
- **通用风格**: 遵循官方 C# 编码约定，编写简洁、地道、高性能的代码。
- **命名约定**:
  - `PascalCase`: 类、方法、公有属性、记录类型、`static readonly` 字段。
  - `camelCase`: 局部变量、方法参数。
  - `_camelCase`: 私有字段。
  - `UPPER_SNAKE_CASE`: 常量 (`const`)。
- **语言特性 (C# 12+)**: **强制并充分利用**现代 C# 特性：
  - **`record`**: DTO **必须**使用 `record` 类型。
  - **主构造函数**: 优先使用以简化类定义。
  - **集合字面量**: **强制使用 `[]` 初始化集合** (e.g., `List<string> names = [];`)。
  - **`required`**: 用于标记对象初始化时必须赋值的属性。
- **语法糖**: 善用空值处理（`?.`, `??`）和字符串插值（`$`）。类型推断明显时优先使用 `var`。

### 2.2. 项目结构与依赖
- **文件结构**: 按类型创建子文件夹 (e.g., `Endpoints`, `Services`, `Models`)。
- **依赖注入 (DI)**: 广泛使用 ASP.NET Core 内置的 DI 容器，遵循依赖倒置原则。
- **NuGet 包**: 谨慎选择第三方库，优先使用微软官方或广泛认可的社区包，并定期审查和更新。
- **注释**: 仅在逻辑复杂或意图不明显时添加清晰、简洁的中文注释。优先使用行内注释解释“为什么”而不是“做什么”。

### 2.3. 可空引用类型 (NRT)
- **强制启用**: 项目文件 (`.csproj`) **必须**启用 `<Nullable>enable</Nullable>`。
- **使用规则**:
  - 数据库 `NOT NULL` 列对应非可空属性 (`string`, `int`)。
  - 数据库 `NULL` 列对应可空属性 (`string?`, `int?`)。
  - **单数导航属性**: 强制关系对应非可空类型，可选关系对应可空类型。
  - **集合导航属性** (`ICollection<T>`, `List<T>`): **必须是非可空的，并且必须初始化为空集合 (`= [];`)**。严禁为 `null`。

## 3. 数据层与持久化 (EF Core)
### 3.1. 实体 (Entity) 规范
- **用途**: 作为数据库表的直接映射，是数据持久化的核心。
- **特性**: **必须是贫血模型 (Anemic Model)**，仅作为数据容器，**严禁包含业务逻辑或依赖任何服务、DTO 或 UI 组件**。
- **不可变性**: 关键字段（如 `Id`, `CreatedAt`）**必须使用 `init` 访问器**以确保创建后不被修改。

### 3.2. 数据库映射 (强制 Fluent API)
- **方式**: **强制只使用 Fluent API** 进行数据库映射。**严禁在实体类上使用任何 Data Annotations** (`[Table]`, `[Key]`, `[ForeignKey]` 等)。
- **结构**:
  - 每个实体创建一个对应的 `IEntityTypeConfiguration<TEntity>` 实现类。
  - 所有配置类统一置于对应实体类同一个 `.cs` 文件中。C# 类型 (含可空性)、默认值、集合类型与数据库列约束严格一致。
  - 在 `DbContext` 的 `OnModelCreating` 方法中通过 `modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());` 自动加载所有配置。

### 3.3. 数据访问
- **访问模式**: **禁用仓储模式 (Repository Pattern)**。在需要访问数据的服务或端点处理程序中，直接注入 `DbContext`。
- **只读查询**: 所有不涉及数据修改的查询，**必须使用 `.AsNoTracking()`** 来提升性能。

### 3.4. 性能考量
- **N+1 问题**: 警惕 N+1 查询问题，**必须使用 `.Include()` / `.ThenInclude()` 或 `Select` 投影**来预加载关联数据。
- **分页**: 对可能返回大量数据的 API **必须实现服务器端分页** (`.Skip()/.Take()`)。

## 4. 应用层与 API 设计
### 4.1. 数据传输对象 (DTO) 规范
- **用途**: API 与客户端之间的数据传输载体。
- **类型**: **强制使用 `record` 类型**，利用其不可变性、基于值的相等性及简洁的语法。
- **特性**:
  - **必须是纯粹的数据容器**，不含任何业务逻辑。
  - 作为实体到客户端的投影，**仅包含客户端需要的字段**，并剔除所有敏感或不必要的数据。

### 4.2. 实体与 DTO 转换
- **机制**: **强制手动编写转换逻辑，严禁使用 AutoMapper** 或其他隐式映射库。
- **推荐方式**:
  - **实体 -> DTO**: **必须使用 LINQ 的 `.Select()` 投影**。这是最高效、最安全的方式。
  - **DTO -> 实体**: 在服务层中手动创建和填充实体对象。
- **原则**: 转换逻辑必须纯粹，严禁在此过程中引入任何业务计算或验证。

### 4.3. API 设计原则
- **RESTful**: 遵循 RESTful 设计原则进行路由（名词复数）和 HTTP 方法（仅 MapGet, MapPost）设计。
- **路由组织**: **强制使用 `MapGroup` 按功能组织 API 路由**，保持入口文件整洁。
- **横切关注点**: 优先使用 Minimal API 的 `IEndpointFilter` 处理，其次再考虑 ASP.NET Core 中间件。

### 4.4. `required` 关键字
- **作用**: 用于在编译时强制属性在对象初始化时被显式赋值。
- **协同工作**: `required` 是**编译时**赋值检查，FluentValidation 是**运行时**值有效性检查。两者协同使用，各司其职。
- **适用场景**: DTO 和实体中没有合理默认值且必须在创建时提供的字段。

## 5. 验证、错误处理与响应结构
### 5.1. 验证 (强制 FluentValidation)
- **DTO 验证 (输入验证)**:
  - **职责**: 验证客户端输入数据的格式、范围和基本结构。
  - **位置**: 验证器 (`AbstractValidator<TDto>`) **必须**与它所验证的 DTO 定义在同一个 `.cs` 文件中。
  - **执行**: 在 API 层通过 Minimal API 过滤器自动执行。
- **实体验证 (业务验证)**:
  - **职责**: 验证领域业务规则和数据库持久化前的完整性。
  - **位置**: 置于 `Validators` 文件夹内。
  - **执行**: 在服务层逻辑中、数据持久化之前显式调用。

### 5.2. 结构化响应与错误码 (i18n-Friendly)
- **核心原则**: 后端不产生面向最终用户的错误消息文本。API 响应通过结构化的错误码和上下文，将错误信息传递给前端，由前端负责本地化展示。
- **错误码格式**: **强制使用 `UPPER_SNAKE_CASE`** (e.g., `USER_NOT_FOUND`, `FIELD_IS_REQUIRED`)。
- **错误码管理**: 项目中必须有一个或多个静态类（如 `ErrorCodes.cs`）来统一定义和管理所有错误码。**错误码是后端与前端之间关于错误的唯一契约**。
- **关键类型定义 (属性列表)**:
    - **`ErrorDetail`**: 业务操作失败的详细信息
      - `Code`: `string` - 唯一的错误码 (e.g., `USER_NOT_FOUND`)。
      - `Placeholders`: `Dictionary<string, object>?` (可选) - 用于前端格式化消息的占位符。
    - **`ValidationError`**: 字段级别的验证错误
      - `Field`: `string` - 发生验证错误的字段名。
      - `Code`: `string` - 字段的错误码 (e.g., `FIELD_IS_REQUIRED`)。
      - `Placeholders`: `Dictionary<string, object>?` (可选) - 相关的上下文数据 (如最小/最大长度)。
    - **`ServiceResult<T>`** / **`ServiceResult`**: 服务层返回类型
      - `IsSuccess`: `bool` - 操作是否成功。
      - `Data`: `T?` (可选) - 成功时返回的数据。
      - `Error`: `ErrorDetail?` (可选) - 失败时的错误详情。
    - **`ApiResponse<T>`**: API 最终响应体
      - `IsSuccess`: `bool` - 请求是否成功处理。
      - `Data`: `T?` (可选) - 成功时的主响应数据。
      - `Error`: `ErrorDetail?` (可选) - 业务失败或系统错误的详情。
      - `ValidationErrors`: `List<ValidationError>?` (可选) - 输入验证失败时的字段错误列表。

### 5.3. 开发工作流
- **服务层**:
  - 方法必须返回 `ServiceResult<T>` 或 `ServiceResult`。
  - **严禁通过抛出异常来表示可预期的业务失败**。必须返回一个 `IsSuccess=false` 的 `ServiceResult`。
- **API 层 (端点处理)**:
  - **DTO 验证失败**: 过滤器自动捕获，返回 `422 Unprocessable Entity` 及 `ValidationErrors`。
  - **服务层成功**: 将 `ServiceResult` 映射为 `ApiResponse`，返回 `200 OK` / `201 Created` / `204 No Content`。
  - **服务层失败**: 将 `ServiceResult` 的 `Error` 映射到 `ApiResponse`，返回 `4xx` 状态码 (如 `404`, `409`)。
- **全局异常处理**:
  - **必须配置全局异常处理器**，捕获所有未处理的系统异常。
  - 处理器将异常统一转换为 `IsSuccess=false` 且 `Error.Code` 为 `INTERNAL_SERVER_ERROR` 的 `ApiResponse`，返回 `500 Internal Server Error`。**严禁在响应中暴露任何技术细节**。

## 6. 横切关注点
### 6.1. 安全性
- **认证/授权**: **强制使用 ASP.NET Core 的认证/授权机制**保护所有非公开端点。
- **传输安全**: 生产环境**强制要求 HTTPS**。
- **CORS**: 采用最小权限原则配置跨域策略。
- **速率限制**: 为关键或计算昂贵的 API 端点按需实现速率限制。

### 6.2. 性能
- **异步编程**: 所有 I/O 密集型操作（数据库、网络请求等）**强制使用 `async/await`**。
- **缓存**: 按需对高频读取且不经常变化的数据使用 `IMemoryCache` 。

### 6.3. 后台任务
- 使用 `IHostedService` / `BackgroundService` 实现长时间运行的后台任务。