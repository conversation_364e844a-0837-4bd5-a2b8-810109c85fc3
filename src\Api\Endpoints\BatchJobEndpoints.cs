using System.Text.Json;
using Api.Data;
using Api.Data.Models;
using Api.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Api.Endpoints;

public static class BatchJobEndpoints
{
    public static void MapBatchJobEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/batch-jobs").WithTags("Batch Jobs");

        // 创建批量作业 - 需要用户身份追踪
        group.MapPost("", async ([FromBody] CreateBatchJobRequest request, UserService userService, AppDbContext context, HttpContext httpContext) =>
        {
            // 确保用户身份存在（这会触发匿名用户创建）
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 创建批量作业
            var batchJob = new BatchJob
            {
                UserId = userIdentity.UserId!.Value,
                SourceType = request.SourceType,
                SourceIdentifier = JsonSerializer.Serialize(request.SourceIdentifier),
                Config = JsonSerializer.Serialize(request.Config ?? new { })
            };

            context.BatchJobs.Add(batchJob);
            await context.SaveChangesAsync();

            return Results.Ok(new
            {
                batchJobId = batchJob.Id, userId = userIdentity.UserId, isAnonymous = userIdentity.IsAnonymous, status = batchJob.Status.ToString()
            });
        }).WithSummary("创建批量作业").WithDescription("创建新的批量下载作业");

        // 获取批量作业详情 - 需要用户身份验证
        group.MapGet("/{batchJobId:guid}", async (Guid batchJobId, UserService userService, AppDbContext context, HttpContext httpContext) =>
        {
            // 确保用户身份存在
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 查找批量作业
            var batchJob = await context.BatchJobs.Where(bj => bj.Id == batchJobId && bj.UserId == userIdentity.UserId).FirstOrDefaultAsync();

            if (batchJob == null) return Results.NotFound(new { error = "BATCH_JOB_NOT_FOUND" });

            return Results.Ok(new
            {
                id = batchJob.Id,
                sourceType = batchJob.SourceType.ToString(),
                status = batchJob.Status.ToString(),
                progress = batchJob.Progress,
                createdAt = batchJob.CreatedAt,
                startedAt = batchJob.StartedAt,
                completedAt = batchJob.CompletedAt
            });
        }).WithSummary("获取批量作业详情").WithDescription("获取指定批量作业的详细信息");

        // 启动批量作业 - 需要用户身份验证
        group.MapPost("/{batchJobId:guid}/start", async (
            Guid batchJobId, [FromBody] StartBatchJobRequest request, UserService userService, AppDbContext context, HttpContext httpContext) =>
        {
            // 确保用户身份存在
            var userIdentity = await userService.RequireUserIdentityAsync(httpContext);

            // 查找批量作业
            var batchJob = await context.BatchJobs.Where(bj => bj.Id == batchJobId && bj.UserId == userIdentity.UserId).FirstOrDefaultAsync();

            if (batchJob == null) return Results.NotFound(new { error = "BATCH_JOB_NOT_FOUND" });

            if (batchJob.Status != BatchJobStatus.NotStarted) return Results.BadRequest(new { error = "BATCH_JOB_ALREADY_STARTED" });

            // 更新状态
            batchJob.Status = BatchJobStatus.Running;
            batchJob.StartedAt = DateTime.UtcNow;
            await context.SaveChangesAsync();

            // TODO: 实际启动批量处理逻辑

            return Results.Ok(new { success = true });
        }).WithSummary("启动批量作业").WithDescription("开始执行批量下载作业");
    }

    // DTOs
    public record CreateBatchJobRequest(BatchJobSourceType SourceType, object? SourceIdentifier = null, object? Config = null);

    public record StartBatchJobRequest(string[]? SelectedVideoIds = null, object? Config = null);
}