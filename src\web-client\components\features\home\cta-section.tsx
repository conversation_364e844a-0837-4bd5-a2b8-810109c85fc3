import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Download, Shield, Zap } from 'lucide-react';
import Link from 'next/link';

export function CtaSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-primary/10">
      <div className="container mx-auto px-4">
        <Card className="max-w-4xl mx-auto border-0 bg-gradient-to-br from-background to-primary/5 shadow-2xl overflow-hidden">
          <CardContent className="p-12 text-center relative">
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl" />

            <div className="relative z-10">
              {/* 标题区域 */}
              <div className="mb-8">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  准备好体验最佳的
                  <br />
                  <span className="bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                    YouTube 下载服务了吗？
                  </span>
                </h2>

                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  加入数百万用户的行列，享受快速、安全、免费的 YouTube 内容下载体验
                </p>
              </div>

              {/* 特色亮点 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border transition-transform duration-200 hover:scale-105">
                  <div className="p-2 bg-green-100 text-green-600 rounded-lg dark:bg-green-900 dark:text-green-400">
                    <Shield className="h-5 w-5" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-sm">100% 安全</div>
                    <div className="text-xs text-muted-foreground">无病毒，无恶意软件</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border transition-transform duration-200 hover:scale-105">
                  <div className="p-2 bg-blue-100 text-blue-600 rounded-lg dark:bg-blue-900 dark:text-blue-400">
                    <Zap className="h-5 w-5" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-sm">极速下载</div>
                    <div className="text-xs text-muted-foreground">平均 30 秒完成</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border transition-transform duration-200 hover:scale-105">
                  <div className="p-2 bg-purple-100 text-purple-600 rounded-lg dark:bg-purple-900 dark:text-purple-400">
                    <Download className="h-5 w-5" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-sm">无限下载</div>
                    <div className="text-xs text-muted-foreground">无次数限制</div>
                  </div>
                </div>
              </div>

              {/* CTA 按钮 */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Button
                  size="lg"
                  className="text-lg px-8 py-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <Link href="/#hero" className="flex items-center gap-2">
                    立即开始下载
                    <ArrowRight className="h-5 w-5" />
                  </Link>
                </Button>

                <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                  <Link href="/pricing">查看专业版</Link>
                </Button>
              </div>

              {/* 底部提示 */}
              <div className="mt-8 text-sm text-muted-foreground">
                <p>
                  🎉 <strong>限时优惠：</strong>专业版首月仅需 ¥9.9
                  <span className="ml-2 line-through text-muted-foreground/60">原价 ¥29.9</span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
