import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowRight,
  Check,
  Clock,
  Crown,
  Gift,
  RefreshCw,
  Shield,
  Star,
  Users,
  X,
  Zap,
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '价格方案 - Youtubear',
  description: '选择适合您的 Youtubear 价格方案，免费版和专业版功能对比，优惠价格，立即升级。',
};

const plans = [
  {
    name: '免费版',
    price: '¥0',
    period: '永久免费',
    description: '适合个人用户的基础下载需求',
    color: 'from-gray-500 to-gray-600',
    popular: false,
    features: [
      { name: '每日下载', value: '10个视频', included: true },
      { name: '视频质量', value: '最高1080p', included: true },
      { name: '批量下载', value: '最多5个', included: true },
      { name: '广告', value: '有广告', included: false },
      { name: '客服支持', value: '社区支持', included: true },
    ],
  },
  {
    name: '专业版',
    price: '¥9.9',
    originalPrice: '¥29.9',
    period: '首月特价',
    description: '专业用户的完整解决方案',
    color: 'from-primary to-blue-600',
    popular: true,
    features: [
      { name: '无限下载', value: '不限次数', included: true },
      { name: '4K超清', value: '最高画质', included: true },
      { name: '批量处理', value: '无限制', included: true },
      { name: '无广告', value: '纯净体验', included: true },
      { name: '优先支持', value: '24/7客服', included: true },
    ],
  },
];

const faqs = [
  {
    question: '免费版有什么限制？',
    answer: '免费版每日可下载10个视频，支持最高1080p质量，包含基础功能。适合轻度使用的个人用户。',
  },
  {
    question: '专业版包含哪些额外功能？',
    answer: '专业版提供无限下载、8K画质、无广告体验、极速下载、优先客服支持等高级功能。',
  },
  {
    question: '如何申请退款？',
    answer:
      '您可以在购买后30天内通过客服或账户中心申请退款。我们承诺24小时内处理，3-5个工作日原路退回。',
  },
  {
    question: '退款需要什么条件吗？',
    answer: '我们提供30天无条件退款保障。无论任何原因，只要在30天内申请，我们都会全额退款。',
  },
  {
    question: '如何升级到专业版？',
    answer: '点击"立即升级"按钮，选择支付方式完成付款即可立即享受专业版功能。',
  },
  {
    question: '支持哪些支付方式？',
    answer: '我们支持支付宝、微信支付、银行卡、PayPal等多种支付方式，安全便捷。',
  },
  {
    question: '可以随时取消订阅吗？',
    answer: '是的，您可以随时在账户设置中取消订阅，取消后仍可使用到当前计费周期结束。',
  },
  {
    question: '专业版价格会变化吗？',
    answer: '首月特价¥9.9，之后恢复原价¥29.9/月。已购买用户的价格在订阅期内保持不变。',
  },
  {
    question: '服务出现问题怎么办？',
    answer: '我们提供24/7客服支持，承诺99.9%服务可用性。如遇问题，我们会第一时间解决并提供补偿。',
  },
];

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-background via-muted/20 to-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h1 className="text-4xl font-bold md:text-5xl mb-4">选择适合您的方案</h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                从免费版开始体验，随时升级到专业版享受完整功能
              </p>
            </div>

            {/* 价格卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {plans.map((plan) => (
                <div key={plan.name} className="group relative">
                  {/* 背景光效 */}
                  {plan.popular && (
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200 animate-pulse"></div>
                  )}

                  <Card
                    className={`relative overflow-hidden border-2 rounded-2xl ${
                      plan.popular
                        ? 'border-transparent bg-gradient-to-br from-white via-purple-50/50 to-pink-50/50 dark:from-gray-900 dark:via-purple-950/50 dark:to-pink-950/50 shadow-2xl'
                        : 'border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-xl'
                    } transition-all duration-500 hover:scale-[1.02] group-hover:shadow-2xl`}>
                    {/* 受欢迎标签 */}
                    {plan.popular && (
                      <div className="absolute -top-0 left-1/2 transform -translate-x-1/2">
                        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 text-sm font-bold rounded-b-xl shadow-lg">
                          <div className="flex items-center gap-2">
                            <Crown className="h-4 w-4" />
                            最受欢迎
                            <Star className="h-4 w-4 animate-pulse" />
                          </div>
                        </div>
                      </div>
                    )}

                    <CardHeader className="text-center pb-6 pt-6">
                      {/* 图标 */}
                      <div className="mb-4">
                        <div
                          className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${plan.color} text-white mx-auto shadow-md`}>
                          {plan.popular ? (
                            <Crown className="h-6 w-6" />
                          ) : (
                            <Users className="h-6 w-6" />
                          )}
                        </div>
                      </div>

                      <CardTitle className="text-2xl font-bold mb-2">{plan.name}</CardTitle>

                      {/* 价格显示 */}
                      <div className="space-y-2">
                        <div className="flex items-baseline justify-center gap-2">
                          <span
                            className={`text-4xl font-black ${plan.popular ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent' : 'text-foreground'}`}>
                            {plan.price}
                          </span>
                          {plan.originalPrice && (
                            <span className="text-lg text-muted-foreground line-through">
                              {plan.originalPrice}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{plan.period}</p>
                        {plan.popular && (
                          <div className="inline-flex items-center gap-1 bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                            省67%
                          </div>
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* 功能列表 */}
                      <div className="space-y-3">
                        {plan.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-3">
                            <div
                              className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                                feature.included
                                  ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                                  : 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                              }`}>
                              {feature.included ? (
                                <Check className="h-3 w-3" />
                              ) : (
                                <X className="h-3 w-3" />
                              )}
                            </div>
                            <div className="flex-1">
                              <span className="text-sm font-medium">{feature.name}</span>
                              <span className="text-xs text-muted-foreground ml-2">
                                {feature.value}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* CTA按钮 */}
                      <div className="pt-4">
                        {plan.name === '免费版' ? (
                          <Link href="/" className="block">
                            <Button
                              variant="outline"
                              className="w-full hover:border-primary hover:text-primary transition-all duration-300"
                              size="lg">
                              <div className="flex items-center gap-2">
                                立即开始
                                <ArrowRight className="h-4 w-4" />
                              </div>
                            </Button>
                          </Link>
                        ) : (
                          <Link href="/checkout" className="block">
                            <Button
                              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all duration-300"
                              size="lg">
                              <div className="flex items-center gap-2">
                                <Crown className="h-4 w-4" />
                                立即升级
                                <Zap className="h-4 w-4" />
                              </div>
                            </Button>
                          </Link>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 服务保障 */}
        <section className="py-20 bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 mb-4">
                <Shield className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-600 uppercase tracking-wider">
                  服务保障
                </span>
              </div>
              <h2 className="text-3xl font-bold mb-4">我们的承诺</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                为您提供安心可靠的服务体验，让您无后顾之忧
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {/* 退款保障 */}
              <Card className="text-center border-0 shadow-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
                <CardContent className="p-8">
                  <div className="inline-flex p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 mb-6">
                    <RefreshCw className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-4">30天退款保障</h3>
                  <p className="text-muted-foreground mb-6">
                    如果您对我们的服务不满意，可在购买后30天内申请全额退款，无需任何理由
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-600" />
                      <span>无条件退款</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-600" />
                      <span>3-5个工作日到账</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-600" />
                      <span>原路退回</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 服务稳定性 */}
              <Card className="text-center border-0 shadow-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
                <CardContent className="p-8">
                  <div className="inline-flex p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 mb-6">
                    <Clock className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-4">99.9%稳定运行</h3>
                  <p className="text-muted-foreground mb-6">
                    我们承诺99.9%的服务可用性，确保您随时都能享受稳定的下载服务
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-blue-600" />
                      <span>24/7监控</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-blue-600" />
                      <span>多节点部署</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-blue-600" />
                      <span>自动故障转移</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 客户满意度 */}
              <Card className="text-center border-0 shadow-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
                <CardContent className="p-8">
                  <div className="inline-flex p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 mb-6">
                    <Star className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-4">4.9星用户评价</h3>
                  <p className="text-muted-foreground mb-6">
                    超过10万用户的信赖选择，平均评分4.9星，用户满意度高达98%
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-yellow-600" />
                      <span>10万+用户</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-yellow-600" />
                      <span>98%满意度</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-yellow-600" />
                      <span>专业客服</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* 退款政策详情 */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">退款政策</h2>
                <p className="text-lg text-muted-foreground">
                  我们致力于为您提供最佳的服务体验，如有不满意，我们提供灵活的退款政策
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Gift className="h-5 w-5 text-green-600" />
                      退款条件
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-green-600 mt-2"></div>
                        <div>
                          <p className="font-medium">购买后30天内</p>
                          <p className="text-sm text-muted-foreground">从付款成功之日起计算</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-green-600 mt-2"></div>
                        <div>
                          <p className="font-medium">服务未达预期</p>
                          <p className="text-sm text-muted-foreground">
                            功能不符合描述或存在重大问题
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-green-600 mt-2"></div>
                        <div>
                          <p className="font-medium">技术故障</p>
                          <p className="text-sm text-muted-foreground">
                            因我方原因导致服务无法正常使用
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <RefreshCw className="h-5 w-5 text-blue-600" />
                      退款流程
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 text-xs font-bold flex items-center justify-center">
                          1
                        </div>
                        <div>
                          <p className="font-medium">提交申请</p>
                          <p className="text-sm text-muted-foreground">
                            联系客服或在账户中心提交退款申请
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 text-xs font-bold flex items-center justify-center">
                          2
                        </div>
                        <div>
                          <p className="font-medium">审核处理</p>
                          <p className="text-sm text-muted-foreground">
                            我们将在24小时内审核您的申请
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 text-xs font-bold flex items-center justify-center">
                          3
                        </div>
                        <div>
                          <p className="font-medium">退款到账</p>
                          <p className="text-sm text-muted-foreground">
                            审核通过后3-5个工作日原路退回
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-8 p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-start gap-3">
                  <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                      特别说明
                    </h4>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• 退款将原路返回到您的付款账户</li>
                      <li>• 退款处理期间，您仍可正常使用专业版功能</li>
                      <li>• 如有疑问，请随时联系我们的客服团队</li>
                      <li>• 我们承诺公平、透明的退款处理流程</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">常见问题</h2>
              <p className="text-muted-foreground">关于价格方案的常见疑问解答</p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {faqs.map((faq, index) => (
                  <Card
                    key={index}
                    className="border hover:border-primary/50 transition-all duration-300">
                    <CardContent className="p-5">
                      <h3 className="font-semibold mb-2 text-sm">{faq.question}</h3>
                      <p className="text-muted-foreground text-sm leading-normal">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* 底部CTA */}
        <section className="py-20 bg-gradient-to-br from-primary/10 via-blue-500/5 to-purple-500/10 relative overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="absolute top-10 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl"></div>

          <div className="container mx-auto px-4 relative">
            <div className="text-center max-w-3xl mx-auto">
              <div className="inline-flex items-center gap-2 mb-6">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-primary uppercase tracking-wider">
                  限时优惠
                </span>
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              </div>

              <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                准备好升级您的下载体验了吗？
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                立即升级到专业版，享受无限制的高速下载服务
              </p>

              {/* 特色亮点 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                <div className="flex items-center justify-center gap-2 text-sm">
                  <Check className="h-4 w-4 text-green-600" />
                  <span>30天退款保障</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm">
                  <Check className="h-4 w-4 text-green-600" />
                  <span>首月仅需¥9.9</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm">
                  <Check className="h-4 w-4 text-green-600" />
                  <span>立即享受所有功能</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="text-lg px-10 py-4 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                  <Link href="/checkout" className="flex items-center gap-2">
                    立即升级专业版
                    <Crown className="h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="text-lg px-10 py-4 border-2 hover:border-primary hover:text-primary transition-all duration-300">
                  <Link href="/">先试用免费版</Link>
                </Button>
              </div>

              <p className="text-xs text-muted-foreground mt-6">
                * 优惠价格仅限前1000名用户，先到先得
              </p>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
