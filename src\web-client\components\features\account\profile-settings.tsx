'use client';

import { useState } from 'react';
import { User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Mail,
  Save,
  Upload,
  User as UserIcon,
} from 'lucide-react';
import { sendEmailVerification, updateUser } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface ProfileSettingsProps {
  user: User;
}

export function ProfileSettings({ user }: ProfileSettingsProps) {
  const [formData, setFormData] = useState({
    username: user.username || '',
    avatarUrl: user.avatarUrl || '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingVerification, setIsSendingVerification] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const updatedUser = await updateUser({
        username: formData.username || undefined,
        avatarUrl: formData.avatarUrl || undefined,
      });

      if (updatedUser) {
        toast({
          title: '更新成功',
          description: '您的个人资料已更新',
        });
      } else {
        toast({
          title: '更新失败',
          description: '无法更新个人资料，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: '更新失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendVerification = async () => {
    setIsSendingVerification(true);
    try {
      const success = await sendEmailVerification();
      if (success) {
        toast({
          title: '验证邮件已发送',
          description: '请检查您的邮箱并点击验证链接',
        });
      } else {
        toast({
          title: '发送失败',
          description: '无法发送验证邮件，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: '发送失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsSendingVerification(false);
    }
  };

  const getUserInitials = (user: User) => {
    if (user.username) {
      return user.username.slice(0, 2).toUpperCase();
    }
    return user.email.slice(0, 2).toUpperCase();
  };

  return (
    <div className="space-y-6">
      {/* 基本信息卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            基本信息
          </CardTitle>
          <CardDescription>管理您的个人资料信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 头像和基本信息 */}
          <div className="flex items-start gap-6">
            <div className="flex flex-col items-center gap-3">
              <Avatar className="h-20 w-20">
                <AvatarImage src={formData.avatarUrl} alt={user.username || user.email} />
                <AvatarFallback className="text-lg">{getUserInitials(user)}</AvatarFallback>
              </Avatar>
              <Button variant="outline" size="sm" disabled>
                <Upload className="mr-2 h-4 w-4" />
                上传头像
              </Button>
              <p className="text-xs text-muted-foreground text-center">头像上传功能即将推出</p>
            </div>

            <div className="flex-1 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">邮箱地址</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{user.email}</span>
                    </div>
                    {user.emailVerified ? (
                      <Badge variant="default" className="bg-green-500">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        已验证
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <AlertCircle className="mr-1 h-3 w-3" />
                        未验证
                      </Badge>
                    )}
                  </div>
                  {!user.emailVerified && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSendVerification}
                      disabled={isSendingVerification}>
                      {isSendingVerification ? '发送中...' : '发送验证邮件'}
                    </Button>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">订阅等级</Label>
                  <div className="flex items-center gap-2">
                    <Badge variant={user.subscriptionTier === 'Pro' ? 'default' : 'secondary'}>
                      {user.subscriptionTier === 'Pro' ? '专业版' : '免费版'}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">注册时间</Label>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{new Date(user.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>

                {user.lastLoginAt && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">最后登录</Label>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(user.lastLoginAt).toLocaleString()}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 编辑表单 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入用户名"
                  value={formData.username}
                  onChange={(e) => setFormData((prev) => ({ ...prev, username: e.target.value }))}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="avatarUrl">头像链接</Label>
                <Input
                  id="avatarUrl"
                  type="url"
                  placeholder="请输入头像图片链接"
                  value={formData.avatarUrl}
                  onChange={(e) => setFormData((prev) => ({ ...prev, avatarUrl: e.target.value }))}
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
