'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { VideoPageData } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Download, Eye, FileText, Music, Play, Scissors, Star } from 'lucide-react';
import { bytesToSize } from '@/lib/youtube-utils';
import { cn } from '@/lib/utils';
import { SubtitlePreviewModal } from './subtitle-preview-modal';
import { VideoClipPanel } from './video-clip-panel';
import { VideoClipModal } from './video-clip-modal';
import { GifMakerPanel } from './gif-maker-panel';
import { AudioClipPanel } from './audio-clip-panel';
import { RingtoneMakerPanel } from './ringtone-maker-panel';
import { MultilingualSubtitleMaker } from './multilingual-subtitle-maker';
import { BatchSubtitleDownloader } from './batch-subtitle-downloader';

interface DownloadOptionsTabsProps {
  videoData: VideoPageData;
}

export function DownloadOptionsTabs({ videoData }: DownloadOptionsTabsProps) {
  const { videoStreams, audioStreams, subtitles } = videoData;
  const [selectedSubtitle, setSelectedSubtitle] = useState<{
    langCode: string;
    langName: string;
    isAutoGenerated: boolean;
  } | null>(null);
  const [clipModalOpen, setClipModalOpen] = useState(false);
  const [selectedStream, setSelectedStream] = useState<{
    type: 'video' | 'audio';
    quality: string;
    format: string;
    fileSize: number;
  } | null>(null);

  return (
    <>
      <Tabs defaultValue="video" className="w-full">
        <TabsList className="grid w-full grid-cols-3 h-12 bg-muted/30">
          <TabsTrigger
            value="video"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
            <Play className="h-4 w-4" />
            <span className="hidden sm:inline">视频下载</span>
            <span className="sm:hidden">视频</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {videoStreams.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="audio"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
            <Music className="h-4 w-4" />
            <span className="hidden sm:inline">音频提取</span>
            <span className="sm:hidden">音频</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {audioStreams.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="subtitle"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">字幕下载</span>
            <span className="sm:hidden">字幕</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {subtitles.length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* 视频选项卡内容 */}
        <TabsContent value="video" className="mt-6 space-y-6">
          {/* 所有视频格式 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5 text-primary" />
                所有视频格式
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {videoStreams.map((stream, index) => (
                  <div
                    key={stream.downloadId}
                    className={cn(
                      'flex items-center justify-between p-3 rounded-lg border transition-all duration-200',
                      'hover:bg-muted/50 hover:border-primary/30',
                    )}>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                        <Play className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-0.5">
                          <span className="font-medium text-sm">{stream.qualityLabel}</span>
                          <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                            {stream.format}
                          </Badge>
                          {index === 0 && (
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs px-1.5 py-0.5">
                              <Star className="h-3 w-3 mr-1" />
                              推荐
                            </Badge>
                          )}
                          {index === 1 && (
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                              <Star className="h-3 w-3 mr-1" />
                              次选
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {stream.resolution} • {stream.fps}fps
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm font-medium">{bytesToSize(stream.fileSize)}</div>
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          预计 2-5 分钟
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-primary hover:text-primary-foreground">
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-orange-500 hover:text-white"
                          onClick={() => {
                            setSelectedStream({
                              type: 'video',
                              quality: stream.resolution,
                              format: stream.format,
                              fileSize: stream.fileSize,
                            });
                            setClipModalOpen(true);
                          }}>
                          <Scissors className="mr-2 h-4 w-4" />
                          剪辑
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 视频剪辑面板 */}
          <VideoClipPanel
            videoData={videoData}
            onClipDownload={(startTime, endTime, format) => {
              console.log('视频剪辑下载:', { startTime, endTime, format });
            }}
          />

          {/* GIF制作面板 */}
          <GifMakerPanel
            videoData={videoData}
            onGifGenerate={(startTime, endTime, fps, width) => {
              console.log('GIF生成:', { startTime, endTime, fps, width });
            }}
          />
        </TabsContent>

        {/* 音频选项卡内容 */}
        <TabsContent value="audio" className="mt-6 space-y-6">
          {/* 所有音频格式 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5 text-primary" />
                所有音频格式
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {audioStreams.map((stream, index) => (
                  <div
                    key={stream.downloadId}
                    className={cn(
                      'flex items-center justify-between p-3 rounded-lg border transition-all duration-200',
                      'hover:bg-muted/50 hover:border-green-500/30',
                    )}>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-green-500/10 rounded-lg">
                        <Music className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-0.5">
                          <span className="font-medium text-sm">{stream.qualityLabel}</span>
                          <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                            {stream.format}
                          </Badge>
                          {index === 0 && (
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs px-1.5 py-0.5">
                              <Star className="h-3 w-3 mr-1" />
                              推荐
                            </Badge>
                          )}
                          {index === 1 && (
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                              <Star className="h-3 w-3 mr-1" />
                              次选
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {stream.bitrate ? `${stream.bitrate}kbps 比特率` : '高品质音频'}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm font-medium">{bytesToSize(stream.fileSize)}</div>
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          预计 1-3 分钟
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-green-500 hover:text-white">
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-orange-500 hover:text-white"
                          onClick={() => {
                            setSelectedStream({
                              type: 'audio',
                              quality: stream.bitrate ? `${stream.bitrate}kbps` : '高品质',
                              format: stream.format,
                              fileSize: stream.fileSize,
                            });
                            setClipModalOpen(true);
                          }}>
                          <Scissors className="mr-2 h-4 w-4" />
                          剪辑
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 音频剪辑面板 */}
          <AudioClipPanel
            videoData={videoData}
            onClipDownload={(startTime, endTime, format) => {
              console.log('音频剪辑下载:', { startTime, endTime, format });
            }}
          />

          {/* 铃声制作面板 */}
          <RingtoneMakerPanel
            videoData={videoData}
            onRingtoneGenerate={(startTime, endTime, format, fadeEffects) => {
              console.log('铃声生成:', { startTime, endTime, format, fadeEffects });
            }}
          />
        </TabsContent>

        {/* 字幕选项卡内容 */}
        <TabsContent value="subtitle" className="mt-6 space-y-6">
          {subtitles.length > 0 ? (
            <>
              {/* 官方字幕 */}
              {subtitles.filter((sub) => !sub.isAutoGenerated).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      官方字幕
                      <Badge variant="secondary" className="bg-green-100 text-green-700">
                        高质量
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {subtitles
                        .filter((sub) => !sub.isAutoGenerated)
                        .map((subtitle, index) => (
                          <div
                            key={subtitle.langCode}
                            className={cn(
                              'flex items-center justify-between p-3 rounded-lg border transition-all duration-200',
                              'hover:bg-muted/50 hover:border-blue-500/30',
                            )}>
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-10 h-10 bg-blue-500/10 rounded-lg">
                                <FileText className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <div className="flex items-center gap-2 mb-0.5">
                                  <span className="font-medium text-sm">{subtitle.langName}</span>
                                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                                    官方
                                  </Badge>
                                  {index === 0 && (
                                    <Badge className="bg-yellow-100 text-yellow-800 text-xs px-1.5 py-0.5">
                                      <Star className="h-3 w-3 mr-1" />
                                      推荐
                                    </Badge>
                                  )}
                                  {index === 1 && (
                                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                                      <Star className="h-3 w-3 mr-1" />
                                      次选
                                    </Badge>
                                  )}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  SRT 格式 • 高质量字幕
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  setSelectedSubtitle({
                                    langCode: subtitle.langCode,
                                    langName: subtitle.langName,
                                    isAutoGenerated: subtitle.isAutoGenerated,
                                  })
                                }>
                                <Eye className="h-4 w-4 mr-2" />
                                预览
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="hover:bg-blue-500 hover:text-white">
                                <Download className="mr-2 h-4 w-4" />
                                下载
                              </Button>
                            </div>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 自动生成字幕 */}
              {subtitles.filter((sub) => sub.isAutoGenerated).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      自动生成字幕
                      <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                        AI生成
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {subtitles
                        .filter((sub) => sub.isAutoGenerated)
                        .map((subtitle, index) => (
                          <div
                            key={subtitle.langCode}
                            className={cn(
                              'flex items-center justify-between p-3 rounded-lg border transition-all duration-200',
                              'hover:bg-muted/50 hover:border-orange-500/30',
                            )}>
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-10 h-10 bg-orange-500/10 rounded-lg">
                                <FileText className="h-4 w-4 text-orange-600" />
                              </div>
                              <div>
                                <div className="flex items-center gap-2 mb-0.5">
                                  <span className="font-medium text-sm">{subtitle.langName}</span>
                                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                                    自动
                                  </Badge>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  SRT 格式 • AI 自动生成
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  setSelectedSubtitle({
                                    langCode: subtitle.langCode,
                                    langName: subtitle.langName,
                                    isAutoGenerated: subtitle.isAutoGenerated,
                                  })
                                }>
                                <Eye className="h-4 w-4 mr-2" />
                                预览
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="hover:bg-orange-500 hover:text-white">
                                <Download className="mr-2 h-4 w-4" />
                                下载
                              </Button>
                            </div>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 批量字幕下载面板 */}
              <BatchSubtitleDownloader
                subtitles={subtitles.map((sub) => ({
                  language: sub.langName,
                  languageCode: sub.langCode,
                  isAutoGenerated: sub.isAutoGenerated,
                  url: `/api/subtitle/download/${videoData.id}/${sub.langCode}`,
                }))}
                onBatchDownload={(selectedLanguages, format, includeTranslations) => {
                  console.log('批量字幕下载:', { selectedLanguages, format, includeTranslations });
                }}
              />

              {/* 多语言字幕制作面板 */}
              <MultilingualSubtitleMaker
                videoData={videoData}
                subtitles={subtitles.map((sub) => ({
                  language: sub.langName,
                  languageCode: sub.langCode,
                  isAutoGenerated: sub.isAutoGenerated,
                  url: `/api/subtitle/download/${videoData.id}/${sub.langCode}`,
                }))}
                onGenerate={(primaryLang, secondaryLang, tertiaryLang, format) => {
                  console.log('多语言字幕生成:', {
                    primaryLang,
                    secondaryLang,
                    tertiaryLang,
                    format,
                  });
                }}
              />
            </>
          ) : (
            /* 无字幕状态 */
            <Card className="border-dashed">
              <CardContent className="p-12 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="p-4 bg-muted/50 rounded-full">
                    <FileText className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">暂无可用字幕</h3>
                    <p className="text-sm text-muted-foreground mb-6 max-w-md">
                      此视频没有提供官方字幕或自动生成字幕。您可以尝试使用我们的 AI 字幕生成功能。
                    </p>
                  </div>
                  <div className="flex gap-3">
                    <Button variant="outline">
                      <FileText className="mr-2 h-4 w-4" />
                      上传字幕文件
                    </Button>
                    <Button disabled>
                      <FileText className="mr-2 h-4 w-4" />
                      AI 生成字幕
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* 字幕预览模态窗口 */}
      <SubtitlePreviewModal
        isOpen={!!selectedSubtitle}
        onClose={() => setSelectedSubtitle(null)}
        videoData={videoData}
        selectedSubtitle={selectedSubtitle || undefined}
      />

      {/* 剪辑功能模态窗口 */}
      <VideoClipModal
        isOpen={clipModalOpen}
        onClose={() => {
          setClipModalOpen(false);
          setSelectedStream(null);
        }}
        videoData={videoData}
        streamInfo={selectedStream || undefined}
      />
    </>
  );
}
