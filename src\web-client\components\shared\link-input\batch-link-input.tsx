'use client';

import { useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, FileText, Loader2, RotateCcw, Search, Upload } from 'lucide-react';
import { analyzeUrl } from '@/lib/youtube-utils';
import { cn } from '@/lib/utils';

// 链接格式化函数 - 清理链接参数
const formatYouTubeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);

    // 保留的参数列表
    const allowedParams = ['v', 'list', 'index', 't', 'start', 'end'];
    const newSearchParams = new URLSearchParams();

    // 只保留允许的参数
    allowedParams.forEach((param) => {
      const value = urlObj.searchParams.get(param);
      if (value) {
        newSearchParams.set(param, value);
      }
    });

    // 重建URL
    urlObj.search = newSearchParams.toString();
    return urlObj.toString();
  } catch {
    // 如果URL无效，返回原始字符串
    return url;
  }
};

interface BatchLinkInputProps {
  inputValue: string;
  isProcessing: boolean;
  errorMessage: string;
  isDragOver: boolean;
  onInputChange: (value: string) => void;
  onSubmit: () => void;
  onSwitchToSingle: () => void;
  onFileUpload: (file: File) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
}

export function BatchLinkInput({
  inputValue,
  isProcessing,
  errorMessage,
  isDragOver,
  onInputChange,
  onSubmit,
  onSwitchToSingle,
  onFileUpload,
  onDragOver,
  onDragLeave,
  onDrop,
}: BatchLinkInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const batchAnalysis = useMemo(() => {
    if (!inputValue) return { validLinks: [], invalidCount: 0 };
    const lines = inputValue.split('\n').filter((line) => line.trim() !== '');
    const validLinks: string[] = [];
    let invalidCount = 0;

    lines.forEach((line) => {
      const res = analyzeUrl(line);
      // 批量模式只接受视频或 Shorts 链接
      if (res.type === 'VIDEO' || res.type === 'SHORTS') {
        validLinks.push(line);
      } else {
        invalidCount++;
      }
    });

    return {
      validLinks: validLinks.slice(0, 100), // 限制最多 100 个
      invalidCount,
      isTruncated: validLinks.length > 100,
    };
  }, [inputValue]);

  const { validLinks, invalidCount, isTruncated } = batchAnalysis;
  const validCount = validLinks.length;
  const isValid = validCount >= 2 && validCount <= 100;

  const getInputBorderColor = () => {
    if (isProcessing) return 'border-blue-500';
    if (!inputValue) return 'border-border';
    return isValid ? 'border-green-500' : 'border-red-500';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;

    // 处理粘贴的多个链接（可能在同一行）
    const lines = value.split('\n');
    const formattedLines: string[] = [];

    lines.forEach((line) => {
      const trimmedLine = line.trim();

      if (trimmedLine) {
        // 检查是否包含多个YouTube链接
        const urlPattern = /(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[^\s]+/g;
        const matches = trimmedLine.match(urlPattern);

        if (matches && matches.length > 1) {
          // 如果一行中有多个链接，分别换行
          matches.forEach((match) => {
            const cleanUrl = formatYouTubeUrl(match.trim());
            formattedLines.push(cleanUrl);
          });
        } else if (matches && matches.length === 1) {
          // 单个链接，格式化
          const cleanUrl = formatYouTubeUrl(trimmedLine);
          formattedLines.push(cleanUrl);
        } else {
          // 非链接内容，保持原样
          formattedLines.push(line);
        }
      } else {
        // 空行保持
        formattedLines.push(line);
      }
    });

    onInputChange(formattedLines.join('\n'));
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileUpload(file);
    }
  };

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}>
      {/* 批量输入区域 */}
      <div className="relative">
        <motion.div
          className={cn(
            'relative border-2 rounded-xl transition-all duration-300',
            getInputBorderColor(),
            isDragOver && 'border-blue-500 bg-blue-50/50 scale-[1.01]',
            'bg-gradient-to-br from-background to-muted/10',
          )}
          animate={isDragOver ? { scale: 1.02 } : { scale: 1 }}
          transition={{ duration: 0.2 }}>
          <Textarea
            placeholder="每行粘贴一个视频链接，或拖拽 TXT 文件到此处...&#10;&#10;• 最少 2 个，最多 100 个链接&#10;• 支持拖拽文件批量导入"
            className={cn(
              'h-64 resize-none p-6 pb-20 pr-28 text-base border-0 bg-transparent',
              'focus:ring-0 focus:outline-none',
              'placeholder:text-muted-foreground/70',
            )}
            value={inputValue}
            onChange={handleInputChange}
            disabled={isProcessing}
            onDragOver={onDragOver}
            onDragLeave={onDragLeave}
            onDrop={onDrop}
            aria-label="批量视频链接输入框"
            aria-describedby="batch-input-help"
            role="textbox"
            aria-multiline="true"
          />

          {/* 框内按钮组 */}
          {/* 左下角上传文件按钮 */}
          <div className="absolute bottom-4 left-4">
            <Button
              size="sm"
              variant="outline"
              onClick={handleUploadClick}
              disabled={isProcessing}
              className="hover:bg-primary hover:text-primary-foreground transition-colors duration-200">
              <Upload className="h-4 w-4 mr-2" />
              上传文件
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".txt,text/plain"
              onChange={handleFileChange}
              className="hidden"
              aria-label="上传TXT文件"
            />
          </div>

          {/* 右上角按钮组 */}
          <div className="absolute top-4 right-4 flex items-center gap-2">
            {/* 清空按钮 */}
            {inputValue && !isProcessing && (
              <Button
                size="icon"
                variant="ghost"
                onClick={() => onInputChange('')}
                className="h-8 w-8 hover:bg-red-100 hover:text-red-600 transition-colors duration-200">
                <RotateCcw className="h-4 w-4" />
              </Button>
            )}

            {/* 返回单次模式按钮 */}
            <Button
              size="sm"
              variant="outline"
              onClick={onSwitchToSingle}
              disabled={isProcessing}
              className="hover:bg-muted">
              返回单次
            </Button>
          </div>

          {/* 右下角搜索按钮 */}
          <div className="absolute bottom-4 right-4">
            <Button
              disabled={!isValid || isProcessing}
              onClick={onSubmit}
              className={cn(
                'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800',
                'shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200',
                'text-white font-semibold',
                isProcessing && 'animate-pulse',
              )}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  搜索 {validCount} 个链接
                </>
              )}
            </Button>
          </div>

          {/* 拖拽提示覆盖层 */}
          {isDragOver && (
            <motion.div
              className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-xl flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}>
              <div className="text-center">
                <FileText className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                <p className="text-blue-700 font-medium">释放文件以上传</p>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* 状态信息 */}
        <motion.div
          className="mt-4 space-y-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}>
          <div id="batch-input-help" className="flex flex-wrap items-center gap-2">
            {validCount > 0 && (
              <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                ✓ {validCount} 个有效链接
              </Badge>
            )}
            {invalidCount > 0 && (
              <Badge variant="secondary" className="bg-red-100 text-red-700 border-red-200">
                ✗ {invalidCount} 个无效链接
              </Badge>
            )}
            {isTruncated && (
              <Badge
                variant="secondary"
                className="bg-yellow-100 text-yellow-700 border-yellow-200">
                <AlertCircle className="h-3 w-3 mr-1" />
                已截取前 100 个
              </Badge>
            )}
          </div>

          {!isValid && inputValue && (
            <p className="text-sm text-red-600">
              {validCount < 2 ? '至少需要 2 个有效链接' : '最多支持 100 个链接'}
            </p>
          )}

          {errorMessage && (
            <motion.p
              className="text-sm text-red-600"
              role="alert"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}>
              {errorMessage}
            </motion.p>
          )}
        </motion.div>
      </div>

      {/* 状态信息 - 保留空间并居中 */}
      <div className="mt-4 h-8 flex items-center justify-center">
        <div id="batch-input-status">
          {(inputValue || errorMessage) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3, delay: 0.1 }}>
              {errorMessage ? (
                <motion.p
                  className="text-sm text-red-600 text-center font-medium"
                  role="alert"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2 }}>
                  {errorMessage}
                </motion.p>
              ) : validCount > 0 ? (
                <div className="text-sm font-medium text-center text-green-600">
                  ✓ 已识别 {validCount} 个有效链接
                  {isTruncated && ` (已截取前100个)`}
                </div>
              ) : inputValue ? (
                <div className="text-sm font-medium text-center text-red-600">
                  ⚠️ 未找到有效的视频链接
                </div>
              ) : null}
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
