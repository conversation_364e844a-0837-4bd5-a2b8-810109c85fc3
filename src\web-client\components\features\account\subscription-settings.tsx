'use client';

import { useEffect, useState } from 'react';
import { Subscription, User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  CreditCard,
  Crown,
  Download,
  ExternalLink,
  FileText,
  Image,
  MessageSquare,
} from 'lucide-react';
import { getUserSubscription } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface SubscriptionSettingsProps {
  user: User;
}

export function SubscriptionSettings({ user }: SubscriptionSettingsProps) {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        const sub = await getUserSubscription();
        setSubscription(sub);
      } catch (error) {
        console.error('Failed to fetch subscription:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscription();
  }, []);

  const handleUpgrade = () => {
    toast({
      title: '升级功能',
      description: '专业版升级功能即将推出',
    });
  };

  const handleManageBilling = () => {
    toast({
      title: '账单管理',
      description: '账单管理功能即将推出',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">活跃</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">已取消</Badge>;
      case 'expired':
        return <Badge variant="outline">已过期</Badge>;
      case 'pending':
        return <Badge variant="secondary">待处理</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* 当前订阅状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            当前订阅
          </CardTitle>
          <CardDescription>查看和管理您的订阅状态</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">
                  {user.subscriptionTier === 'Pro' ? '专业版' : '免费版'}
                </h3>
                <Badge variant={user.subscriptionTier === 'Pro' ? 'default' : 'secondary'}>
                  {user.subscriptionTier === 'Pro' ? 'PRO' : 'FREE'}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {user.subscriptionTier === 'Pro'
                  ? '享受所有高级功能和优先支持'
                  : '基础功能，满足日常下载需求'}
              </p>
            </div>

            {user.subscriptionTier === 'Free' && (
              <Button onClick={handleUpgrade}>
                <Crown className="mr-2 h-4 w-4" />
                升级到专业版
              </Button>
            )}
          </div>

          {/* 专业版订阅详情 */}
          {user.subscriptionTier === 'Pro' && subscription && (
            <div className="space-y-4">
              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">订阅状态</Label>
                  <div>{getStatusBadge(subscription.status)}</div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">自动续费</Label>
                  <div>
                    <Badge variant={subscription.autoRenew ? 'default' : 'outline'}>
                      {subscription.autoRenew ? '已开启' : '已关闭'}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">订阅开始</Label>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDate(subscription.startDate)}</span>
                  </div>
                </div>

                {subscription.endDate && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">到期时间</Label>
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDate(subscription.endDate)}</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button variant="outline" onClick={handleManageBilling}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  管理账单
                </Button>
                <Button variant="outline" disabled>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  客户门户
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 功能对比 */}
      <Card>
        <CardHeader>
          <CardTitle>功能对比</CardTitle>
          <CardDescription>了解免费版和专业版的功能差异</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-sm font-medium">
              <div>功能</div>
              <div className="text-center">免费版</div>
              <div className="text-center">专业版</div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  视频下载
                </div>
                <div className="text-center">✓</div>
                <div className="text-center">✓</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  音频下载
                </div>
                <div className="text-center">✓</div>
                <div className="text-center">✓</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  字幕下载
                </div>
                <div className="text-center">基础</div>
                <div className="text-center">高级</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  评论下载
                </div>
                <div className="text-center">100条</div>
                <div className="text-center">1000条</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Image className="h-4 w-4" />
                  缩略图下载
                </div>
                <div className="text-center">✓</div>
                <div className="text-center">✓</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>批量下载</div>
                <div className="text-center">10个视频</div>
                <div className="text-center">无限制</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>下载历史</div>
                <div className="text-center">30天</div>
                <div className="text-center">永久</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>优先处理</div>
                <div className="text-center">-</div>
                <div className="text-center">✓</div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>技术支持</div>
                <div className="text-center">社区</div>
                <div className="text-center">优先邮件</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 付费历史 */}
      {user.subscriptionTier === 'pro' &&
        subscription &&
        subscription.paymentHistory.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>付费历史</CardTitle>
              <CardDescription>查看您的付费记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {subscription.paymentHistory.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <div className="font-medium">
                        ${payment.amount} {payment.currency}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(payment.paidAt)} • {payment.paymentMethod}
                      </div>
                    </div>
                    {payment.invoiceUrl && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={payment.invoiceUrl} target="_blank" rel="noopener noreferrer">
                          <FileText className="mr-2 h-4 w-4" />
                          发票
                        </a>
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>;
}
