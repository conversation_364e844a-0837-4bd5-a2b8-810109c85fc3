using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public enum BatchJobSourceType
{
    Playlist,
    Channel,
    MultiVideoList
}

public enum BatchJobStatus
{
    NotStarted,
    Running,
    PartiallyCompleted,
    Completed,
    Failed,
    Cancelled
}

public class BatchJob
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid UserId { get; set; }
    public User User { get; set; } = null!;
    public BatchJobSourceType SourceType { get; set; }
    public required string SourceIdentifier { get; set; }
    public required string Config { get; set; }
    public BatchJobStatus Status { get; set; } = BatchJobStatus.NotStarted;
    public int Progress { get; set; }
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public ICollection<WorkTask> WorkTasks { get; set; } = [];
}

public class BatchJobConfiguration : IEntityTypeConfiguration<BatchJob>
{
    public void Configure(EntityTypeBuilder<BatchJob> builder)
    {
        // 主键配置
        builder.HasKey(bj => bj.Id);
        // 基本属性配置
        builder.Property(bj => bj.UserId).IsRequired();
        builder.Property(bj => bj.SourceType).IsRequired().HasConversion<string>();
        builder.Property(bj => bj.Status).IsRequired().HasConversion<string>();
        builder.Property(bj => bj.SourceIdentifier).IsRequired().HasColumnType("jsonb");
        builder.Property(bj => bj.Config).IsRequired().HasColumnType("jsonb");
        builder.Property(bj => bj.Progress).HasDefaultValue(0);
        // 关系配置
        builder.HasOne(bj => bj.User).WithMany(u => u.BatchJobs).HasForeignKey(bj => bj.UserId).OnDelete(DeleteBehavior.Cascade);
        // 索引配置
        builder.HasIndex(bj => bj.UserId);
        builder.HasIndex(bj => bj.Status);
        builder.HasIndex(bj => bj.CreatedAt);
        // 默认值配置
        builder.Property(bj => bj.Status).HasDefaultValue(BatchJobStatus.NotStarted);
        builder.Property(bj => bj.CreatedAt).HasDefaultValueSql("NOW()");
    }
}