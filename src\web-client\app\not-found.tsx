import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Compass, Download, FileQuestion, Home, Music, PlayCircle, Search } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '页面未找到 - YTDownloader',
  description: '抱歉，您访问的页面不存在。返回首页继续使用我们的YouTube下载服务。',
};

export default function NotFound() {
  const quickLinks = [
    {
      href: '/',
      icon: Home,
      title: '返回首页',
      description: '回到主页开始下载',
    },
    {
      href: '/youtube-video-downloader',
      icon: Download,
      title: '视频下载',
      description: '下载YouTube视频',
    },
    {
      href: '/youtube-audio-extractor',
      icon: Music,
      title: '音频提取',
      description: '提取音频文件',
    },
    {
      href: '/youtube-playlist-downloader',
      icon: PlayCircle,
      title: '播放列表',
      description: '批量下载播放列表',
    },
  ];

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* 404 图标和标题 */}
            <div className="mb-12">
              <div className="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-red-100 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 rounded-full mb-8">
                <FileQuestion className="h-16 w-16 text-red-500" />
              </div>

              <h1 className="text-6xl font-black mb-4 bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                404
              </h1>

              <h2 className="text-3xl font-bold mb-4 text-foreground">页面未找到</h2>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                抱歉，您访问的页面不存在或已被移动。可能是链接错误，或者页面已被删除。
                不过别担心，我们有很多其他有用的功能等您探索！
              </p>
            </div>

            {/* 快速导航 */}
            <div className="mb-12">
              <h3 className="text-xl font-semibold mb-6 flex items-center justify-center gap-2">
                <Compass className="h-5 w-5 text-primary" />
                快速导航
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {quickLinks.map((link, index) => (
                  <Link key={index} href={link.href}>
                    <Card className="h-full border-2 border-transparent hover:border-primary/50 transition-all duration-300 hover:shadow-lg group">
                      <CardContent className="p-6 text-center">
                        <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-lg mb-4 group-hover:scale-110 transition-transform duration-300">
                          <link.icon className="h-6 w-6 text-primary" />
                        </div>
                        <h4 className="font-semibold mb-2">{link.title}</h4>
                        <p className="text-sm text-muted-foreground">{link.description}</p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>

            {/* 主要操作按钮 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link href="/">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 shadow-lg hover:shadow-xl transition-all duration-300">
                  <Home className="mr-2 h-5 w-5" />
                  返回首页
                </Button>
              </Link>
            </div>

            {/* 搜索建议 */}
            <Card className="max-w-2xl mx-auto border-0 shadow-lg bg-gradient-to-br from-background to-muted/20">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-4">
                  <Search className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-semibold">寻找特定功能？</h3>
                </div>

                <p className="text-muted-foreground mb-6">
                  如果您在寻找特定的下载功能，可以尝试以下方式：
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <h4 className="font-medium">常用功能：</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• YouTube视频下载</li>
                      <li>• 音频提取转换</li>
                      <li>• 播放列表批量下载</li>
                      <li>• 字幕下载</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">创作工具：</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• GIF制作器</li>
                      <li>• 视频剪辑工具</li>
                      <li>• 铃声制作</li>
                      <li>• 缩略图下载</li>
                    </ul>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t">
                  <p className="text-sm text-muted-foreground">
                    如果问题持续存在，请
                    <Link href="/contact" className="text-primary hover:underline mx-1">
                      联系我们
                    </Link>
                    获取帮助。
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
