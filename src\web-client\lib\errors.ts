// 认证相关错误类型定义

export enum AuthErrorCode {
  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',

  // 认证错误
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',

  // 会话错误
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  INVALID_SESSION = 'INVALID_SESSION',

  // 验证错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  INVALID_EMAIL = 'INVALID_EMAIL',

  // 服务器错误
  SERVER_ERROR = 'SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',

  // 匿名用户错误
  ANONYMOUS_USER_NOT_FOUND = 'ANONYMOUS_USER_NOT_FOUND',
  MIGRATION_FAILED = 'MIGRATION_FAILED',

  // 未知错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class AuthError extends Error {
  public readonly code: AuthErrorCode;
  public readonly userMessage: string;
  public readonly details?: any;

  constructor(code: AuthErrorCode, message: string, userMessage: string, details?: any) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
    this.userMessage = userMessage;
    this.details = details;
  }
}

// 错误消息映射
export const ERROR_MESSAGES: Record<AuthErrorCode, string> = {
  [AuthErrorCode.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [AuthErrorCode.TIMEOUT_ERROR]: '请求超时，请稍后重试',

  [AuthErrorCode.INVALID_CREDENTIALS]: '邮箱或密码错误，请重新输入',
  [AuthErrorCode.EMAIL_ALREADY_EXISTS]: '该邮箱已被注册，请使用其他邮箱或直接登录',
  [AuthErrorCode.USER_NOT_FOUND]: '用户不存在，请检查邮箱地址',
  [AuthErrorCode.ACCOUNT_DISABLED]: '账户已被禁用，请联系客服',

  [AuthErrorCode.SESSION_EXPIRED]: '登录已过期，请重新登录',
  [AuthErrorCode.INVALID_SESSION]: '登录状态异常，请重新登录',

  [AuthErrorCode.VALIDATION_ERROR]: '输入信息有误，请检查后重试',
  [AuthErrorCode.WEAK_PASSWORD]: '密码强度不足，请使用至少8位包含字母和数字的密码',
  [AuthErrorCode.INVALID_EMAIL]: '邮箱格式不正确，请重新输入',

  [AuthErrorCode.SERVER_ERROR]: '服务器暂时无法处理请求，请稍后重试',
  [AuthErrorCode.SERVICE_UNAVAILABLE]: '服务暂时不可用，请稍后重试',

  [AuthErrorCode.ANONYMOUS_USER_NOT_FOUND]: '匿名用户信息丢失，将重新创建',
  [AuthErrorCode.MIGRATION_FAILED]: '数据迁移失败，请联系客服处理',

  [AuthErrorCode.UNKNOWN_ERROR]: '发生未知错误，请稍后重试',
};

// 错误分析器
export class AuthErrorAnalyzer {
  static analyzeError(error: any): AuthError {
    // 网络错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new AuthError(
        AuthErrorCode.NETWORK_ERROR,
        'Network request failed',
        ERROR_MESSAGES[AuthErrorCode.NETWORK_ERROR],
        error,
      );
    }

    // 超时错误
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return new AuthError(
        AuthErrorCode.TIMEOUT_ERROR,
        'Request timeout',
        ERROR_MESSAGES[AuthErrorCode.TIMEOUT_ERROR],
        error,
      );
    }

    // API 响应错误
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      // 根据状态码分析
      switch (status) {
        case 400:
          return this.analyzeBadRequest(data, error);
        case 401:
          return new AuthError(
            AuthErrorCode.INVALID_CREDENTIALS,
            'Unauthorized',
            ERROR_MESSAGES[AuthErrorCode.INVALID_CREDENTIALS],
            error,
          );
        case 403:
          return new AuthError(
            AuthErrorCode.ACCOUNT_DISABLED,
            'Forbidden',
            ERROR_MESSAGES[AuthErrorCode.ACCOUNT_DISABLED],
            error,
          );
        case 404:
          return new AuthError(
            AuthErrorCode.USER_NOT_FOUND,
            'Not found',
            ERROR_MESSAGES[AuthErrorCode.USER_NOT_FOUND],
            error,
          );
        case 500:
          return new AuthError(
            AuthErrorCode.SERVER_ERROR,
            'Internal server error',
            ERROR_MESSAGES[AuthErrorCode.SERVER_ERROR],
            error,
          );
        case 503:
          return new AuthError(
            AuthErrorCode.SERVICE_UNAVAILABLE,
            'Service unavailable',
            ERROR_MESSAGES[AuthErrorCode.SERVICE_UNAVAILABLE],
            error,
          );
        default:
          return new AuthError(
            AuthErrorCode.UNKNOWN_ERROR,
            `HTTP ${status}`,
            ERROR_MESSAGES[AuthErrorCode.UNKNOWN_ERROR],
            error,
          );
      }
    }

    // 后端返回的错误信息
    if (error.error) {
      switch (error.error) {
        case 'EMAIL_ALREADY_EXISTS':
          return new AuthError(
            AuthErrorCode.EMAIL_ALREADY_EXISTS,
            'Email already exists',
            ERROR_MESSAGES[AuthErrorCode.EMAIL_ALREADY_EXISTS],
            error,
          );
        case 'INVALID_CREDENTIALS':
          return new AuthError(
            AuthErrorCode.INVALID_CREDENTIALS,
            'Invalid credentials',
            ERROR_MESSAGES[AuthErrorCode.INVALID_CREDENTIALS],
            error,
          );
        case 'MIGRATION_FAILED':
          return new AuthError(
            AuthErrorCode.MIGRATION_FAILED,
            'Migration failed',
            ERROR_MESSAGES[AuthErrorCode.MIGRATION_FAILED],
            error,
          );
        default:
          return new AuthError(
            AuthErrorCode.UNKNOWN_ERROR,
            error.error,
            ERROR_MESSAGES[AuthErrorCode.UNKNOWN_ERROR],
            error,
          );
      }
    }

    // 默认未知错误
    return new AuthError(
      AuthErrorCode.UNKNOWN_ERROR,
      error.message || 'Unknown error',
      ERROR_MESSAGES[AuthErrorCode.UNKNOWN_ERROR],
      error,
    );
  }

  private static analyzeBadRequest(data: any, originalError: any): AuthError {
    if (data?.error) {
      switch (data.error) {
        case 'EMAIL_ALREADY_EXISTS':
          return new AuthError(
            AuthErrorCode.EMAIL_ALREADY_EXISTS,
            'Email already exists',
            ERROR_MESSAGES[AuthErrorCode.EMAIL_ALREADY_EXISTS],
            originalError,
          );
        case 'VALIDATION_ERROR':
          return new AuthError(
            AuthErrorCode.VALIDATION_ERROR,
            'Validation error',
            data.message || ERROR_MESSAGES[AuthErrorCode.VALIDATION_ERROR],
            originalError,
          );
        default:
          return new AuthError(
            AuthErrorCode.VALIDATION_ERROR,
            'Bad request',
            ERROR_MESSAGES[AuthErrorCode.VALIDATION_ERROR],
            originalError,
          );
      }
    }

    return new AuthError(
      AuthErrorCode.VALIDATION_ERROR,
      'Bad request',
      ERROR_MESSAGES[AuthErrorCode.VALIDATION_ERROR],
      originalError,
    );
  }
}

// 错误重试策略
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  retryableErrors: AuthErrorCode[];
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  retryableErrors: [
    AuthErrorCode.NETWORK_ERROR,
    AuthErrorCode.TIMEOUT_ERROR,
    AuthErrorCode.SERVER_ERROR,
    AuthErrorCode.SERVICE_UNAVAILABLE,
  ],
};

export class RetryHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = DEFAULT_RETRY_CONFIG,
  ): Promise<T> {
    let lastError: AuthError;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        const authError = AuthErrorAnalyzer.analyzeError(error);
        lastError = authError;

        // 如果是最后一次尝试或错误不可重试，直接抛出
        if (attempt === config.maxRetries || !config.retryableErrors.includes(authError.code)) {
          throw authError;
        }

        // 计算延迟时间（指数退避）
        const delay = Math.min(config.baseDelay * Math.pow(2, attempt), config.maxDelay);

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }
}
