import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { HeroSection } from '@/components/features/home/<USER>';
import { FeaturesSection } from '@/components/features/home/<USER>';
import { DetailedFeaturesSection } from '@/components/features/home/<USER>';
import { HowItWorksSection } from '@/components/features/home/<USER>';
import { WhyUsSection } from '@/components/features/home/<USER>';
import { SecuritySection } from '@/components/features/home/<USER>';
import { AboutSection } from '@/components/features/home/<USER>';
import { CtaSection } from '@/components/features/home/<USER>';
import { FaqSection } from '@/components/features/home/<USER>';

export const metadata: Metadata = {
  title: 'YTDownloader - 免费YouTube视频下载器',
  description:
    '免费下载YouTube视频、音频、字幕。支持MP4、MP3格式，高清画质，批量下载，无需注册即可使用。',
};

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow">
        <HeroSection />
        <FeaturesSection />
        <DetailedFeaturesSection />
        <HowItWorksSection />
        <WhyUsSection />
        <SecuritySection />
        <AboutSection />
        <CtaSection />
        <FaqSection />
      </main>
      <Footer />
    </div>
  );
}
