{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport Credentials from 'next-auth/providers/credentials';\nimport { z } from 'zod';\n\n// 登录表单验证 schema，用于前后端统一验证\nexport const loginSchema = z.object({\n  email: z.string().email('请输入有效的邮箱地址'),\n  password: z.string().min(6, '密码至少需要6个字符'),\n});\n\nexport const { handlers, auth, signIn, signOut } = NextAuth({\n  providers: [\n    // 配置凭据（邮箱+密码）认证提供者\n    Credentials({\n      name: 'credentials',\n      credentials: {\n        email: { label: '邮箱', type: 'email' },\n        password: { label: '密码', type: 'password' },\n      },\n      // 核心授权逻辑：当 signIn('credentials', ...) 被调用时执行\n      async authorize(credentials) {\n        try {\n          // 1. 使用 Zod 安全地验证输入数据\n          const validatedFields = loginSchema.safeParse(credentials);\n          if (!validatedFields.success) {\n            return null; // 验证失败\n          }\n\n          const { email, password } = validatedFields.data;\n\n          // 2. 调用后端 API 进行实际的凭据验证\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({ email, password }),\n          });\n\n          if (!response.ok) {\n            return null; // 后端验证失败\n          }\n\n          const data = await response.json();\n\n          // 3. 如果后端返回了用户信息和JWT token，则认证成功\n          if (data.user && data.accessToken) {\n            // 返回的用户对象将用于创建会话，同时保存后端JWT token\n            return {\n              id: data.user.id,\n              email: data.user.email,\n              name: data.user.username || data.user.email,\n              subscriptionTier: data.user.subscriptionTier,\n              emailVerified: data.user.emailVerified,\n              backendToken: data.accessToken, // 保存后端签发的access token\n              refreshToken: data.refreshToken, // 保存refresh token\n              tokenExpiresAt: data.expiresAt, // 保存token过期时间\n            };\n          }\n\n          return null; // 未找到用户或凭据无效\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  // 配置会话管理策略\n  session: {\n    strategy: 'jwt', // 使用 JWT 进行会话管理，不依赖数据库\n    maxAge: 30 * 24 * 60 * 60, // 会话有效期为 30 天\n  },\n  // 配置安全的Cookie设置\n  cookies: {\n    sessionToken: {\n      name: `next-auth.session-token`,\n      options: {\n        httpOnly: true, // 防止XSS攻击\n        sameSite: 'lax', // CSRF保护\n        path: '/',\n        secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS\n      },\n    },\n  },\n  // 回调函数，用于控制认证流程中的行为\n  callbacks: {\n    // `session` 回调：在每次访问会话时调用，用于将自定义数据从 token 同步到 session.user\n    async session({ session, token }) {\n      if (session.user && token) {\n        session.user.id = token.sub!; // 从 token 的 sub 字段获取用户 ID\n        // 将自定义字段从 token 添加到 session 中，使其在客户端可用\n        session.user.subscriptionTier = (token.subscriptionTier as string) || 'free';\n        session.user.emailVerified = token.emailVerified as boolean;\n        // 将后端JWT token添加到session中，供API调用使用\n        session.user.backendToken = token.backendToken as string;\n        session.user.refreshToken = token.refreshToken as string;\n        session.user.tokenExpiresAt = token.tokenExpiresAt as string;\n      }\n      return session;\n    },\n    // `jwt` 回调：在创建或更新 JWT 时调用，用于将自定义数据添加到 token 中\n    async jwt({ token, user }) {\n      // 首次登录时（user 对象存在），将后端返回的用户信息添加到 token 中\n      if (user) {\n        const u = user as {\n          subscriptionTier?: string;\n          emailVerified?: boolean;\n          backendToken?: string;\n          refreshToken?: string;\n          tokenExpiresAt?: string;\n        };\n        token.subscriptionTier = u.subscriptionTier;\n        token.emailVerified = u.emailVerified;\n        token.backendToken = u.backendToken; // 保存后端JWT token\n        token.refreshToken = u.refreshToken; // 保存refresh token\n        token.tokenExpiresAt = u.tokenExpiresAt; // 保存token过期时间\n      }\n      return token;\n    },\n    // `redirect` 回调：控制登录或登出后的重定向行为\n    async redirect({ url, baseUrl }) {\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  // 自定义认证页面\n  pages: {\n    signIn: '/login', // 指定登录页面的路径\n    error: '/auth/error', // 指定认证错误页面的路径\n  },\n  // 事件监听器，用于记录认证相关的事件\n  events: {\n    async signIn({ user, account }) {\n      console.log('User signed in:', { user: user.email, account: account?.provider });\n    },\n    async signOut() {\n      console.log('User signed out');\n    },\n  },\n  // 在开发环境中启用调试模式\n  debug: process.env.NODE_ENV === 'development',\n});\n\n// 通过模块扩展（Module Augmentation）为 NextAuth 的默认类型添加自定义字段\ndeclare module 'next-auth' {\n  interface User {\n    subscriptionTier?: string;\n    emailVerified?: boolean;\n    backendToken?: string;\n    refreshToken?: string;\n    tokenExpiresAt?: string;\n  }\n\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      name?: string | null;\n      image?: string | null;\n      subscriptionTier?: string;\n      emailVerified?: boolean;\n      backendToken?: string;\n      refreshToken?: string;\n      tokenExpiresAt?: string;\n    };\n  }\n}\n\ndeclare module '@auth/core/jwt' {\n  interface JWT {\n    subscriptionTier?: string;\n    emailVerified?: boolean;\n    backendToken?: string;\n    refreshToken?: string;\n    tokenExpiresAt?: string;\n  }\n}\n\n// ===== 统一认证管理 =====\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL!;\n\n// 匿名用户token的本地存储key\nconst ANONYMOUS_ACCESS_TOKEN_KEY = 'anonymous_access_token';\nconst ANONYMOUS_REFRESH_TOKEN_KEY = 'anonymous_refresh_token';\n\n/**\n * Token对接口\n */\nexport interface TokenPair {\n  accessToken: string;\n  refreshToken: string;\n  expiresAt: string;\n}\n\n/**\n * 匿名用户Token管理器\n * 单例模式，管理匿名用户的JWT token\n */\nexport class AnonymousTokenManager {\n  private static instance: AnonymousTokenManager;\n  private currentAccessToken: string | null = null;\n  private currentRefreshToken: string | null = null;\n\n  private constructor() {\n    this.loadTokensFromStorage();\n  }\n\n  static getInstance(): AnonymousTokenManager {\n    if (!AnonymousTokenManager.instance) {\n      AnonymousTokenManager.instance = new AnonymousTokenManager();\n    }\n    return AnonymousTokenManager.instance;\n  }\n\n  /**\n   * 获取当前access token\n   */\n  getToken(): string | null {\n    return this.currentAccessToken;\n  }\n\n  /**\n   * 获取当前refresh token\n   */\n  getRefreshToken(): string | null {\n    return this.currentRefreshToken;\n  }\n\n  /**\n   * 设置token对\n   */\n  setTokenPair(tokenPair: TokenPair): void {\n    this.currentAccessToken = tokenPair.accessToken;\n    this.currentRefreshToken = tokenPair.refreshToken;\n\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(ANONYMOUS_ACCESS_TOKEN_KEY, tokenPair.accessToken);\n      localStorage.setItem(ANONYMOUS_REFRESH_TOKEN_KEY, tokenPair.refreshToken);\n    }\n  }\n\n  /**\n   * 设置单个token（向后兼容）\n   */\n  setToken(token: string): void {\n    this.currentAccessToken = token;\n\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(ANONYMOUS_ACCESS_TOKEN_KEY, token);\n    }\n  }\n\n  /**\n   * 清除所有tokens\n   */\n  clearToken(): void {\n    this.currentAccessToken = null;\n    this.currentRefreshToken = null;\n\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(ANONYMOUS_ACCESS_TOKEN_KEY);\n      localStorage.removeItem(ANONYMOUS_REFRESH_TOKEN_KEY);\n    }\n  }\n\n  /**\n   * 创建新的匿名用户token\n   */\n  async createToken(): Promise<string | null> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/anonymous/create`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.accessToken && data.refreshToken) {\n          this.setTokenPair({\n            accessToken: data.accessToken,\n            refreshToken: data.refreshToken,\n            expiresAt: data.expiresAt,\n          });\n          return data.accessToken;\n        }\n        // 向后兼容\n        if (data.token) {\n          this.setToken(data.token);\n          return data.token;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to create anonymous token:', error);\n    }\n    return null;\n  }\n\n  /**\n   * 获取或创建匿名用户token\n   */\n  async getOrCreateToken(): Promise<string | null> {\n    if (this.currentAccessToken) {\n      // 验证token是否仍然有效\n      if (await this.validateToken(this.currentAccessToken)) {\n        return this.currentAccessToken;\n      }\n\n      // 如果access token过期，尝试使用refresh token刷新\n      if (this.currentRefreshToken) {\n        const newToken = await this.refreshToken();\n        if (newToken) {\n          return newToken;\n        }\n      }\n    }\n\n    // 创建新token\n    return await this.createToken();\n  }\n\n  /**\n   * 使用refresh token刷新access token\n   */\n  async refreshToken(): Promise<string | null> {\n    if (!this.currentRefreshToken) return null;\n\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ refreshToken: this.currentRefreshToken }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.accessToken && data.refreshToken) {\n          this.setTokenPair({\n            accessToken: data.accessToken,\n            refreshToken: data.refreshToken,\n            expiresAt: data.expiresAt,\n          });\n          return data.accessToken;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to refresh token:', error);\n    }\n\n    // 刷新失败，清除token\n    this.clearToken();\n    return null;\n  }\n\n  /**\n   * 从localStorage加载tokens\n   */\n  private loadTokensFromStorage(): void {\n    if (typeof window !== 'undefined') {\n      this.currentAccessToken = localStorage.getItem(ANONYMOUS_ACCESS_TOKEN_KEY);\n      this.currentRefreshToken = localStorage.getItem(ANONYMOUS_REFRESH_TOKEN_KEY);\n    }\n  }\n\n  /**\n   * 验证token是否有效\n   */\n  private async validateToken(token: string): Promise<boolean> {\n    try {\n      // 简单的token格式验证\n      const parts = token.split('.');\n      if (parts.length !== 3) return false;\n\n      // 解析payload检查过期时间\n      const payload = JSON.parse(atob(parts[1]));\n      const now = Math.floor(Date.now() / 1000);\n\n      return payload.exp > now;\n    } catch {\n      return false;\n    }\n  }\n}\n\n/**\n * 统一的认证头获取函数\n */\nexport async function getUnifiedAuthHeaders(): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n\n  // 检查是否在客户端环境\n  if (typeof window !== 'undefined') {\n    // 1. 尝试从session获取已登录用户的token\n    try {\n      const sessionResponse = await fetch('/api/auth/session');\n      if (sessionResponse.ok) {\n        const session = await sessionResponse.json();\n        if (session?.user?.backendToken) {\n          // 检查token是否即将过期\n          if (isTokenNearExpiry(session.user.backendToken)) {\n            // 尝试刷新token\n            const refreshedToken = await refreshSessionToken(session.user.refreshToken);\n            if (refreshedToken) {\n              headers['Authorization'] = `Bearer ${refreshedToken}`;\n              return headers;\n            }\n          } else {\n            headers['Authorization'] = `Bearer ${session.user.backendToken}`;\n            return headers;\n          }\n        }\n      }\n    } catch (error) {\n      console.warn('Failed to get session:', error);\n    }\n\n    // 2. 如果没有登录用户session，使用匿名用户token\n    const anonymousManager = AnonymousTokenManager.getInstance();\n    const anonymousToken = await anonymousManager.getOrCreateToken();\n\n    if (anonymousToken) {\n      headers['Authorization'] = `Bearer ${anonymousToken}`;\n    }\n  }\n\n  return headers;\n}\n\n/**\n * 检查token是否即将过期（在过期前30分钟）\n */\nfunction isTokenNearExpiry(token: string, minutesBeforeExpiry: number = 30): boolean {\n  try {\n    const parts = token.split('.');\n    if (parts.length !== 3) return true;\n\n    const payload = JSON.parse(atob(parts[1]));\n    const expiryTime = new Date(payload.exp * 1000);\n    const warningTime = new Date(Date.now() + minutesBeforeExpiry * 60 * 1000);\n\n    return expiryTime <= warningTime;\n  } catch {\n    return true; // 如果无法解析token，认为需要刷新\n  }\n}\n\n/**\n * 刷新session中的token\n */\nasync function refreshSessionToken(refreshToken: string): Promise<string | null> {\n  if (!refreshToken) return null;\n\n  try {\n    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ refreshToken }),\n    });\n\n    if (response.ok) {\n      const data = await response.json();\n      if (data.accessToken) {\n        // 这里应该更新session，但由于NextAuth的限制，我们只能返回新token\n        // 实际应用中可能需要重新登录或使用其他方式更新session\n        return data.accessToken;\n      }\n    }\n  } catch (error) {\n    console.error('Failed to refresh session token:', error);\n  }\n\n  return null;\n}\n\n/**\n * 用户登录后清理匿名token\n */\nexport function clearAnonymousTokenOnLogin(): void {\n  const anonymousManager = AnonymousTokenManager.getInstance();\n  anonymousManager.clearToken();\n}\n\n/**\n * 用户登出后恢复匿名token\n */\nexport async function restoreAnonymousTokenOnLogout(): Promise<void> {\n  const anonymousManager = AnonymousTokenManager.getInstance();\n  await anonymousManager.getOrCreateToken();\n}\n\n/**\n * 迁移匿名用户数据到注册用户\n */\nexport async function migrateAnonymousData(): Promise<boolean> {\n  const anonymousManager = AnonymousTokenManager.getInstance();\n  const anonymousToken = anonymousManager.getToken();\n\n  if (!anonymousToken) return true; // 没有匿名数据需要迁移\n\n  try {\n    // 从匿名token中提取anonymousId\n    const payload = JSON.parse(atob(anonymousToken.split('.')[1]));\n    const anonymousId = payload.anonymousId;\n\n    if (!anonymousId) return true;\n\n    // 调用迁移API\n    const response = await fetch(`${API_BASE_URL}/api/anonymous/migrate`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        Authorization: `Bearer ${anonymousToken}`,\n      },\n      body: JSON.stringify({ anonymousId }),\n    });\n\n    if (response.ok) {\n      // 迁移成功，清理匿名token\n      anonymousManager.clearToken();\n      return true;\n    }\n  } catch (error) {\n    console.error('Failed to migrate anonymous data:', error);\n  }\n\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAGO,MAAM,cAAc,0LAAC,CAAC,MAAM,CAAC;IAClC,OAAO,0LAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,0LAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,wKAAQ,EAAC;IAC1D,WAAW;QACT,mBAAmB;QACnB,IAAA,6KAAW,EAAC;YACV,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAM,MAAM;gBAAQ;gBACpC,UAAU;oBAAE,OAAO;oBAAM,MAAM;gBAAW;YAC5C;YACA,6CAA6C;YAC7C,MAAM,WAAU,WAAW;gBACzB,IAAI;oBACF,sBAAsB;oBACtB,MAAM,kBAAkB,YAAY,SAAS,CAAC;oBAC9C,IAAI,CAAC,gBAAgB,OAAO,EAAE;wBAC5B,OAAO,MAAM,OAAO;oBACtB;oBAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;oBAEhD,wBAAwB;oBACxB,MAAM,WAAW,MAAM,MAAM,6DAAmC,eAAe,CAAC,EAAE;wBAChF,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BAAE;4BAAO;wBAAS;oBACzC;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,OAAO,MAAM,SAAS;oBACxB;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,iCAAiC;oBACjC,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW,EAAE;wBACjC,iCAAiC;wBACjC,OAAO;4BACL,IAAI,KAAK,IAAI,CAAC,EAAE;4BAChB,OAAO,KAAK,IAAI,CAAC,KAAK;4BACtB,MAAM,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,KAAK;4BAC3C,kBAAkB,KAAK,IAAI,CAAC,gBAAgB;4BAC5C,eAAe,KAAK,IAAI,CAAC,aAAa;4BACtC,cAAc,KAAK,WAAW;4BAC9B,cAAc,KAAK,YAAY;4BAC/B,gBAAgB,KAAK,SAAS;wBAChC;oBACF;oBAEA,OAAO,MAAM,aAAa;gBAC5B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;IACX,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,gBAAgB;IAChB,SAAS;QACP,cAAc;YACZ,MAAM,CAAC,uBAAuB,CAAC;YAC/B,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ,oDAAyB;YACnC;QACF;IACF;IACA,oBAAoB;IACpB,WAAW;QACT,2DAA2D;QAC3D,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,IAAI,OAAO;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,EAAG,0BAA0B;gBACxD,uCAAuC;gBACvC,QAAQ,IAAI,CAAC,gBAAgB,GAAG,AAAC,MAAM,gBAAgB,IAAe;gBACtE,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;gBAChD,mCAAmC;gBACnC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc;YACpD;YACA,OAAO;QACT;QACA,8CAA8C;QAC9C,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,yCAAyC;YACzC,IAAI,MAAM;gBACR,MAAM,IAAI;gBAOV,MAAM,gBAAgB,GAAG,EAAE,gBAAgB;gBAC3C,MAAM,aAAa,GAAG,EAAE,aAAa;gBACrC,MAAM,YAAY,GAAG,EAAE,YAAY,EAAE,gBAAgB;gBACrD,MAAM,YAAY,GAAG,EAAE,YAAY,EAAE,kBAAkB;gBACvD,MAAM,cAAc,GAAG,EAAE,cAAc,EAAE,cAAc;YACzD;YACA,OAAO;QACT;QACA,+BAA+B;QAC/B,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAC7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,UAAU;IACV,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,oBAAoB;IACpB,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE;YAC5B,QAAQ,GAAG,CAAC,mBAAmB;gBAAE,MAAM,KAAK,KAAK;gBAAE,SAAS,SAAS;YAAS;QAChF;QACA,MAAM;YACJ,QAAQ,GAAG,CAAC;QACd;IACF;IACA,eAAe;IACf,OAAO,oDAAyB;AAClC;AAqCA,qBAAqB;AAErB,MAAM;AAEN,oBAAoB;AACpB,MAAM,6BAA6B;AACnC,MAAM,8BAA8B;AAe7B,MAAM;IACX,OAAe,SAAgC;IACvC,qBAAoC,KAAK;IACzC,sBAAqC,KAAK;IAElD,aAAsB;QACpB,IAAI,CAAC,qBAAqB;IAC5B;IAEA,OAAO,cAAqC;QAC1C,IAAI,CAAC,sBAAsB,QAAQ,EAAE;YACnC,sBAAsB,QAAQ,GAAG,IAAI;QACvC;QACA,OAAO,sBAAsB,QAAQ;IACvC;IAEA;;GAEC,GACD,WAA0B;QACxB,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;GAEC,GACD,kBAAiC;QAC/B,OAAO,IAAI,CAAC,mBAAmB;IACjC;IAEA;;GAEC,GACD,aAAa,SAAoB,EAAQ;QACvC,IAAI,CAAC,kBAAkB,GAAG,UAAU,WAAW;QAC/C,IAAI,CAAC,mBAAmB,GAAG,UAAU,YAAY;QAEjD;;IAIF;IAEA;;GAEC,GACD,SAAS,KAAa,EAAQ;QAC5B,IAAI,CAAC,kBAAkB,GAAG;QAE1B;;IAGF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,mBAAmB,GAAG;QAE3B;;IAIF;IAEA;;GAEC,GACD,MAAM,cAAsC;QAC1C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;gBACnE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,EAAE;oBACzC,IAAI,CAAC,YAAY,CAAC;wBAChB,aAAa,KAAK,WAAW;wBAC7B,cAAc,KAAK,YAAY;wBAC/B,WAAW,KAAK,SAAS;oBAC3B;oBACA,OAAO,KAAK,WAAW;gBACzB;gBACA,OAAO;gBACP,IAAI,KAAK,KAAK,EAAE;oBACd,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK;oBACxB,OAAO,KAAK,KAAK;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;QACA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAA2C;QAC/C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,gBAAgB;YAChB,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,GAAG;gBACrD,OAAO,IAAI,CAAC,kBAAkB;YAChC;YAEA,uCAAuC;YACvC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;gBACxC,IAAI,UAAU;oBACZ,OAAO;gBACT;YACF;QACF;QAEA,WAAW;QACX,OAAO,MAAM,IAAI,CAAC,WAAW;IAC/B;IAEA;;GAEC,GACD,MAAM,eAAuC;QAC3C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO;QAEtC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,cAAc,IAAI,CAAC,mBAAmB;gBAAC;YAChE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,EAAE;oBACzC,IAAI,CAAC,YAAY,CAAC;wBAChB,aAAa,KAAK,WAAW;wBAC7B,cAAc,KAAK,YAAY;wBAC/B,WAAW,KAAK,SAAS;oBAC3B;oBACA,OAAO,KAAK,WAAW;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QAEA,eAAe;QACf,IAAI,CAAC,UAAU;QACf,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAA8B;QACpC;;IAIF;IAEA;;GAEC,GACD,MAAc,cAAc,KAAa,EAAoB;QAC3D,IAAI;YACF,eAAe;YACf,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;YAE/B,kBAAkB;YAClB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;YACxC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAEpC,OAAO,QAAQ,GAAG,GAAG;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAKO,eAAe;IACpB,MAAM,UAAkC,CAAC;IAEzC,aAAa;IACb;;IAkCA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,kBAAkB,KAAa,EAAE,sBAA8B,EAAE;IACxE,IAAI;QACF,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;QACxC,MAAM,aAAa,IAAI,KAAK,QAAQ,GAAG,GAAG;QAC1C,MAAM,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,sBAAsB,KAAK;QAErE,OAAO,cAAc;IACvB,EAAE,OAAM;QACN,OAAO,MAAM,qBAAqB;IACpC;AACF;AAEA;;CAEC,GACD,eAAe,oBAAoB,YAAoB;IACrD,IAAI,CAAC,cAAc,OAAO;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC/D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAa;QACtC;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,WAAW,EAAE;gBACpB,4CAA4C;gBAC5C,gCAAgC;gBAChC,OAAO,KAAK,WAAW;YACzB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;IAEA,OAAO;AACT;AAKO,SAAS;IACd,MAAM,mBAAmB,sBAAsB,WAAW;IAC1D,iBAAiB,UAAU;AAC7B;AAKO,eAAe;IACpB,MAAM,mBAAmB,sBAAsB,WAAW;IAC1D,MAAM,iBAAiB,gBAAgB;AACzC;AAKO,eAAe;IACpB,MAAM,mBAAmB,sBAAsB,WAAW;IAC1D,MAAM,iBAAiB,iBAAiB,QAAQ;IAEhD,IAAI,CAAC,gBAAgB,OAAO,MAAM,aAAa;IAE/C,IAAI;QACF,yBAAyB;QACzB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,eAAe,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5D,MAAM,cAAc,QAAQ,WAAW;QAEvC,IAAI,CAAC,aAAa,OAAO;QAEzB,UAAU;QACV,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,sBAAsB,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,gBAAgB;YAC3C;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,iBAAiB;YACjB,iBAAiB,UAAU;YAC3B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IAEA,OAAO;AACT"}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { auth } from '@/lib/auth';\n\n// 定义需要登录才能访问的受保护路径\nconst protectedPaths = ['/account', '/my-downloads'];\n\n// 定义登录后不应访问的认证路径\nconst authPaths = ['/login', '/register', '/forgot-password', '/reset-password'];\n\nexport default auth((req) => {\n  const { nextUrl } = req;\n  // 通过检查 req.auth 对象是否存在来判断用户是否登录\n  const isLoggedIn = !!req.auth;\n\n  // 检查当前请求是否为受保护路径\n  const isProtectedPath = protectedPaths.some((path) => nextUrl.pathname.startsWith(path));\n\n  // 检查当前请求是否为认证路径\n  const isAuthPath = authPaths.some((path) => nextUrl.pathname.startsWith(path));\n\n  // 如果用户未登录且访问受保护路径，则重定向到登录页并附带原始URL\n  if (isProtectedPath && !isLoggedIn) {\n    const redirectUrl = new URL('/login', nextUrl.origin);\n    redirectUrl.searchParams.set('redirect', nextUrl.pathname);\n    return NextResponse.redirect(redirectUrl);\n  }\n\n  // 如果用户已登录且访问认证路径，则重定向到首页\n  if (isAuthPath && isLoggedIn) {\n    return NextResponse.redirect(new URL('/', nextUrl.origin));\n  }\n\n  // 直接返回响应，不再处理匿名Cookie\n  // 匿名用户身份现在通过JWT token在客户端管理\n  return NextResponse.next();\n});\n\n// 配置中间件的执行路径\nexport const config = {\n  matcher: [\n    // 匹配所有请求路径，但排除 API、Next.js 静态文件、图片、图标和公共资源\n    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA,mBAAmB;AACnB,MAAM,iBAAiB;IAAC;IAAY;CAAgB;AAEpD,iBAAiB;AACjB,MAAM,YAAY;IAAC;IAAU;IAAa;IAAoB;CAAkB;uCAEjE,IAAA,2HAAI,EAAC,CAAC;IACnB,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,gCAAgC;IAChC,MAAM,aAAa,CAAC,CAAC,IAAI,IAAI;IAE7B,iBAAiB;IACjB,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAC,OAAS,QAAQ,QAAQ,CAAC,UAAU,CAAC;IAElF,gBAAgB;IAChB,MAAM,aAAa,UAAU,IAAI,CAAC,CAAC,OAAS,QAAQ,QAAQ,CAAC,UAAU,CAAC;IAExE,mCAAmC;IACnC,IAAI,mBAAmB,CAAC,YAAY;QAClC,MAAM,cAAc,IAAI,IAAI,UAAU,QAAQ,MAAM;QACpD,YAAY,YAAY,CAAC,GAAG,CAAC,YAAY,QAAQ,QAAQ;QACzD,OAAO,gMAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,yBAAyB;IACzB,IAAI,cAAc,YAAY;QAC5B,OAAO,gMAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,MAAM;IAC1D;IAEA,sBAAsB;IACtB,4BAA4B;IAC5B,OAAO,gMAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,2CAA2C;QAC3C;KACD;AACH"}}]}