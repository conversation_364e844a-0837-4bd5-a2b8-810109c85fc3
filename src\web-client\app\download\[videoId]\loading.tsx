import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function DownloadLoading() {
  return (
    <div className="bg-gradient-to-br from-background via-muted/10 to-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-10">
          {/* 左侧区域: 视频信息概览骨架 */}
          <aside className="lg:col-span-3">
            <div className="sticky top-24 space-y-6">
              {/* 视频缩略图骨架 */}
              <Card className="border-2 border-muted/20">
                <CardContent className="p-0">
                  <Skeleton className="w-full aspect-video rounded-t-lg" />
                  <div className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </CardContent>
              </Card>

              {/* 视频基本信息骨架 */}
              <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/20">
                <CardContent className="p-4">
                  {/* 视频标题骨架 */}
                  <Skeleton className="h-6 w-full mb-3" />
                  <Skeleton className="h-6 w-4/5 mb-3" />

                  {/* 频道信息骨架 */}
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-2/3" />

                    {/* 统计信息骨架 */}
                    <div className="flex items-center gap-4">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 视频描述骨架 */}
              <Card className="border-2 border-muted/20">
                <CardContent className="p-4">
                  <Skeleton className="h-5 w-1/3 mb-3" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>

              {/* 评论下载骨架 */}
              <Card className="border-2 border-muted/20">
                <CardContent className="p-4">
                  <Skeleton className="h-5 w-1/2 mb-3" />
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            </div>
          </aside>

          {/* 右侧区域: 下载选项骨架 */}
          <section className="lg:col-span-7">
            <div className="space-y-6">
              {/* 下载选项骨架 */}
              <Card className="border-2 border-muted/20">
                <CardContent className="p-6">
                  {/* 标签页骨架 */}
                  <div className="flex space-x-1 mb-6">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                  </div>

                  {/* 下载选项列表骨架 */}
                  <div className="space-y-4">
                    {/* 质量选项骨架 */}
                    {[1, 2, 3, 4].map((index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <Skeleton className="h-12 w-12 rounded" />
                          <div className="space-y-2">
                            <Skeleton className="h-5 w-32" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                        </div>
                        <Skeleton className="h-10 w-20" />
                      </div>
                    ))}
                  </div>

                  {/* 下载按钮骨架 */}
                  <div className="mt-6 pt-6 border-t">
                    <Skeleton className="h-12 w-full" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>
        </div>

        {/* 加载提示 */}
        <div className="fixed bottom-8 right-8">
          <Card className="border-0 shadow-lg bg-background/95 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary border-t-transparent"></div>
                <span className="text-sm font-medium">正在加载视频信息...</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
