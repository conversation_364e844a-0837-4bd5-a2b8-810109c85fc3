using Api.Services;
using Microsoft.AspNetCore.Mvc;

namespace Api.Endpoints;

public static class AuthEndpoints
{
    public static void MapAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var authGroup = app.MapGroup("/api/auth").WithTags("Authentication");
        var anonymousGroup = app.MapGroup("/api/anonymous").WithTags("Anonymous Users");

        // 认证端点
        authGroup.MapPost("/register", async ([FromBody] RegisterRequest request, AuthService authService) =>
        {
            // 验证邮箱是否已存在
            if (await authService.IsEmailExistsAsync(request.Email)) return Results.BadRequest(new { error = "EMAIL_ALREADY_EXISTS" });

            var user = await authService.CreateRegisteredUserAsync(request.Email, request.Password, request.Username);

            // 如果提供了匿名用户ID，进行数据迁移
            if (!string.IsNullOrEmpty(request.AnonymousId))
            {
                var (success, tasksCount, batchJobsCount) = await authService.MigrateAnonymousDataAsync(request.AnonymousId, user.Id);
                if (success)
                    return Results.Ok(new
                    {
                        user = new { user.Id, user.Email, user.Username, user.SubscriptionTier },
                        requiresEmailVerification = false,
                        migration = new { migratedTasksCount = tasksCount, migratedBatchJobsCount = batchJobsCount }
                    });
            }

            return Results.Ok(new { user = new { user.Id, user.Email, user.Username, user.SubscriptionTier }, requiresEmailVerification = false });
        }).WithSummary("用户注册").WithDescription("注册新用户账户，支持匿名用户数据迁移");

        authGroup.MapPost("/login", async ([FromBody] LoginRequest request, AuthService authService) =>
        {
            var user = await authService.ValidateUserCredentialsAsync(request.Email, request.Password);

            if (user == null) return Results.BadRequest(new { error = "INVALID_CREDENTIALS" });

            var tokenPair = authService.GenerateTokenPair(user);

            return Results.Ok(new
            {
                user = new
                {
                    user.Id,
                    user.Email,
                    user.Username,
                    user.SubscriptionTier,
                    user.EmailVerified
                },
                accessToken = tokenPair.AccessToken,
                refreshToken = tokenPair.RefreshToken,
                expiresAt = tokenPair.ExpiresAt
            });
        }).WithSummary("用户登录").WithDescription("验证用户凭据并返回JWT令牌");

        authGroup.MapPost("/logout", () =>
        {
            // Auth.js 会处理会话清理
            return Results.Ok(new { success = true });
        }).WithSummary("用户退出").WithDescription("退出当前用户会话");

        authGroup.MapPost("/refresh", async ([FromBody] RefreshTokenRequest request, AuthService authService) =>
        {
            if (string.IsNullOrEmpty(request.RefreshToken)) return Results.BadRequest(new { error = "REFRESH_TOKEN_REQUIRED" });

            var tokenPair = await authService.RefreshTokenAsync(request.RefreshToken);

            if (tokenPair == null) return Results.Unauthorized();

            return Results.Ok(new
            {
                accessToken = tokenPair.AccessToken,
                refreshToken = tokenPair.RefreshToken,
                expiresAt = tokenPair.ExpiresAt,
                // 保持向后兼容
                token = tokenPair.AccessToken
            });
        }).WithSummary("刷新访问令牌").WithDescription("使用refresh token获取新的access token");

        authGroup.MapGet("/me", async (AuthService authService, HttpContext httpContext) =>
        {
            // 从 Auth.js 会话中获取用户ID
            var userIdClaim = httpContext.User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId)) return Results.Unauthorized();

            var user = await authService.GetUserByIdAsync(userId);

            if (user == null) return Results.Unauthorized();

            return Results.Ok(new
            {
                user.Id,
                user.Email,
                user.Username,
                user.SubscriptionTier,
                user.EmailVerified,
                user.CreatedAt
            });
        }).WithSummary("获取当前用户信息").WithDescription("获取当前登录用户的详细信息").RequireAuthorization();

        // 匿名用户端点
        anonymousGroup.MapPost("/create", async (AuthService authService) =>
        {
            var result = await authService.CreateAnonymousUserAsync();
            var tokenPair = authService.GenerateTokenPair(result.User);

            return Results.Ok(new
            {
                anonymousId = result.AnonymousId,
                accessToken = tokenPair.AccessToken,
                refreshToken = tokenPair.RefreshToken,
                expiresAt = tokenPair.ExpiresAt
            });
        }).WithSummary("创建匿名用户").WithDescription("为未注册用户创建匿名身份标识并返回JWT token");

        anonymousGroup.MapPost("/token", async ([FromBody] GetAnonymousTokenRequest request, AuthService authService) =>
        {
            var anonymousUser = await authService.GetAnonymousUserAsync(request.AnonymousId ?? "");

            if (anonymousUser == null)
            {
                // 如果匿名用户不存在，创建新的
                var result = await authService.CreateAnonymousUserAsync();
                var newTokenPair = authService.GenerateTokenPair(result.User);

                return Results.Ok(new
                {
                    anonymousId = result.AnonymousId,
                    accessToken = newTokenPair.AccessToken,
                    refreshToken = newTokenPair.RefreshToken,
                    expiresAt = newTokenPair.ExpiresAt
                });
            }

            var tokenPair = authService.GenerateTokenPair(anonymousUser);

            return Results.Ok(new
            {
                anonymousId = anonymousUser.AnonymousId,
                accessToken = tokenPair.AccessToken,
                refreshToken = tokenPair.RefreshToken,
                expiresAt = tokenPair.ExpiresAt
            });
        }).WithSummary("获取匿名用户JWT token").WithDescription("为现有匿名用户获取或创建JWT token");

        anonymousGroup.MapPost("/migrate", async ([FromBody] MigrateRequest request, AuthService authService, HttpContext httpContext) =>
        {
            // 验证用户已认证
            var userIdClaim = httpContext.User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId)) return Results.Unauthorized();

            var (success, tasksCount, batchJobsCount) = await authService.MigrateAnonymousDataAsync(request.AnonymousId, userId);

            if (!success) return Results.BadRequest(new { error = "MIGRATION_FAILED" });

            return Results.Ok(new { success = true, migratedTasksCount = tasksCount, migratedBatchJobsCount = batchJobsCount });
        }).WithSummary("迁移匿名用户数据").WithDescription("将匿名用户的数据迁移到注册用户账户").RequireAuthorization();

        anonymousGroup.MapPost("/update-activity", async ([FromBody] UpdateActivityRequest request, AuthService authService) =>
        {
            await authService.UpdateLastActiveAsync(request.AnonymousId);
            return Results.Ok(new { success = true });
        }).WithSummary("更新匿名用户活跃时间").WithDescription("更新匿名用户的最后活跃时间");
    }

    // DTOs
    public record RegisterRequest(string Email, string Password, string? Username = null, string? AnonymousId = null);

    public record LoginRequest(string Email, string Password);

    public record RefreshTokenRequest(string RefreshToken);

    public record MigrateRequest(string AnonymousId);

    public record UpdateActivityRequest(string AnonymousId);

    public record GetAnonymousTokenRequest(string? AnonymousId = null);
}