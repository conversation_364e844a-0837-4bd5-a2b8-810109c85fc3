import { ArrowRight, Crown } from 'lucide-react';
import Link from 'next/link';

interface ToolCtaSectionProps {
  title?: string;
  description?: string;
  buttonText?: string;
  features?: string[];
  className?: string;
  href?: string;
}

export function ToolCtaSection({
  title = '升级到专业版，解锁更多功能',
  description = '获得更高质量、更多格式选择、批量处理和优先支持，提升您的工作效率',
  buttonText = '立即升级专业版',
  features = ['4K超高清下载', '批量处理', '无广告体验', '优先客服支持'],
  className = '',
  href = '/pricing',
}: ToolCtaSectionProps) {
  return (
    <div
      className={`bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-white text-center relative overflow-hidden ${className}`}>
      {/* 背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12" />

      <div className="max-w-3xl mx-auto relative z-10">
        {/* 皇冠图标 */}
        <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-400 rounded-full mb-4">
          <Crown className="h-8 w-8 text-yellow-900" />
        </div>

        <h3 className="text-2xl font-bold mb-4">{title}</h3>
        <p className="text-lg mb-6 opacity-90">{description}</p>

        {features.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center justify-center gap-2 bg-white/20 rounded-lg py-3 px-4">
                <span className="text-yellow-300">✨</span>
                <span className="text-sm font-medium">{feature}</span>
              </div>
            ))}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
          <div className="text-sm opacity-75">💎 专业版特权 | 🚀 更快处理 | 🎯 专属功能</div>
        </div>

        <Link
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center justify-center bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-300 hover:to-orange-400 px-8 py-3 text-lg font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200 rounded-lg">
          <Crown className="mr-2 h-5 w-5" />
          {buttonText}
          <ArrowRight className="ml-2 h-5 w-5" />
        </Link>

        <p className="text-xs opacity-60 mt-4">30天无条件退款保证 | 随时可取消</p>
      </div>
    </div>
  );
}
