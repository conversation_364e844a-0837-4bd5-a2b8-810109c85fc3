using System.Text;
using Api.Data;
using Api.Endpoints;
using Api.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

// 数据库配置
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")).UseSnakeCaseNamingConvention());

// JWT 认证配置
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]!))
    };
});

builder.Services.AddAuthorization();

// 注册服务
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<YouTubeService>();

// API 文档
builder.Services.AddOpenApi();

// CORS 配置
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        policy => { policy.WithOrigins("http://localhost:3000", "https://localhost:3000").AllowAnyMethod().AllowAnyHeader().AllowCredentials(); });
});

var app = builder.Build();

// 开发环境配置
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

// 启用 CORS
app.UseCors("AllowFrontend");

// 启用认证和授权
app.UseAuthentication();
app.UseAuthorization();

// 映射端点
app.MapAuthEndpoints();
app.MapYouTubeEndpoints();
app.MapBatchJobEndpoints();

// 健康检查端点
app.MapGet("/", () => Results.Ok(new { status = "Backend is running", timestamp = DateTime.UtcNow })).WithTags("Health");

app.Run();