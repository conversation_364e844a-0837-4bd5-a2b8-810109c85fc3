'use client';

import { useState } from 'react';
import { BatchDownloadConfig, BatchJobDetails } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Download, Pause, RotateCcw, Settings, Square } from 'lucide-react';
import { cancelBatchJob, pauseBatchJob, startBatchJob } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface BatchJobConfigProps {
  jobData: BatchJobDetails;
  isJobStarted: boolean;
  onJobStart: () => void;
  onJobUpdate: (data: Partial<BatchJobDetails>) => void;
}

export function BatchJobConfig({
  jobData,
  isJobStarted,
  onJobStart,
  onJobUpdate,
}: BatchJobConfigProps) {
  const [config, setConfig] = useState<BatchDownloadConfig>(jobData.config);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const selectedCount = jobData.videos.filter((v) => v.selected).length;
  const canStart =
    selectedCount > 0 && Object.values(config.contentTypes).some(Boolean) && !isJobStarted;

  const handleConfigChange = (newConfig: Partial<BatchDownloadConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
    onJobUpdate({ config: updatedConfig });
  };

  const handleContentTypeChange = (
    type: keyof BatchDownloadConfig['contentTypes'],
    enabled: boolean,
  ) => {
    handleConfigChange({
      contentTypes: {
        ...config.contentTypes,
        [type]: enabled,
      },
    });
  };

  const handleStartJob = async () => {
    setIsLoading(true);
    try {
      const selectedVideoIds = jobData.videos.filter((v) => v.selected).map((v) => v.id);
      const success = await startBatchJob(jobData.id, {
        selectedVideoIds,
        config,
      });

      if (success) {
        onJobStart();
        toast({
          title: '任务已启动',
          description: `开始处理 ${selectedCount} 个视频`,
        });
      } else {
        toast({
          title: '启动失败',
          description: '无法启动批量下载任务，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: '启动失败',
        description: '网络错误，请检查连接后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePauseJob = async () => {
    setIsLoading(true);
    try {
      const success = await pauseBatchJob(jobData.id);
      if (success) {
        onJobUpdate({ status: 'partially_completed' });
        toast({
          title: '任务已暂停',
          description: '批量下载任务已暂停',
        });
      }
    } catch (error) {
      toast({
        title: '操作失败',
        description: '无法暂停任务',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelJob = async () => {
    setIsLoading(true);
    try {
      const success = await cancelBatchJob(jobData.id);
      if (success) {
        onJobUpdate({ status: 'cancelled' });
        toast({
          title: '任务已取消',
          description: '批量下载任务已取消',
        });
      }
    } catch (error) {
      toast({
        title: '操作失败',
        description: '无法取消任务',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetConfig = () => {
    const defaultConfig: BatchDownloadConfig = {
      contentTypes: {
        video: true,
        audio: false,
        subtitle: false,
        comment: false,
        thumbnail: false,
        description: false,
      },
      videoQuality: 'best',
      audioQuality: 'best',
      subtitleLanguages: 'all',
      commentSettings: {
        sortBy: 'top',
        maxCount: 100,
        format: 'xlsx',
      },
      thumbnailFormat: 'jpg',
      destination: {
        type: 'browser',
        createSubfolders: true,
      },
    };
    setConfig(defaultConfig);
    onJobUpdate({ config: defaultConfig });
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            全局配置
          </CardTitle>
          {!isJobStarted && (
            <Button variant="ghost" size="sm" onClick={resetConfig} className="h-8 px-2">
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 下载内容类型和配置 */}
        <div className="space-y-4">
          <Label className="text-sm font-medium">下载内容类型</Label>
          <div className="space-y-4">
            {/* 视频配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="video" className="text-sm">
                  视频
                </Label>
                <Switch
                  id="video"
                  checked={config.contentTypes.video}
                  onCheckedChange={(checked) => handleContentTypeChange('video', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.video && (
                <div className="ml-4 space-y-2">
                  <Label className="text-xs text-muted-foreground">视频质量</Label>
                  <Select
                    value={config.videoQuality}
                    onValueChange={(value) => handleConfigChange({ videoQuality: value as any })}
                    disabled={isJobStarted}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="best">最佳质量</SelectItem>
                      <SelectItem value="1080p">1080p</SelectItem>
                      <SelectItem value="720p">720p</SelectItem>
                      <SelectItem value="480p">480p</SelectItem>
                      <SelectItem value="360p">360p</SelectItem>
                      <SelectItem value="worst">最低质量</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* 音频配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="audio" className="text-sm">
                  音频
                </Label>
                <Switch
                  id="audio"
                  checked={config.contentTypes.audio}
                  onCheckedChange={(checked) => handleContentTypeChange('audio', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.audio && (
                <div className="ml-4 space-y-2">
                  <Label className="text-xs text-muted-foreground">音频质量</Label>
                  <Select
                    value={config.audioQuality}
                    onValueChange={(value) => handleConfigChange({ audioQuality: value as any })}
                    disabled={isJobStarted}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="best">最佳原始格式 (M4A/WebM)</SelectItem>
                      <SelectItem value="mp3_320">MP3 320kbps</SelectItem>
                      <SelectItem value="mp3_192">MP3 192kbps</SelectItem>
                      <SelectItem value="mp3_128">MP3 128kbps</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* 字幕配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="subtitle" className="text-sm">
                  字幕
                </Label>
                <Switch
                  id="subtitle"
                  checked={config.contentTypes.subtitle}
                  onCheckedChange={(checked) => handleContentTypeChange('subtitle', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.subtitle && (
                <div className="ml-4 space-y-2">
                  <Label className="text-xs text-muted-foreground">字幕语言</Label>
                  <Select
                    value={
                      Array.isArray(config.subtitleLanguages) ? 'custom' : config.subtitleLanguages
                    }
                    onValueChange={(value) =>
                      handleConfigChange({
                        subtitleLanguages: value === 'all' ? 'all' : ['zh-Hans', 'en'],
                      })
                    }
                    disabled={isJobStarted}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">下载所有可用语言</SelectItem>
                      <SelectItem value="custom">仅中文和英文</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* 评论配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="comment" className="text-sm">
                  评论
                </Label>
                <Switch
                  id="comment"
                  checked={config.contentTypes.comment}
                  onCheckedChange={(checked) => handleContentTypeChange('comment', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.comment && (
                <div className="ml-4 space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">排序方式</Label>
                      <Select
                        value={config.commentSettings.sortBy}
                        onValueChange={(value: 'top' | 'newest') =>
                          handleConfigChange({
                            commentSettings: { ...config.commentSettings, sortBy: value },
                          })
                        }
                        disabled={isJobStarted}>
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="top">热门评论</SelectItem>
                          <SelectItem value="newest">最新评论</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">数量限制</Label>
                      <Select
                        value={config.commentSettings.maxCount.toString()}
                        onValueChange={(value) =>
                          handleConfigChange({
                            commentSettings: {
                              ...config.commentSettings,
                              maxCount: parseInt(value),
                            },
                          })
                        }
                        disabled={isJobStarted}>
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="100">100条</SelectItem>
                          <SelectItem value="500">500条 (Pro)</SelectItem>
                          <SelectItem value="1000">1000条 (Pro)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">输出格式</Label>
                    <Select
                      value={config.commentSettings.format}
                      onValueChange={(value: 'xlsx' | 'csv' | 'txt') =>
                        handleConfigChange({
                          commentSettings: { ...config.commentSettings, format: value },
                        })
                      }
                      disabled={isJobStarted}>
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="xlsx">Excel (.xlsx)</SelectItem>
                        <SelectItem value="csv">CSV (.csv)</SelectItem>
                        <SelectItem value="txt">文本 (.txt)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>

            {/* 缩略图配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="thumbnail" className="text-sm">
                  缩略图
                </Label>
                <Switch
                  id="thumbnail"
                  checked={config.contentTypes.thumbnail}
                  onCheckedChange={(checked) => handleContentTypeChange('thumbnail', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.thumbnail && (
                <div className="ml-4 space-y-2">
                  <Label className="text-xs text-muted-foreground">图片格式</Label>
                  <Select
                    value={config.thumbnailFormat}
                    onValueChange={(value: 'jpg' | 'png') =>
                      handleConfigChange({ thumbnailFormat: value })
                    }
                    disabled={isJobStarted}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpg">JPEG (.jpg)</SelectItem>
                      <SelectItem value="png">PNG (.png)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* 描述配置 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="description" className="text-sm">
                  视频描述
                </Label>
                <Switch
                  id="description"
                  checked={config.contentTypes.description}
                  onCheckedChange={(checked) => handleContentTypeChange('description', checked)}
                  disabled={isJobStarted}
                />
              </div>
              {config.contentTypes.description && (
                <div className="ml-4">
                  <p className="text-xs text-muted-foreground">将以 TXT 格式保存视频描述信息</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <Separator />

        {/* 下载目的地设置 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">下载目的地</Label>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="browser-download"
                name="destination"
                checked={config.destination.type === 'browser'}
                onChange={() =>
                  handleConfigChange({
                    destination: { ...config.destination, type: 'browser' },
                  })
                }
                disabled={isJobStarted}
                className="h-4 w-4"
              />
              <Label htmlFor="browser-download" className="text-sm">
                标准浏览器下载
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="local-folder"
                name="destination"
                checked={config.destination.type === 'local_folder'}
                onChange={() =>
                  handleConfigChange({
                    destination: { ...config.destination, type: 'local_folder' },
                  })
                }
                disabled={isJobStarted}
                className="h-4 w-4"
              />
              <Label htmlFor="local-folder" className="text-sm">
                本地文件夹保存
              </Label>
            </div>
            {config.destination.type === 'local_folder' && (
              <div className="ml-6 space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="create-subfolders"
                    checked={config.destination.createSubfolders}
                    onCheckedChange={(checked) =>
                      handleConfigChange({
                        destination: { ...config.destination, createSubfolders: checked },
                      })
                    }
                    disabled={isJobStarted}
                  />
                  <Label htmlFor="create-subfolders" className="text-xs">
                    为每个视频创建单独子文件夹
                  </Label>
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* 启动按钮 */}
        <div className="space-y-3">
          {!isJobStarted ? (
            <Button
              onClick={handleStartJob}
              disabled={!canStart || isLoading}
              className="w-full"
              size="lg">
              <Download className="mr-2 h-4 w-4" />
              开始下载 ({selectedCount} 个视频)
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                onClick={handlePauseJob}
                disabled={isLoading}
                variant="outline"
                className="flex-1">
                <Pause className="mr-2 h-4 w-4" />
                暂停
              </Button>
              <Button
                onClick={handleCancelJob}
                disabled={isLoading}
                variant="destructive"
                className="flex-1">
                <Square className="mr-2 h-4 w-4" />
                取消
              </Button>
            </div>
          )}

          {!canStart && !isJobStarted && (
            <p className="text-xs text-muted-foreground text-center">
              {selectedCount === 0 ? '请至少选择一个视频' : '请至少选择一种下载内容类型'}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function getContentTypeLabel(type: keyof BatchDownloadConfig['contentTypes']): string {
  const labels = {
    video: '视频',
    audio: '音频',
    subtitle: '字幕',
    comment: '评论',
    thumbnail: '缩略图',
    description: '描述',
  };
  return labels[type];
}
