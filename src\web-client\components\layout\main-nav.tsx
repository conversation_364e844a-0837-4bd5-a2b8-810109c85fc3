import Link from 'next/link';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { getAllTools } from '@/lib/tools-config';
import { cn } from '@/lib/utils';
import { ArrowRight } from 'lucide-react';

export function MainNav() {
  const tools = getAllTools();

  return (
    <nav className="hidden items-center space-x-8 text-sm md:flex ml-12">
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger className="text-sm font-normal flex items-center gap-2 [&>svg:last-child]:ml-0">
              所有功能
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <div className="w-[700px] p-4">
                <div className="grid grid-cols-2 gap-2">
                  {tools.map((tool) => (
                    <NavigationMenuLink key={tool.slug} asChild>
                      <Link
                        href={`/${tool.slug}`}
                        className={cn(
                          'group block select-none rounded-lg p-3 leading-none no-underline outline-none transition-all duration-100',
                          'hover:bg-gradient-to-r hover:from-primary/5 hover:to-blue-500/5 hover:shadow-sm',
                          'border border-transparent hover:border-primary/20',
                          'transform hover:scale-[1.01]',
                        )}>
                        <div className="flex items-start gap-2">
                          <div className="p-1.5 rounded-md bg-primary/0 text-primary group-hover:bg-primary group-hover:text-white transition-colors duration-100">
                            <tool.iconComponent className="h-3.5 w-3.5" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-1 mb-1">
                              <span className="text-sm font-medium leading-none group-hover:text-primary transition-colors duration-100">
                                {tool.title}
                              </span>
                              <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-100" />
                            </div>
                            <p className="line-clamp-2 text-xs leading-relaxed text-muted-foreground group-hover:text-foreground/80 transition-colors duration-100">
                              {tool.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </NavigationMenuLink>
                  ))}
                </div>
              </div>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>

      <Link href="/pricing" className="font-normal flex items-center gap-1">
        价格
      </Link>
    </nav>
  );
}
