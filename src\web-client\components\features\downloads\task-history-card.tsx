'use client';

import Link from 'next/link';
import { TaskHistoryItem } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  ExternalLink,
  FileText,
  HardDrive,
  Loader2,
  Pause,
  Users,
  XCircle,
} from 'lucide-react';
import { formatFileSize, formatRelativeTime } from '@/lib/formatters';

interface TaskHistoryCardProps {
  task: TaskHistoryItem;
}

export function TaskHistoryCard({ task }: TaskHistoryCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      case 'pending':
        return <Pause className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500">已完成</Badge>;
      case 'running':
        return <Badge className="bg-blue-500">进行中</Badge>;
      case 'failed':
        return <Badge variant="destructive">失败</Badge>;
      case 'cancelled':
        return <Badge variant="outline">已取消</Badge>;
      case 'pending':
        return <Badge variant="secondary">待处理</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'single':
        return <FileText className="h-4 w-4 text-muted-foreground" />;
      case 'batch':
        return <Users className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Download className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'single':
        return '单次下载';
      case 'batch':
        return '批量下载';
      default:
        return type;
    }
  };

  const getTaskUrl = (task: TaskHistoryItem) => {
    if (task.type === 'batch') {
      return `/batch/${task.id}`;
    } else {
      // 从 sourceUrl 中提取视频 ID
      const videoId = task.sourceUrl?.match(
        /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/,
      )?.[1];
      return videoId ? `/download/${videoId}` : null;
    }
  };

  const taskUrl = getTaskUrl(task);

  return (
    <Card className="hover:border-primary/50 transition-colors border-2">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          {/* 任务类型图标 */}
          <div className="flex-shrink-0 mt-1">{getTypeIcon(task.type)}</div>

          {/* 主要内容 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold line-clamp-2 mb-2">{task.title}</h3>

                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    {getTypeIcon(task.type)}
                    <span>{getTypeLabel(task.type)}</span>
                  </div>

                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatRelativeTime(task.createdAt)}</span>
                  </div>

                  {task.videoCount && task.videoCount > 1 && (
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{task.videoCount} 个视频</span>
                    </div>
                  )}

                  {task.totalFileSize && task.totalFileSize > 0 && (
                    <div className="flex items-center gap-1">
                      <HardDrive className="h-4 w-4" />
                      <span>{formatFileSize(task.totalFileSize)}</span>
                    </div>
                  )}
                </div>

                {/* 进度条（仅在运行中时显示） */}
                {task.status === 'running' && task.progress > 0 && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>进度</span>
                      <span>{task.progress}%</span>
                    </div>
                    <Progress value={task.progress} className="h-2" />
                  </div>
                )}

                {/* 批量任务统计 */}
                {task.type === 'batch' && (task.completedVideoCount || task.failedVideoCount) && (
                  <div className="flex flex-wrap gap-3 text-sm text-muted-foreground mb-3">
                    {task.completedVideoCount && task.completedVideoCount > 0 && (
                      <span className="text-green-600">✓ {task.completedVideoCount} 已完成</span>
                    )}
                    {task.failedVideoCount && task.failedVideoCount > 0 && (
                      <span className="text-red-600">✗ {task.failedVideoCount} 失败</span>
                    )}
                  </div>
                )}

                {/* 完成时间 */}
                {task.completedAt && (
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-3">
                    <Clock className="h-4 w-4" />
                    <span>完成于 {new Date(task.completedAt).toLocaleString()}</span>
                  </div>
                )}
              </div>

              {/* 状态和操作 */}
              <div className="flex flex-col items-end gap-3">
                <div className="flex items-center gap-2">
                  {getStatusIcon(task.status)}
                  {getStatusBadge(task.status)}
                </div>

                <div className="flex gap-2">
                  {/* 查看详情按钮 */}
                  {taskUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={taskUrl}>
                        <ExternalLink className="mr-1 h-4 w-4" />
                        查看详情
                      </Link>
                    </Button>
                  )}

                  {/* 重新下载按钮（仅失败任务） */}
                  {task.status === 'failed' && task.sourceUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={task.sourceUrl} target="_blank" rel="noopener noreferrer">
                        <Download className="mr-1 h-4 w-4" />
                        重新下载
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* 源链接 */}
            {task.sourceUrl && (
              <div className="mt-3 pt-3 border-t">
                <div className="flex items-center gap-2 text-sm">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={task.sourceUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline line-clamp-1">
                    {task.sourceUrl}
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
