'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Crown, Download, Loader2, LogOut, Settings } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { LoginModal } from '@/components/features/auth/login-modal';

export function UserNav() {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: '已退出登录',
        description: '您已成功退出登录',
      });
    } catch {
      toast({
        title: '退出失败',
        description: '退出登录时出现错误',
        variant: 'destructive',
      });
    }
  };

  const getUserInitials = () => {
    if (!user) return 'U';
    if (user.name) {
      return user.name.slice(0, 2).toUpperCase();
    }
    return user.email.slice(0, 2).toUpperCase();
  };

  const getUserDisplayName = () => {
    if (!user) return '';
    return user.name || user.email.split('@')[0];
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center">
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    );
  }

  // 已登录状态
  if (isAuthenticated && user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.image || undefined} alt={getUserDisplayName()} />
              <AvatarFallback>{getUserInitials()}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuItem className="font-normal">
            <div className="flex flex-col space-y-1">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium leading-none">{getUserDisplayName()}</p>
                {user.subscriptionTier === 'Pro' && (
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                    <Crown className="mr-1 h-3 w-3" />
                    PRO
                  </Badge>
                )}
              </div>
              <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
            </div>
          </DropdownMenuItem>
          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link href="/my-downloads" className="flex items-center">
              <Download className="mr-2 h-4 w-4" />
              我的下载
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/account" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              账户设置
            </Link>
          </DropdownMenuItem>

          {user.subscriptionTier === 'Free' && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/account?tab=subscription" className="flex items-center text-primary">
                  <Crown className="mr-2 h-4 w-4" />
                  升级到专业版
                </Link>
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout} className="flex items-center text-destructive">
            <LogOut className="mr-2 h-4 w-4" />
            退出登录
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // 未登录状态
  return (
    <>
      <div className="flex items-center">
        <Button size="sm" onClick={() => setIsLoginModalOpen(true)}>
          登录
        </Button>
      </div>

      {/* 登录模态窗口 */}
      <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
    </>
  );
}
