'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { BatchJobDetails, BatchVideoItem } from '@/lib/types';
import { getBatchJobDetails } from '@/lib/api';

interface UseBatchJobOptions {
  batchJobId: string;
  initialData?: BatchJobDetails;
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
}

interface UseBatchJobReturn {
  jobData: BatchJobDetails | null;
  isLoading: boolean;
  error: string | null;
  updateJobData: (updates: Partial<BatchJobDetails>) => void;
  updateVideoData: (videoId: string, updates: Partial<BatchVideoItem>) => void;
  refreshJobData: () => Promise<void>;
  isRealTimeActive: boolean;
  toggleRealTime: () => void;
}

export function useBatchJob({
  batchJobId,
  initialData,
  enableRealTimeUpdates = true,
  updateInterval = 3000,
}: UseBatchJobOptions): UseBatchJobReturn {
  const [jobData, setJobData] = useState<BatchJobDetails | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [isRealTimeActive, setIsRealTimeActive] = useState(enableRealTimeUpdates);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isJobRunningRef = useRef(false);

  // 检查任务是否正在运行
  const checkIfJobRunning = useCallback((data: BatchJobDetails) => {
    return data.status === 'running' || data.status === 'partially_completed';
  }, []);

  // 更新任务数据
  const updateJobData = useCallback((updates: Partial<BatchJobDetails>) => {
    setJobData((prev) => {
      if (!prev) return null;
      const updated = { ...prev, ...updates };

      // 重新计算统计信息
      if (updates.videos) {
        const selectedVideos = updated.videos.filter((v) => v.selected);
        updated.stats = {
          totalSelected: selectedVideos.length,
          completed: selectedVideos.filter((v) => v.status === 'completed').length,
          processing: selectedVideos.filter((v) => v.status === 'processing').length,
          failed: selectedVideos.filter((v) => v.status === 'failed').length,
          queued: selectedVideos.filter((v) => v.status === 'queued').length,
          pending: selectedVideos.filter((v) => v.status === 'pending').length,
        };

        // 计算整体进度
        if (updated.stats.totalSelected > 0) {
          updated.progress = Math.round(
            (updated.stats.completed / updated.stats.totalSelected) * 100,
          );
        }
      }

      return updated;
    });
  }, []);

  // 更新单个视频数据
  const updateVideoData = useCallback((videoId: string, updates: Partial<BatchVideoItem>) => {
    setJobData((prev) => {
      if (!prev) return null;

      const updatedVideos = prev.videos.map((video) =>
        video.id === videoId ? { ...video, ...updates } : video,
      );

      return {
        ...prev,
        videos: updatedVideos,
      };
    });
  }, []);

  // 刷新任务数据
  const refreshJobData = useCallback(async () => {
    try {
      setError(null);
      const data = await getBatchJobDetails(batchJobId);

      if (data) {
        setJobData(data);
        isJobRunningRef.current = checkIfJobRunning(data);
      } else {
        setError('无法获取任务数据');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
      console.error('Failed to refresh job data:', err);
    }
  }, [batchJobId, checkIfJobRunning]);

  // 初始加载数据
  useEffect(() => {
    if (!initialData) {
      setIsLoading(true);
      refreshJobData().finally(() => setIsLoading(false));
    }
  }, [initialData, refreshJobData]);

  // 实时更新逻辑
  useEffect(() => {
    if (!isRealTimeActive || !jobData) return;

    const shouldUpdate = checkIfJobRunning(jobData);
    isJobRunningRef.current = shouldUpdate;

    if (shouldUpdate) {
      intervalRef.current = setInterval(async () => {
        try {
          const data = await getBatchJobDetails(batchJobId);
          if (data) {
            setJobData(data);

            // 如果任务已完成，停止实时更新
            if (!checkIfJobRunning(data)) {
              isJobRunningRef.current = false;
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }
            }
          }
        } catch (err) {
          console.error('Real-time update failed:', err);
        }
      }, updateInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isRealTimeActive, jobData, batchJobId, updateInterval, checkIfJobRunning]);

  // 切换实时更新
  const toggleRealTime = useCallback(() => {
    setIsRealTimeActive((prev) => !prev);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    jobData,
    isLoading,
    error,
    updateJobData,
    updateVideoData,
    refreshJobData,
    isRealTimeActive,
    toggleRealTime,
  };
}
