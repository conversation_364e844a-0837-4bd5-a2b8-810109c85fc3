import { AlertTriangle, Home, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';

interface ErrorDisplayProps {
  title: string;
  description: string;
  showHomeButton?: boolean;
  homeButtonText?: string;
  showRefreshButton?: boolean;
  refreshButtonText?: string;
  onRefresh?: () => void;
  className?: string;
  children?: React.ReactNode;
}

export function ErrorDisplay({
  title,
  description,
  showHomeButton = true,
  homeButtonText = '返回首页',
  showRefreshButton = false,
  refreshButtonText = '重试',
  onRefresh,
  className = '',
  children,
}: ErrorDisplayProps) {
  return (
    <div className={`flex min-h-[60vh] items-center justify-center p-4 ${className}`}>
      <div className="w-full max-w-md">
        <Card className="border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20">
          <CardContent className="p-8 text-center space-y-6">
            {/* 错误图标 */}
            <div className="flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-destructive/20 rounded-full blur-xl"></div>
                <div className="relative bg-destructive/10 p-4 rounded-full">
                  <AlertTriangle className="h-12 w-12 text-destructive" />
                </div>
              </div>
            </div>

            {/* 标题和描述 */}
            <div className="space-y-3">
              <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
              <p className="text-muted-foreground leading-relaxed">{description}</p>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              {showRefreshButton && onRefresh && (
                <Button
                  onClick={onRefresh}
                  className="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {refreshButtonText}
                </Button>
              )}
              {showHomeButton && (
                <Button
                  asChild
                  variant={showRefreshButton ? 'outline' : 'default'}
                  className={`flex-1 ${!showRefreshButton ? 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105' : 'hover:bg-accent/80'}`}>
                  <Link href="/">
                    <Home className="mr-2 h-4 w-4" />
                    {homeButtonText}
                  </Link>
                </Button>
              )}
            </div>

            {/* 自定义内容 */}
            {children && <div className="pt-4 border-t border-border/50">{children}</div>}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
