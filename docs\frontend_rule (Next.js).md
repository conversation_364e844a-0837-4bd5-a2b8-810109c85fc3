# 前端开发规范 (Next.js 15)
> 技术栈: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS, Shadcn/ui

## 1. 核心原则
- **RSC 优先**: 默认使用 Server Components。仅在需要交互、状态管理或浏览器 API 时才使用 `'use client'`。客户端组件应保持小巧并尽可能靠近组件树的叶子节点。
- **关注点分离**: 清晰地分离 UI、业务逻辑和状态管理。
- **移动优先**: 使用 Tailwind CSS 进行响应式设计，优先满足移动端体验。
- **可访问性 (A11y)**: 严格遵循 WCAG 2.1 AA 标准，使用语义化 HTML 和 ARIA 属性。
- **性能至上**: 关注核心 Web Vitals，充分利用 Next.js 的优化策略（缓存、代码分割、图片/字体优化），并最小化客户端 JavaScript 体积。
- **函数式编程**: 坚持使用函数组件和 Hooks，优先考虑数据结构的不可变性。

## 2. 项目结构与代码风格
- **命名规范**:
  - **目录/文件**: `kebab-case` (短横线命名)
  - **组件/类型/接口**: `PascalCase` (大驼峰命名)
  - **变量/函数/Hooks**: `camelCase` (小驼峰命名)
  - **常量**: `UPPER_SNAKE_CASE` (大写下划线命名)
- **代码风格**:
  - 强制使用 Prettier 和 ESLint (基于 `next/core-web-vitals`) 进行代码格式化和质量检查。
  - 优先使用 `const` 声明变量，拥抱现代 JavaScript/TypeScript 语法。
  - 保持代码简洁，遵循 DRY (Don't Repeat Yourself) 原则。
- **注释**: 仅为复杂或非显而易见的逻辑添加简洁的中文注释，重点解释“为什么”这么做，而不是“做了什么”。
- **导出方式**: 优先使用命名导出 (`export function MyComponent`)，避免默认导出 (`export default`) 以保持一致性。

## 3. TypeScript 规范
- **严格模式**: 项目必须启用 `strict` 模式。
- **类型定义**: 为 `Props`、`State`、API 响应等定义明确的 `interface` (优先) 或 `type`。
- **禁止 `any`**: 严禁使用 `any` 类型，应使用 `unknown` 或更具体的类型进行替代。
- **类型保护**: 在需要时使用类型守卫（如 `typeof`, `instanceof`）来确保类型安全。

## 4. React & Next.js (App Router)
- **组件设计**: 设计小巧、单一职责的组件，优先采用组合而非继承。Props 定义应清晰、最小化。
- **自定义 Hooks**: 遵循 Hooks 的规则，将可复用的逻辑封装到自定义 Hooks 中（以 `use` 开头）。
- **路由与布局**:
  - 使用基于文件系统的路由 (`app/` 目录)。
  - 使用 `<Link>` 组件进行页面导航。
  - 使用 `layout.tsx` 定义共享 UI，`template.tsx` 处理需要保留状态的场景。
- **加载与错误处理**:
  - 实现 `loading.tsx` (结合 Suspense) 提供即时加载状态。
  - 实现 `error.tsx` 作为错误边界处理运行时错误。
  - 使用 `notFound()` 函数触发 404 页面。
- **元数据 (Metadata)**: 使用 `generateMetadata` API 以编程方式定义页面元数据。
- **内置优化**:
  - 使用 `<Image>` 组件优化图片（必须提供明确的 `width` 和 `height`）。
  - 使用 `next/font` 优化字体加载。
  - 使用 `next/dynamic` 按需加载组件。

## 5. 数据处理 (获取与变更)
- **服务端数据获取 (RSC)**: 在 Server Components 中，强制优先使用 `fetch()` API (结合 `async/await`)。这可以充分利用 Next.js 的自动缓存、请求去重等扩展功能。
- **客户端数据获取**: 在 Client Components 中，首选 `Axios` 进行数据获取（例如，动态数据、由客户端交互触发的请求、调用第三方 API）。应封装一个 `Axios` 实例，并配置拦截器（用于认证、错误处理等）。
- **数据变更 (Mutations)**:
  - **必须**使用 **Server Actions** 处理所有表单提交和 CUD (创建/更新/删除) 操作。
  - **严禁**在 Server Actions 内部使用 `Axios`。应直接在 Action 内部使用 `fetch` 或使用后端 API。
- **服务端验证**: **必须**在 Server Actions 中使用 `Zod` 对传入数据进行严格验证。
- **类型安全 Actions**: 确保 Server Actions 是类型安全的，可以考虑为 Action 的返回定义统一的响应结构。
- **客户端缓存 (可选)**: 对于复杂的服务器状态管理，推荐使用 TanStack Query (`@tanstack/react-query`)。

## 6. 状态管理
- **服务器状态**: 主要通过 RSC 数据获取、URL 参数和 Server Actions 进行管理。
- **客户端状态**:
  - **局部状态**: `useState` / `useReducer`。
  - **简单跨组件状态**: `useContext` (谨慎使用，避免性能问题)。
  - **全局/复杂状态**: `Zustand`。
- **派生状态**: 优先在渲染时直接计算。若遇到性能瓶颈，再考虑使用 `useMemo`。

## 7. 表单与验证
- **表单库**: 推荐使用 `React Hook Form`。
- **Schema 验证**: **必须**使用 `Zod` 定义验证 Schema，并在客户端和服务端复用。
- **集成**: 将 `React Hook Form` 与 Server Actions 无缝集成。
- **状态与反馈 (React 19+)**: 使用 `useActionState` Hook 来管理表单的提交状态（pending, error, success）和来自服务端的验证反馈。
- **用户反馈**: 提供清晰、即时的字段级验证错误提示。

## 8. 样式 (Tailwind CSS & Shadcn/ui)
- **UI 库**: 主要使用 `Shadcn/ui` 作为基础组件库，通过组合构建复杂 UI。
- **自定义**: 通过 `globals.css` 修改 Tailwind 配置和全局 CSS 变量。
- **Tailwind 使用**: 编写语义化、可维护的类名。谨慎使用 `@apply`。避免使用内联 `style` 属性（除非是动态计算的样式）。
- **CSS 变量**: 优先使用 CSS 变量进行主题化和动态样式控制。

## 9. 性能优化
- **最小化客户端 Bundle**: 严格控制 `'use client'` 的使用范围，确保其尽可能小且靠近叶子节点。
- **缓存策略**: 深入理解并有效利用 Next.js 的多层缓存机制（Fetch Cache, Full Route Cache, Data Cache），并使用 `revalidatePath` / `revalidateTag` 按需清除缓存。
- **Memoization**: 审慎使用 `React.memo`, `useCallback`, `useMemo`，仅在基于性能分析确认存在瓶颈时使用。
- **Suspense**: 有效利用 `<Suspense>` 改善流式渲染和数据加载的用户体验。

## 10. 可访问性 (A11y)
- **测试**: 使用 `Axe DevTools` 等工具进行自动化检测，并辅以手动测试（键盘导航、屏幕阅读器）。

## 11. 安全性
- **服务端验证**: 所有用户输入**必须**在 Server Actions 或 Route Handlers 中使用 `Zod` 进行验证。
- **XSS 防护**: 依赖 React 的自动转义机制。谨慎使用 `dangerouslySetInnerHTML`，若必须使用，则内容必须经过严格的净化处理。
- **认证/授权**: 所有认证和授权检查**必须**在服务器端（Server Components, Server Actions, Route Handlers）强制执行。
- **环境变量**: 不在客户端暴露任何敏感信息。只有以 `NEXT_PUBLIC_` 为前缀的环境变量才能被客户端代码访问。

## 12. 错误处理
- **预期错误 (Actions)**: 对于 Server Action 中可预期的业务逻辑错误（如“用户名已存在”），应作为 Action 的返回值返回，由 `useActionState` 处理，而不是抛出异常。
- **意外错误**: 使用 `error.tsx` 和 `global-error.tsx` 捕获运行时发生的意外错误。
- **函数健壮性**: 在函数内部使用卫语句或提前返回来处理无效输入。在 Service 层可以返回一个可辨识联合类型（如 `Result` 对象）来表示成功或失败，并实现统一的服务端日志记录。

## 13. 国际化 (i18n)
- **库**: 推荐使用 `next-intl`。
- **实现**: 国际化所有面向用户的文本内容，并妥善处理不同区域设置的日期、数字格式化等问题。