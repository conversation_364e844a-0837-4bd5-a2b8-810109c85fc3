'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import {
  Clock,
  Download,
  FileVideo,
  Music,
  Pause,
  Play,
  RotateCcw,
  Scissors,
  X,
} from 'lucide-react';
import { VideoPageData } from '@/lib/types';

interface VideoClipModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoData: VideoPageData;
  streamInfo?: {
    type: 'video' | 'audio';
    quality: string;
    format: string;
    fileSize: number;
  };
}

export function VideoClipModal({ isOpen, onClose, videoData, streamInfo }: VideoClipModalProps) {
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(60);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  // 模拟视频总时长（秒）
  const totalDuration = 300; // 5分钟

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartTimeChange = (value: string) => {
    const time = parseInt(value) || 0;
    if (time < endTime) {
      setStartTime(time);
    }
  };

  const handleEndTimeChange = (value: string) => {
    const time = parseInt(value) || 0;
    if (time > startTime && time <= totalDuration) {
      setEndTime(time);
    }
  };

  const handleSliderChange = (values: number[]) => {
    const [start, end] = values;
    setStartTime(start);
    setEndTime(end);
  };

  const clipDuration = endTime - startTime;
  const estimatedSize = streamInfo ? (streamInfo.fileSize * clipDuration) / totalDuration : 0;

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <DialogHeader className="p-6 pb-4 border-b">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <DialogTitle className="text-xl font-bold line-clamp-2 mb-2">
                  <Scissors className="inline h-5 w-5 mr-2 text-orange-500" />
                  视频剪辑
                </DialogTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="font-medium line-clamp-1">{videoData.title}</span>
                  {streamInfo && (
                    <Badge variant="secondary">
                      {streamInfo.type === 'video' ? (
                        <FileVideo className="h-3 w-3 mr-1" />
                      ) : (
                        <Music className="h-3 w-3 mr-1" />
                      )}
                      {streamInfo.quality} {streamInfo.format.toUpperCase()}
                    </Badge>
                  )}
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* 主要内容 */}
          <div className="flex-1 p-6 space-y-6">
            {/* 预览区域 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">预览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                  <div className="text-center text-white">
                    <Play className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg">视频预览</p>
                    <p className="text-sm opacity-75">点击播放按钮开始预览</p>
                  </div>
                </div>

                {/* 播放控制 */}
                <div className="flex items-center justify-center gap-4 mt-4">
                  <Button variant="outline" size="sm" onClick={() => setIsPlaying(!isPlaying)}>
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    {formatTime(currentTime)} / {formatTime(totalDuration)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* 剪辑设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">剪辑设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 时间轴滑块 */}
                <div className="space-y-4">
                  <Label>选择剪辑范围</Label>
                  <div className="px-3">
                    <Slider
                      value={[startTime, endTime]}
                      onValueChange={handleSliderChange}
                      max={totalDuration}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-2">
                      <span>0:00</span>
                      <span>{formatTime(totalDuration)}</span>
                    </div>
                  </div>
                </div>

                {/* 精确时间输入 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-time">开始时间 (秒)</Label>
                    <Input
                      id="start-time"
                      type="number"
                      min="0"
                      max={endTime - 1}
                      value={startTime}
                      onChange={(e) => handleStartTimeChange(e.target.value)}
                      placeholder="0"
                    />
                    <p className="text-xs text-muted-foreground">{formatTime(startTime)}</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-time">结束时间 (秒)</Label>
                    <Input
                      id="end-time"
                      type="number"
                      min={startTime + 1}
                      max={totalDuration}
                      value={endTime}
                      onChange={(e) => handleEndTimeChange(e.target.value)}
                      placeholder="60"
                    />
                    <p className="text-xs text-muted-foreground">{formatTime(endTime)}</p>
                  </div>
                </div>

                {/* 剪辑信息 */}
                <div className="bg-muted/30 rounded-lg p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-sm font-medium">剪辑时长</div>
                      <div className="text-lg font-bold text-primary">
                        {formatTime(clipDuration)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">预估大小</div>
                      <div className="text-lg font-bold text-green-600">
                        {formatFileSize(estimatedSize)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">处理时间</div>
                      <div className="text-lg font-bold text-orange-600">
                        <Clock className="h-4 w-4 inline mr-1" />~{Math.ceil(clipDuration / 30)}分钟
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 底部操作栏 */}
          <div className="p-6 border-t bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Scissors className="h-4 w-4" />
                  <span>智能剪辑，保持原始质量</span>
                </div>
              </div>

              <div className="flex gap-3">
                <Button variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setStartTime(0);
                    setEndTime(60);
                  }}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
                <Button className="bg-orange-500 hover:bg-orange-600">
                  <Download className="h-4 w-4 mr-2" />
                  开始剪辑下载
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
