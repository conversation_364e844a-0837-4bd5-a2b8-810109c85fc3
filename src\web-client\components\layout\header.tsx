'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { MainNav } from './main-nav';
import { UserNav } from './user-nav';
import { LanguageSwitcher } from '../shared/language-switcher';

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // 检测是否滚动
      setIsScrolled(currentScrollY > 10);

      // 检测滚动方向（隐藏/显示header）
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <AnimatePresence>
      <motion.header
        initial={{ y: 0 }}
        animate={{
          y: isVisible ? 0 : -100,
          backgroundColor: isScrolled ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.80)',
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`sticky top-0 z-50 w-full border-b backdrop-blur-md supports-[backdrop-filter]:bg-background/80 ${
          isScrolled ? 'shadow-sm border-border/40' : 'border-transparent'
        }`}>
        <div className="container mx-auto flex h-16 items-center justify-between px-4">
          {/* Logo区域 */}
          <div className="flex items-center gap-6">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Youtubear Logo"
                  width={32}
                  height={32}
                  className="rounded-lg shadow-sm"
                />
              </div>

              <div className="hidden sm:block">
                <div className="flex items-center gap-2">
                  <span className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                    Youtubear
                  </span>
                </div>
                <div className="text-xs text-muted-foreground -mt-0.5">专业下载工具</div>
              </div>
            </Link>
            {/* 导航区域 */}
            <MainNav />
          </div>

          {/* 右侧功能区域 */}
          <div className="flex items-center justify-end space-x-6">
            <UserNav />
            <LanguageSwitcher />
          </div>
        </div>

        {/* 进度条 */}
        <motion.div
          className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-primary to-purple-600"
          style={{
            width:
              typeof window !== 'undefined'
                ? `${Math.min(((window.scrollY || 0) / (document.documentElement.scrollHeight - window.innerHeight || 1)) * 100, 100)}%`
                : '0%',
          }}
          initial={{ width: 0 }}
          animate={{
            width:
              typeof window !== 'undefined'
                ? `${Math.min(((window.scrollY || 0) / (document.documentElement.scrollHeight - window.innerHeight || 1)) * 100, 100)}%`
                : '0%',
          }}
          transition={{ duration: 0.1 }}
        />
      </motion.header>
    </AnimatePresence>
  );
}
