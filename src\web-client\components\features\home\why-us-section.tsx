import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Award,
  CheckCircle,
  MousePointer,
  Rocket,
  Shield,
  Sparkles,
  Users,
  Zap,
} from 'lucide-react';

const whyUsData = [
  {
    icon: <Zap className="h-6 w-6" />,
    title: '功能全面且强大',
    description: '从高清视频到SRT字幕，从单个视频到整个频道，我们满足您的一切下载需求。',
    color: 'from-blue-500 to-cyan-500',
    stats: '12+ 种格式',
    highlights: ['8K 超高清', '批量处理', '多语言字幕', '在线剪辑'],
  },
  {
    icon: <MousePointer className="h-6 w-6" />,
    title: '操作极致简化',
    description: '我们相信工具应该为人服务。粘贴链接，选择格式，点击下载，就这么简单。',
    color: 'from-purple-500 to-pink-500',
    stats: '3 步完成',
    highlights: ['一键操作', '智能识别', '自动配置', '即时下载'],
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: '安全、可靠、尊重隐私',
    description: '我们不存储您的任何个人信息，所有处理都在云端安全进行，保障您的数据安全。',
    color: 'from-green-500 to-emerald-500',
    stats: '100% 安全',
    highlights: ['无需注册', '隐私保护', '安全加密', '自动清理'],
  },
  {
    icon: <Rocket className="h-6 w-6" />,
    title: '持续创新，引领未来',
    description:
      '我们不断投入研发，紧跟技术前沿，为用户提供最新最好的功能体验，始终保持行业领先地位。',
    color: 'from-orange-500 to-red-500',
    stats: '每月更新',
    highlights: ['AI智能', '新格式支持', '性能优化', '用户反馈'],
  },
];

export function WhyUsSection() {
  return (
    <section id="why-us" className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold md:text-4xl mb-4">为何选择我们？</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          我们致力于提供最优质的 YouTube 内容下载体验，让每个用户都能轻松获取所需内容
        </p>
      </div>

      <div className="mx-auto max-w-6xl space-y-8">
        {whyUsData.map((item, index) => (
          <div key={item.title} className="transform transition-all duration-300 hover:scale-105">
            <Card className="group border-0 bg-gradient-to-r from-background to-muted/20 shadow-lg hover:shadow-xl transition-all duration-500">
              <CardContent className="p-8">
                <div className="flex flex-col lg:flex-row items-start gap-8">
                  {/* 左侧图标和标题 */}
                  <div className="flex-shrink-0">
                    <div className="flex items-center gap-4 mb-4">
                      <div
                        className={`p-4 rounded-2xl bg-gradient-to-br ${item.color} text-white shadow-lg transition-transform duration-200 group-hover:scale-105 group-hover:rotate-6`}>
                        {item.icon}
                      </div>
                      <div>
                        <Badge variant="secondary" className="mb-2">
                          {item.stats}
                        </Badge>
                        <h3 className="text-xl font-bold">{item.title}</h3>
                      </div>
                    </div>
                  </div>

                  {/* 右侧内容 */}
                  <div className="flex-grow">
                    <p className="text-muted-foreground mb-6 leading-relaxed lg:text-lg">
                      {item.description}
                    </p>

                    {/* 亮点功能 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {item.highlights.map((highlight, idx) => (
                        <div
                          key={idx}
                          className="flex items-center gap-2 p-3 bg-background/50 rounded-lg border transition-transform duration-200 hover:scale-105">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm font-medium">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>

      {/* 底部统计和信任标识 */}
      <div className="mt-16 text-center">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="flex flex-col items-center gap-2">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <span className="text-2xl font-bold text-primary">1M+</span>
            </div>
            <span className="text-sm text-muted-foreground">全球用户信赖</span>
          </div>
          <div className="flex flex-col items-center gap-2">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-primary" />
              <span className="text-2xl font-bold text-primary">99.9%</span>
            </div>
            <span className="text-sm text-muted-foreground">服务可用性</span>
          </div>
          <div className="flex flex-col items-center gap-2">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <span className="text-2xl font-bold text-primary">24/7</span>
            </div>
            <span className="text-sm text-muted-foreground">全天候服务</span>
          </div>
        </div>
      </div>
    </section>
  );
}
