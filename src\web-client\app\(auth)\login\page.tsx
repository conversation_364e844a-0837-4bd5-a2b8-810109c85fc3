import { Metadata } from 'next';
import { LoginForm } from '@/components/features/auth/login-form';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '登录 - YTDownloader',
  description: '登录您的 YTDownloader 账户',
};

export default function LoginPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">欢迎回来</CardTitle>
                <p className="text-muted-foreground">登录您的账户以继续使用 YTDownloader</p>
              </CardHeader>
              <CardContent>
                <LoginForm />
                <div className="mt-6 pt-6 border-t text-center text-sm text-muted-foreground">
                  <p>还没有账户？</p>
                  <Link
                    href="/register"
                    className="font-medium text-primary hover:text-primary/80 transition-colors">
                    立即注册
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
