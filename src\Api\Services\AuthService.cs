using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Api.Data.Models;
using Microsoft.IdentityModel.Tokens;

namespace Api.Services;

public class AuthService(UserService userService, IConfiguration configuration)
{
    // 密码哈希常量
    private const int SaltSize = 16;
    private const int HashSize = 32;
    private const int Iterations = 100000;

    #region 密码管理

    public string HashPassword(string password)
    {
        using var rng = RandomNumberGenerator.Create();
        var salt = new byte[SaltSize];
        rng.GetBytes(salt);

        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
        var hash = pbkdf2.GetBytes(HashSize);

        var combined = new byte[SaltSize + HashSize];
        Array.Copy(salt, 0, combined, 0, SaltSize);
        Array.Copy(hash, 0, combined, SaltSize, HashSize);

        return Convert.ToBase64String(combined);
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        try
        {
            var combined = Convert.FromBase64String(hashedPassword);

            if (combined.Length != SaltSize + HashSize)
                return false;

            var salt = new byte[SaltSize];
            var hash = new byte[HashSize];

            Array.Copy(combined, 0, salt, 0, SaltSize);
            Array.Copy(combined, SaltSize, hash, 0, HashSize);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
            var computedHash = pbkdf2.GetBytes(HashSize);

            return CryptographicOperations.FixedTimeEquals(hash, computedHash);
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region JWT管理

    /// <summary>
    ///     为用户生成JWT token（支持注册用户和匿名用户）
    /// </summary>
    public string GenerateToken(User user, bool isRefreshToken = false)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]!));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new("userType", user.Type.ToString()),
            new("subscriptionTier", user.SubscriptionTier.ToString()),
            new("tokenType", isRefreshToken ? "refresh" : "access")
        };

        // 根据用户类型添加不同的claims
        if (user.Type == UserType.Registered)
        {
            claims.Add(new Claim(JwtRegisteredClaimNames.Email, user.Email ?? ""));
            claims.Add(new Claim("emailVerified", user.EmailVerified.ToString()));
            if (!string.IsNullOrEmpty(user.Username)) claims.Add(new Claim("username", user.Username));
        }
        else if (user.Type == UserType.Anonymous)
        {
            claims.Add(new Claim("anonymousId", user.AnonymousId ?? ""));
        }

        // 设置token过期时间
        DateTime expiry;
        if (isRefreshToken)
            // Refresh token有更长的过期时间
            expiry = user.Type == UserType.Anonymous
                ? DateTime.UtcNow.AddDays(365) // 匿名用户refresh token: 1年
                : DateTime.UtcNow.AddDays(90); // 注册用户refresh token: 90天
        else
            // Access token有较短的过期时间
            expiry = user.Type == UserType.Anonymous
                ? DateTime.UtcNow.AddDays(7) // 匿名用户access token: 7天
                : DateTime.UtcNow.AddHours(2); // 注册用户access token: 2小时

        var token = new JwtSecurityToken(_configuration["Jwt:Issuer"], _configuration["Jwt:Audience"], claims, expires: expiry,
            signingCredentials: credentials);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    ///     生成token对（access token + refresh token）
    /// </summary>
    public TokenPair GenerateTokenPair(User user)
    {
        var accessToken = GenerateToken(user);
        var refreshToken = GenerateToken(user, true);

        return new TokenPair
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresAt = user.Type == UserType.Anonymous ? DateTime.UtcNow.AddDays(7) : DateTime.UtcNow.AddHours(2)
        };
    }

    public ClaimsPrincipal? ValidateToken(string token)
    {
        try
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]!));
            var tokenHandler = new JwtSecurityTokenHandler();

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = _configuration["Jwt:Issuer"],
                ValidAudience = _configuration["Jwt:Audience"],
                IssuerSigningKey = key,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
            return principal;
        }
        catch
        {
            return null;
        }
    }

    public Guid? GetUserIdFromToken(string token)
    {
        var principal = ValidateToken(token);
        var userIdClaim = principal?.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

        if (Guid.TryParse(userIdClaim, out var userId)) return userId;

        return null;
    }

    /// <summary>
    ///     验证refresh token并生成新的token对
    /// </summary>
    public async Task<TokenPair?> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            var principal = ValidateToken(refreshToken);
            if (principal == null) return null;

            // 验证是否为refresh token
            var tokenTypeClaim = principal.FindFirst("tokenType")?.Value;
            if (tokenTypeClaim != "refresh") return null;

            // 获取用户ID和类型
            var userIdClaim = principal.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;
            var userTypeClaim = principal.FindFirst("userType")?.Value;

            if (!Guid.TryParse(userIdClaim, out var userId) || !Enum.TryParse<UserType>(userTypeClaim, out var userType))
                return null;

            // 根据用户类型获取用户信息
            User? user = null;
            if (userType == UserType.Registered)
            {
                user = await _userService.GetRegisteredUserByIdAsync(userId);
            }
            else if (userType == UserType.Anonymous)
            {
                var anonymousIdClaim = principal.FindFirst("anonymousId")?.Value;
                if (!string.IsNullOrEmpty(anonymousIdClaim)) user = await _userService.GetAnonymousUserAsync(anonymousIdClaim);
            }

            if (user == null) return null;

            // 生成新的token对
            return GenerateTokenPair(user);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Token refresh failed: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    ///     检查token是否即将过期（在过期前30分钟）
    /// </summary>
    public bool IsTokenNearExpiry(string token, int minutesBeforeExpiry = 30)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var expiryTime = jsonToken.ValidTo;
            var warningTime = DateTime.UtcNow.AddMinutes(minutesBeforeExpiry);

            return expiryTime <= warningTime;
        }
        catch
        {
            return true; // 如果无法解析token，认为需要刷新
        }
    }

    #endregion

    #region 匿名用户管理

    /// <summary>
    ///     创建匿名用户并返回JWT token
    /// </summary>
    public async Task<AnonymousUserResult> CreateAnonymousUserAsync()
    {
        var anonymousId = GenerateAnonymousId();
        var anonymousUser = await _userService.CreateAnonymousUserAsync(anonymousId);
        var token = GenerateToken(anonymousUser);

        return new AnonymousUserResult { AnonymousId = anonymousId, Token = token, User = anonymousUser };
    }

    public async Task<User?> GetAnonymousUserAsync(string anonymousId)
    {
        return await _userService.GetAnonymousUserAsync(anonymousId);
    }

    public async Task UpdateLastActiveAsync(string anonymousId)
    {
        await _userService.UpdateAnonymousUserActivityAsync(anonymousId);
    }

    public async Task<(bool Success, int TasksCount, int BatchJobsCount)> MigrateAnonymousDataAsync(string anonymousId, Guid registeredUserId)
    {
        return await _userService.MigrateAnonymousDataAsync(anonymousId, registeredUserId);
    }

    public async Task<int> CleanupExpiredAnonymousUsersAsync()
    {
        return await _userService.CleanupExpiredAnonymousUsersAsync();
    }

    private static string GenerateAnonymousId()
    {
        return $"anon_{Guid.NewGuid():N}";
    }

    #endregion

    #region 用户认证

    public async Task<User?> ValidateUserCredentialsAsync(string email, string password)
    {
        var user = await _userService.GetRegisteredUserByEmailAsync(email);

        if (user == null || string.IsNullOrEmpty(user.PasswordHash) || !VerifyPassword(password, user.PasswordHash))
            return null;

        // 更新最后登录时间
        await _userService.UpdateLastLoginAsync(user.Id);

        return user;
    }

    public async Task<User> CreateRegisteredUserAsync(string email, string password, string? username = null)
    {
        var passwordHash = HashPassword(password);
        return await _userService.CreateRegisteredUserAsync(email, passwordHash, username);
    }

    public async Task<bool> IsEmailExistsAsync(string email)
    {
        return await _userService.IsEmailExistsAsync(email);
    }

    public async Task<User?> GetUserByIdAsync(Guid userId)
    {
        return await _userService.GetRegisteredUserByIdAsync(userId);
    }

    #endregion
}

/// <summary>
///     Token对结果类
/// </summary>
public class TokenPair
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
///     匿名用户创建结果
/// </summary>
public class AnonymousUserResult
{
    public string AnonymousId { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public User User { get; set; } = null!;
}