import Link from 'next/link';
import Image from 'next/image';

import { Badge } from '@/components/ui/badge';
import { ExternalLink, Globe, Mail, Shield, Zap } from 'lucide-react';

const footerLinks = {
  product: {
    title: '产品功能',
    links: [
      { href: '/youtube-to-mp4-converter', label: '视频转换器' },
      { href: '/youtube-to-mp3-converter', label: '音频提取器' },
      { href: '/youtube-playlist-downloader', label: '播放列表下载' },
      { href: '/youtube-subtitle-downloader', label: '字幕下载' },
    ],
  },
  tools: {
    title: '创作工具',
    links: [
      { href: '/youtube-gif-maker', label: 'GIF 制作器' },
      { href: '/youtube-clip-maker', label: '视频剪辑' },
      { href: '/youtube-ringtone-maker', label: '铃声制作' },
      { href: '/youtube-thumbnail-downloader', label: '缩略图下载' },
    ],
  },
  support: {
    title: '更多工具',
    links: [
      { href: '/youtube-channel-downloader', label: '频道下载' },
      { href: '/youtube-comment-downloader', label: '评论下载' },
      { href: '/youtube-shorts-downloader', label: 'Shorts下载' },
      { href: '/youtube-description-downloader', label: '描述下载' },
    ],
  },
  legal: {
    title: '法律信息',
    links: [
      { href: '/terms', label: '服务条款' },
      { href: '/privacy', label: '隐私政策' },
      { href: '/disclaimer', label: '免责声明' },
      { href: '/dmca', label: '版权政策' },
    ],
  },
};

const socialLinks = [{ href: 'mailto:<EMAIL>', icon: Mail, label: 'Email' }];

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-muted/30 via-background to-muted/30 border-t">
      <div className="container mx-auto px-4">
        {/* 主要内容区域 */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* 品牌信息 */}
            <div className="lg:col-span-2">
              <div>
                <Link href="/" className="flex items-center space-x-3 mb-4">
                  <Image
                    src="/logo.png"
                    alt="Youtubear Logo"
                    width={40}
                    height={40}
                    className="rounded-lg shadow-sm"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                        Youtubear
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">专业下载工具</div>
                  </div>
                </Link>

                <p className="text-sm text-muted-foreground mb-6 leading-relaxed">
                  为全球用户提供最专业的 YouTube 内容下载服务。
                  安全、快速、免费，让您轻松获取所需内容。
                </p>

                {/* 特色标识 */}
                <div className="flex flex-wrap gap-2 mb-6">
                  <Badge variant="outline" className="text-xs">
                    <Shield className="w-3 h-3 mr-1" />
                    100% 安全
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    <Zap className="w-3 h-3 mr-1" />
                    极速处理
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    <Globe className="w-3 h-3 mr-1" />
                    全球服务
                  </Badge>
                </div>

                {/* 社交媒体 */}
                <div className="flex items-center gap-3">
                  {socialLinks.map((social) => (
                    <a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 bg-muted hover:bg-primary hover:text-primary-foreground rounded-lg transition-all duration-200 hover:scale-110 hover:-translate-y-0.5"
                      aria-label={social.label}>
                      <social.icon className="w-4 h-4" />
                    </a>
                  ))}
                </div>
              </div>
            </div>

            {/* 链接列表 */}
            {Object.entries(footerLinks).map(([key, section], index) => (
              <div key={key}>
                <div>
                  <h3 className="font-semibold mb-4 text-sm uppercase tracking-wider">
                    {section.title}
                  </h3>
                  <ul className="space-y-3">
                    {section.links.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center gap-1 group">
                          {link.label}
                          <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部版权区域 */}
        <div className="border-t py-8">
          <div>
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>&copy; {new Date().getFullYear()} Youtubear. All rights reserved.</span>
              </div>
            </div>
          </div>
        </div>

        {/* 免责声明 */}
        <div className="border-t py-6">
          <div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground leading-relaxed max-w-4xl mx-auto">
                <strong>免责声明：</strong>
                本服务仅供个人学习和研究使用，请勿用于商业用途。
                用户需自行承担因使用本服务而产生的版权风险。 我们尊重版权，如有侵权请联系我们删除。
                使用本服务即表示您同意我们的服务条款和隐私政策。
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
