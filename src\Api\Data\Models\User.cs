using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public enum UserType
{
    Anonymous,
    Registered
}

public enum SubscriptionTier
{
    Free,
    Pro
}

public class User
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public UserType Type { get; set; } = UserType.Anonymous;
    public string? Email { get; set; }
    public bool EmailVerified { get; set; }
    public DateTime? EmailVerifiedAt { get; set; }
    public string? EmailVerificationToken { get; set; }
    public DateTime? EmailVerificationTokenExpiresAt { get; set; }
    public string? PasswordHash { get; set; }
    public string? PasswordResetToken { get; set; }
    public DateTime? PasswordResetTokenExpiresAt { get; set; }
    public SubscriptionTier SubscriptionTier { get; set; } = SubscriptionTier.Free;
    public DateTime? SubscriptionExpiresAt { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public DateTime LastActiveAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public string? LastLoginIp { get; set; }
    public int LoginFailureCount { get; set; }
    public DateTime? AccountLockedUntil { get; set; }
    public bool IsActive { get; set; } = true;

    public ICollection<WorkTask> WorkTasks { get; set; } = [];
    public ICollection<BatchJob> BatchJobs { get; set; } = [];
    public bool IsAnonymous => Type == UserType.Anonymous;
    public bool IsRegistered => Type == UserType.Registered;
}

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        // 主键配置
        builder.HasKey(u => u.Id);
        // 基本属性配置
        builder.Property(u => u.Id).ValueGeneratedNever();
        builder.Property(u => u.Type).IsRequired().HasConversion<string>();
        builder.Property(u => u.Email).HasMaxLength(255);
        builder.Property(u => u.EmailVerified).IsRequired().HasDefaultValue(false);
        builder.Property(u => u.EmailVerificationToken).HasMaxLength(255);
        builder.Property(u => u.PasswordHash).HasMaxLength(255);
        builder.Property(u => u.PasswordResetToken).HasMaxLength(255);
        builder.Property(u => u.SubscriptionTier).IsRequired().HasConversion<string>().HasDefaultValue("Free");
        builder.Property(u => u.CreatedAt).IsRequired().ValueGeneratedOnAdd();
        builder.Property(u => u.UpdatedAt).IsRequired().ValueGeneratedOnAddOrUpdate();
        builder.Property(u => u.LastActiveAt).IsRequired().HasDefaultValueSql("NOW()");
        builder.Property(u => u.LastLoginIp).HasMaxLength(45);
        builder.Property(u => u.LoginFailureCount).IsRequired().HasDefaultValue(0);
        builder.Property(u => u.IsActive).HasDefaultValue(true);
        // 索引配置
        builder.HasIndex(u => u.Email).IsUnique().HasFilter("(\"type\" = 'Registered')");
        builder.HasIndex(u => u.Type);
        builder.HasIndex(u => u.LastActiveAt);
        // 约束配置
        builder.ToTable(t => t.HasCheckConstraint("CK_User_SubscriptionTier", "subscription_tier IN ('Free', 'Pro')"));
        builder.ToTable(t => t.HasCheckConstraint("CK_User_AnonymousFields",
            "(type = 'Anonymous') OR (type = 'Registered' AND email IS NOT NULL AND password_hash IS NOT NULL)"));
    }
}