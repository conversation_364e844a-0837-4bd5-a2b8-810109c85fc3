'use client';

import { useMemo } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertCircle, Check, Link2, Loader2, Plus, RotateCcw, Search } from 'lucide-react';
import { analyzeUrl, ExtractedEntity } from '@/lib/youtube-utils';
import { cn } from '@/lib/utils';

const INVALID_ENTITY: ExtractedEntity = { type: 'INVALID' };

interface SingleLinkInputProps {
  inputValue: string;
  isProcessing: boolean;
  errorMessage: string;
  placeholder?: string;
  onInputChange: (value: string) => void;
  onSubmit: () => void;
  onSwitchToBatch: () => void;
  onClearInput: () => void;
}

export function SingleLinkInput({
  inputValue,
  isProcessing,
  errorMessage,
  placeholder = '粘贴 YouTube 视频、播放列表或频道链接...',
  onInputChange,
  onSubmit,
  onSwitchToBatch,
  onClearInput,
}: SingleLinkInputProps) {
  const linkAnalysis = useMemo(() => {
    if (!inputValue) return INVALID_ENTITY;
    return analyzeUrl(inputValue);
  }, [inputValue]);

  const isValid = linkAnalysis.type !== 'INVALID';

  const getInputBorderColor = () => {
    if (isProcessing) return 'border-blue-500';
    if (!inputValue) return 'border-border';
    return isValid ? 'border-green-500' : 'border-red-500';
  };

  const getLinkTypeDisplay = () => {
    if (!inputValue) return null;

    switch (linkAnalysis.type) {
      case 'VIDEO':
        return (
          <div className="flex items-center gap-1">
            <Check className="h-4 w-4 text-green-600" />
            <span>检测到视频链接</span>
          </div>
        );
      case 'SHORTS':
        return (
          <div className="flex items-center gap-1">
            <Check className="h-4 w-4 text-green-600" />
            <span>检测到 Shorts 链接</span>
          </div>
        );
      case 'PLAYLIST':
        return (
          <div className="flex items-center gap-1">
            <Check className="h-4 w-4 text-green-600" />
            <span>检测到播放列表链接</span>
          </div>
        );
      case 'CHANNEL':
        return (
          <div className="flex items-center gap-1">
            <Check className="h-4 w-4 text-green-600" />
            <span>检测到频道链接</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-1">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span>无效的链接格式</span>
          </div>
        );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onInputChange(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && isValid && !isProcessing) {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <div className="w-full">
      {/* 输入框容器 */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}>
        {/* 左侧链接图标 */}
        <div className="absolute left-4 top-1/2 -translate-y-1/2 z-10">
          <Link2 className="h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors duration-200" />
        </div>

        <Input
          placeholder={placeholder}
          className={cn(
            'h-16 pl-12 pr-48 text-lg font-medium transition-all duration-300',
            'border-2 rounded-xl shadow-sm',
            'focus:shadow-lg focus:scale-[1.01]',
            'bg-gradient-to-r from-background to-muted/20',
            getInputBorderColor(),
            isProcessing && 'animate-pulse',
          )}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={isProcessing}
          autoComplete="url"
          name="youtube-url"
          aria-label="YouTube链接输入框"
          aria-describedby="single-input-help"
          role="textbox"
        />

        {/* 右侧按钮组 */}
        <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
          {/* 清空按钮 */}
          <AnimatePresence>
            {inputValue && !isProcessing && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={onClearInput}
                  className="h-8 w-8 hover:bg-red-100 hover:text-red-600 transition-colors duration-200">
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 批量添加按钮 */}
          <Button
            size="sm"
            variant="outline"
            onClick={onSwitchToBatch}
            disabled={isProcessing}
            className="h-10 px-3 hover:bg-primary hover:text-primary-foreground transition-colors duration-200">
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">批量</span>
          </Button>

          {/* 搜索按钮 */}
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              size="sm"
              disabled={!isValid || isProcessing}
              onClick={onSubmit}
              className={cn(
                'h-12 px-6 bg-gradient-to-r from-purple-600 to-purple-700',
                'hover:from-purple-700 hover:to-purple-800',
                'shadow-lg hover:shadow-xl transition-all duration-200',
                'font-semibold text-white',
                isProcessing && 'animate-pulse',
              )}>
              <motion.div
                className="flex items-center gap-1"
                animate={isProcessing ? { scale: [1, 1.05, 1] } : {}}
                transition={{ duration: 1, repeat: isProcessing ? Infinity : 0 }}>
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="hidden sm:inline">处理中</span>
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4" />
                    <span className="hidden sm:inline">搜索</span>
                  </>
                )}
              </motion.div>
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* 状态信息 - 保留空间并居中 */}
      <div className="mt-4 h-8 flex items-center justify-center">
        <div id="single-input-help">
          {(inputValue || errorMessage) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3, delay: 0.1 }}>
              {getLinkTypeDisplay() && (
                <div
                  className={cn(
                    'text-sm font-medium text-center',
                    isValid ? 'text-green-600' : 'text-red-600',
                  )}>
                  {getLinkTypeDisplay()}
                </div>
              )}
              {errorMessage && (
                <motion.p
                  className="text-sm text-red-600 mt-1 text-center"
                  role="alert"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2 }}>
                  {errorMessage}
                </motion.p>
              )}
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
