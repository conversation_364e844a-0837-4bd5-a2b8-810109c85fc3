'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Image as ImageIcon,
  Loader2,
  Minus,
  Plus,
  Zap,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { VideoPageData } from '@/lib/types';
import { formatDuration } from '@/lib/youtube-utils';

interface GifMakerPanelProps {
  videoData: VideoPageData;
  onGifGenerate: (startTime: number, endTime: number, fps: number, width: number) => void;
}

interface TimeInput {
  hours: number;
  minutes: number;
  seconds: number;
}

interface GifSettings {
  fps: number;
  width: number;
  quality: 'high' | 'medium' | 'low';
}

export function GifMakerPanel({ videoData, onGifGenerate }: GifMakerPanelProps) {
  const [startTime, setStartTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 0 });
  const [endTime, setEndTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 5 });
  const [gifSettings, setGifSettings] = useState<GifSettings>({
    fps: 10,
    width: 400,
    quality: 'medium',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedGif, setGeneratedGif] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [estimatedSize, setEstimatedSize] = useState<number>(0);

  // 将时间对象转换为秒数
  const timeToSeconds = (time: TimeInput): number => {
    return time.hours * 3600 + time.minutes * 60 + time.seconds;
  };

  // 将秒数转换为时间对象
  const secondsToTime = (seconds: number): TimeInput => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return { hours, minutes, seconds: secs };
  };

  // 验证时间范围
  const validateTimeRange = () => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    const valid =
      startSeconds >= 0 &&
      endSeconds > startSeconds &&
      endSeconds <= videoData.duration &&
      duration >= 1 &&
      duration <= 30;

    setIsValid(valid);

    if (valid) {
      // 估算GIF文件大小
      const frames = duration * gifSettings.fps;
      const pixelCount = gifSettings.width * ((gifSettings.width * 9) / 16); // 假设16:9比例
      const bytesPerPixel =
        gifSettings.quality === 'high' ? 3 : gifSettings.quality === 'medium' ? 2 : 1;
      const sizeInMB = (frames * pixelCount * bytesPerPixel) / (1024 * 1024);
      setEstimatedSize(Math.round(sizeInMB * 100) / 100);
    } else {
      setEstimatedSize(0);
    }
  };

  // 时间输入变化处理
  const handleTimeChange = (
    type: 'start' | 'end',
    field: 'hours' | 'minutes' | 'seconds',
    value: number,
  ) => {
    const newTime = { ...(type === 'start' ? startTime : endTime) };
    newTime[field] = Math.max(0, value);

    // 限制范围
    if (field === 'hours') newTime[field] = Math.min(23, newTime[field]);
    if (field === 'minutes' || field === 'seconds') newTime[field] = Math.min(59, newTime[field]);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 微调时间
  const adjustTime = (type: 'start' | 'end', delta: number) => {
    const currentSeconds = timeToSeconds(type === 'start' ? startTime : endTime);
    const newSeconds = Math.max(0, Math.min(videoData.duration, currentSeconds + delta));
    const newTime = secondsToTime(newSeconds);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 格式化时间显示
  const formatTimeInput = (time: TimeInput): string => {
    return `${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
  };

  // 解析时间输入
  const parseTimeInput = (timeStr: string): TimeInput => {
    const parts = timeStr.split(':').map(Number);
    return {
      hours: parts[0] || 0,
      minutes: parts[1] || 0,
      seconds: parts[2] || 0,
    };
  };

  // 应用质量预设
  const applyQualityPreset = (quality: 'high' | 'medium' | 'low') => {
    const presets = {
      high: { fps: 20, width: 800 },
      medium: { fps: 15, width: 600 },
      low: { fps: 10, width: 400 },
    };

    const preset = presets[quality];
    setGifSettings((prev) => ({
      ...prev,
      quality,
      fps: preset.fps,
      width: preset.width,
    }));
  };

  // 生成GIF
  const handleGenerateGif = async () => {
    if (!isValid) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedGif(null);

    try {
      // 模拟生成过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 200));
        setGenerationProgress(i);
      }

      // 模拟生成的GIF URL
      const mockGifUrl = `data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7`;
      setGeneratedGif(mockGifUrl);

      // 调用回调
      onGifGenerate(
        timeToSeconds(startTime),
        timeToSeconds(endTime),
        gifSettings.fps,
        gifSettings.width,
      );
    } catch (error) {
      console.error('GIF生成失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    validateTimeRange();
  }, [startTime, endTime, gifSettings]);

  // 获取验证错误信息
  const getValidationError = (): string | null => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    if (startSeconds < 0) return '开始时间不能为负数';
    if (endSeconds <= startSeconds) return '结束时间必须大于开始时间';
    if (endSeconds > videoData.duration) return '结束时间不能超过视频总时长';
    if (duration < 1) return 'GIF时长至少为1秒';
    if (duration > 30) return 'GIF时长不能超过30秒';

    return null;
  };

  const validationError = getValidationError();
  const duration = timeToSeconds(endTime) - timeToSeconds(startTime);

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <ImageIcon className="h-5 w-5 text-primary" />
          GIF制作
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 时间范围设置 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 开始时间 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">开始时间</Label>
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={formatTimeInput(startTime)}
                onChange={(e) => setStartTime(parseTimeInput(e.target.value))}
                placeholder="00:00:00"
                className="font-mono text-center"
              />
              <div className="flex flex-col gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('start', 1)}>
                  <Plus className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('start', -1)}>
                  <Minus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          {/* 结束时间 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">结束时间</Label>
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={formatTimeInput(endTime)}
                onChange={(e) => setEndTime(parseTimeInput(e.target.value))}
                placeholder="00:00:05"
                className="font-mono text-center"
              />
              <div className="flex flex-col gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('end', 1)}>
                  <Plus className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('end', -1)}>
                  <Minus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* GIF设置 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">GIF设置</Label>

          {/* 质量预设 */}
          <div className="grid grid-cols-3 gap-2">
            {(['high', 'medium', 'low'] as const).map((quality) => (
              <Button
                key={quality}
                size="sm"
                variant={gifSettings.quality === quality ? 'default' : 'outline'}
                onClick={() => applyQualityPreset(quality)}
                className="flex flex-col gap-1 h-auto py-2">
                <span className="font-medium">
                  {quality === 'high' && '高质量'}
                  {quality === 'medium' && '中等质量'}
                  {quality === 'low' && '低质量'}
                </span>
                <span className="text-xs opacity-75">
                  {quality === 'high' && '20fps, 800px'}
                  {quality === 'medium' && '15fps, 600px'}
                  {quality === 'low' && '10fps, 400px'}
                </span>
              </Button>
            ))}
          </div>

          {/* 详细设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm">帧率 (FPS)</Label>
              <Select
                value={gifSettings.fps.toString()}
                onValueChange={(value) =>
                  setGifSettings((prev) => ({ ...prev, fps: parseInt(value) }))
                }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 fps (最小)</SelectItem>
                  <SelectItem value="10">10 fps (标准)</SelectItem>
                  <SelectItem value="15">15 fps (流畅)</SelectItem>
                  <SelectItem value="20">20 fps (最高)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">宽度 (像素)</Label>
              <Select
                value={gifSettings.width.toString()}
                onValueChange={(value) =>
                  setGifSettings((prev) => ({ ...prev, width: parseInt(value) }))
                }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="200">200px (小)</SelectItem>
                  <SelectItem value="400">400px (中)</SelectItem>
                  <SelectItem value="600">600px (大)</SelectItem>
                  <SelectItem value="800">800px (超大)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 时间和大小信息 */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>时长: {formatDuration(Math.max(0, duration))}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>限制: 1-30秒</span>
            </div>
          </div>
          {isValid && (
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              预估大小: {estimatedSize} MB
            </Badge>
          )}
        </div>

        {/* 验证错误提示 */}
        {validationError && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
            <span className="text-sm text-red-700">{validationError}</span>
          </div>
        )}

        {/* 生成按钮和进度 */}
        <div className="space-y-3">
          <Button
            onClick={handleGenerateGif}
            disabled={!isValid || isGenerating}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            size="lg">
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                生成GIF
              </>
            )}
          </Button>

          {/* 生成进度 */}
          {isGenerating && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>生成进度</span>
                <span>{generationProgress}%</span>
              </div>
              <Progress value={generationProgress} className="w-full" />
            </div>
          )}
        </div>

        {/* 生成结果 */}
        {generatedGif && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3">
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700">GIF生成完成！</span>
            </div>

            {/* GIF预览 */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="text-center space-y-3">
                <div className="w-32 h-18 bg-gray-200 rounded mx-auto flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>
                <div className="text-sm text-gray-600">
                  GIF预览 ({gifSettings.width}×{Math.round((gifSettings.width * 9) / 16)}px,{' '}
                  {gifSettings.fps}fps)
                </div>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Download className="h-4 w-4 mr-2" />
                  下载GIF
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
