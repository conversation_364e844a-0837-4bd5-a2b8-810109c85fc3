'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { TaskHistoryItem } from '@/lib/types';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Download, Filter, Loader2, RefreshCw, Search } from 'lucide-react';
import { getUserTaskHistory } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { TaskHistoryCard } from './task-history-card';

export function MyDownloadsContent() {
  const [tasks, setTasks] = useState<TaskHistoryItem[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<TaskHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTasks, setTotalTasks] = useState(0);

  const { user, isLoading: authLoading } = useAuth();
  const { toast } = useToast();

  const fetchTasks = async (page: number = 1) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const response = await getUserTaskHistory(page, 10);
      if (response) {
        setTasks(response.items);
        setTotalPages(Math.ceil(response.total / response.limit));
        setTotalTasks(response.total);
        setCurrentPage(page);
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载下载历史，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTasks();
    }
  }, [user]);

  useEffect(() => {
    let filtered = [...tasks];

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (task) =>
          task.title.toLowerCase().includes(query) || task.sourceUrl?.toLowerCase().includes(query),
      );
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter((task) => task.status === statusFilter);
    }

    // 类型过滤
    if (typeFilter !== 'all') {
      filtered = filtered.filter((task) => task.type === typeFilter);
    }

    setFilteredTasks(filtered);
  }, [tasks, searchQuery, statusFilter, typeFilter]);

  const handleRefresh = () => {
    fetchTasks(currentPage);
  };

  const getStatusStats = () => {
    const stats = {
      total: tasks.length,
      completed: tasks.filter((t) => t.status === 'completed').length,
      running: tasks.filter((t) => t.status === 'running').length,
      failed: tasks.filter((t) => t.status === 'failed').length,
      pending: tasks.filter((t) => t.status === 'pending').length,
    };
    return stats;
  };

  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-4" />
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md text-center">
            <CardHeader>
              <CardTitle>需要登录</CardTitle>
              <CardDescription>请先登录以查看您的下载历史</CardDescription>
            </CardHeader>
            <CardContent>
              <Link
                href="/login"
                className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90">
                前往登录
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const stats = getStatusStats();

  return (
    <div className="container mx-auto px-4 py-8">
      <div>
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">我的下载</h1>
              <p className="text-muted-foreground mt-2">查看和管理您的所有下载任务</p>
            </div>
            <Button onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-primary">{stats.total}</div>
              <div className="text-xs text-muted-foreground">总任务</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
              <div className="text-xs text-muted-foreground">已完成</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
              <div className="text-xs text-muted-foreground">进行中</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
              <div className="text-xs text-muted-foreground">失败</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
              <div className="text-xs text-muted-foreground">待处理</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 筛选和搜索 */}
      <div>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Filter className="h-4 w-4" />
              筛选和搜索
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索任务</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索任务标题或链接..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">任务状态</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="running">进行中</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                    <SelectItem value="pending">待处理</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">任务类型</label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="single">单次下载</SelectItem>
                    <SelectItem value="batch">批量下载</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 任务列表 */}
      <div>
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">加载任务历史...</p>
              </div>
            </div>
          ) : filteredTasks.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Download className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {tasks.length === 0 ? '暂无下载任务' : '没有找到匹配的任务'}
                </h3>
                <p className="text-muted-foreground">
                  {tasks.length === 0 ? '开始您的第一次下载吧！' : '尝试调整筛选条件或搜索关键词'}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredTasks.map((task) => <TaskHistoryCard key={task.id} task={task} />)
          )}
        </div>
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div>
          <div className="flex items-center justify-center gap-2 mt-8">
            <Button
              variant="outline"
              onClick={() => fetchTasks(currentPage - 1)}
              disabled={currentPage <= 1 || isLoading}>
              上一页
            </Button>
            <span className="text-sm text-muted-foreground">
              第 {currentPage} 页，共 {totalPages} 页
            </span>
            <Button
              variant="outline"
              onClick={() => fetchTasks(currentPage + 1)}
              disabled={currentPage >= totalPages || isLoading}>
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
