'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { Check, Download, Image as ImageIcon, Loader2, Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { VideoPreviewModal } from './video-preview-modal';
import { VideoPageData } from '@/lib/types';
import { formatDuration } from '@/lib/youtube-utils';

interface VideoThumbnailProps {
  videoData: VideoPageData;
}

export function VideoThumbnail({ videoData }: VideoThumbnailProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [downloadingFormat, setDownloadingFormat] = useState<string | null>(null);
  const [downloadedFormats, setDownloadedFormats] = useState<Set<string>>(new Set());

  const handlePlayClick = () => {
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
  };

  const downloadThumbnail = async (url: string, filename: string, format: string) => {
    setDownloadingFormat(format);

    try {
      // 模拟下载延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setDownloadedFormats((prev) => new Set([...prev, format]));
      setTimeout(() => {
        setDownloadedFormats((prev) => {
          const newSet = new Set(prev);
          newSet.delete(format);
          return newSet;
        });
      }, 3000);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setDownloadingFormat(null);
    }
  };

  const thumbnailOptions = [
    {
      format: 'jpg',
      label: 'JPG 格式',
      url: videoData.thumbnailUrl,
      size: '高质量',
      recommended: true,
    },
    ...(videoData.thumbnails.find((t) => t.format === 'png')
      ? [
          {
            format: 'png',
            label: 'PNG 格式',
            url: videoData.thumbnails.find((t) => t.format === 'png')!.url,
            size: '无损压缩',
            recommended: false,
          },
        ]
      : []),
  ];

  return (
    <>
      <Card className="overflow-hidden border-2 border-muted/20 bg-gradient-to-br from-background to-muted/20">
        <CardContent className="p-0">
          <div className="relative group cursor-pointer" onClick={handlePlayClick}>
            <Image
              src={videoData.thumbnailUrl}
              alt={`Thumbnail for ${videoData.title}`}
              width={1280}
              height={720}
              className="aspect-video w-full object-cover transition-transform duration-300 group-hover:scale-105"
              priority
            />

            {/* 播放按钮覆盖层 */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <motion.button
                className="bg-white/90 rounded-full p-4 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                aria-label="播放视频预览"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2 }}>
                <Play className="h-8 w-8 text-primary fill-current" />
              </motion.button>
            </div>

            {/* 时长标签 */}
            <div className="absolute bottom-3 right-3 bg-black/80 text-white px-2 py-1 rounded text-sm font-medium backdrop-blur-sm">
              {formatDuration(videoData.duration)}
            </div>

            {/* 下载按钮 */}
            <div className="absolute top-3 left-3 opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="bg-black/80 text-white border-0 hover:bg-black/90 backdrop-blur-sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDownloadOptions(!showDownloadOptions);
                }}>
                <Download className="h-3 w-3 mr-1" />
                下载
              </Button>
            </div>

            {/* 下载选项弹出层 */}
            <AnimatePresence>
              {showDownloadOptions && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-12 left-3 z-10 bg-white rounded-lg border-2 border-muted/20 p-2 min-w-[200px]"
                  onClick={(e) => e.stopPropagation()}>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 px-2 py-1 text-xs text-muted-foreground border-b">
                      <ImageIcon className="h-3 w-3" />
                      <span>缩略图下载</span>
                    </div>
                    {thumbnailOptions.map((option) => {
                      const isDownloading = downloadingFormat === option.format;
                      const isDownloaded = downloadedFormats.has(option.format);

                      return (
                        <Button
                          key={option.format}
                          variant="ghost"
                          size="sm"
                          className="w-full justify-between h-auto p-2 text-left"
                          onClick={() => {
                            downloadThumbnail(
                              option.url,
                              `thumbnail-${videoData.id}.${option.format}`,
                              option.format,
                            );
                            setShowDownloadOptions(false);
                          }}
                          disabled={isDownloading}>
                          <div className="flex items-center gap-2">
                            {isDownloading ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : isDownloaded ? (
                              <Check className="h-3 w-3 text-green-600" />
                            ) : (
                              <Download className="h-3 w-3" />
                            )}
                            <span className="text-xs font-medium">{option.label}</span>
                            {option.recommended && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-primary/10 text-primary px-1 py-0">
                                推荐
                              </Badge>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {isDownloading ? '下载中...' : isDownloaded ? '已下载' : option.size}
                          </span>
                        </Button>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* 视频预览模态窗口 */}
      <VideoPreviewModal
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        videoData={videoData}
      />
    </>
  );
}
