import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { MyDownloadsContent } from '@/components/features/downloads/my-downloads-content';

export const metadata: Metadata = {
  title: '我的下载 - YTDownloader',
  description: '查看您的下载历史和任务状态',
};

export default function MyDownloadsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <MyDownloadsContent />
      </main>
      <Footer />
    </div>
  );
}
