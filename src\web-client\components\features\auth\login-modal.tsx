'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import Link from 'next/link';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { AlertCircle, Eye, EyeOff, Lock, LogIn, Mail, User, UserPlus, X } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { loginSchema } from '@/lib/auth';

type LoginFormValues = z.infer<typeof loginSchema>;

// 注册表单验证 schema
const registerSchema = z
  .object({
    email: z.string().email('请输入有效的邮箱地址'),
    password: z
      .string()
      .min(8, '密码至少需要8个字符')
      .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码需要包含大小写字母和数字'),
    confirmPassword: z.string(),
    username: z.string().min(2, '用户名至少需要2个字符').optional(),
    agreeToTerms: z.boolean().refine((val) => val === true, '请同意服务条款'),
    agreeToPrivacy: z.boolean().refine((val) => val === true, '请同意隐私政策'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  });

type RegisterFormValues = z.infer<typeof registerSchema>;

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');

  const router = useRouter();
  const { login, register: registerUser, anonymousId } = useAuth();

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      username: '',
      agreeToTerms: false,
      agreeToPrivacy: false,
    },
  });

  const onLoginSubmit = async (data: LoginFormValues) => {
    try {
      setSubmitError('');
      await login(data.email, data.password, '/');
      onClose();
    } catch (error) {
      console.error('Login error:', error);
      setSubmitError('登录失败，请检查您的邮箱和密码');
    }
  };

  const onRegisterSubmit = async (data: RegisterFormValues) => {
    try {
      setSubmitError('');

      const success = await registerUser({
        email: data.email,
        password: data.password,
        username: data.username || undefined,
        anonymousId: anonymousId || undefined,
      });

      if (success) {
        onClose();
        router.push('/');
      } else {
        setSubmitError('注册失败，请稍后重试');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setSubmitError('注册失败，请稍后重试');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden max-h-[90vh] overflow-y-auto">
        <div className="relative">
          {/* 关闭按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-4 top-4 z-10"
            onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>

          {/* 头部 */}
          <DialogHeader className="p-6 pb-4 text-center">
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
              {activeTab === 'login' ? '欢迎回来' : '创建账户'}
            </DialogTitle>
            <p className="text-muted-foreground mt-2">
              {activeTab === 'login' ? '登录您的账户以享受完整功能' : '加入我们，开始您的下载之旅'}
            </p>
          </DialogHeader>

          {/* 标签页和表单内容 */}
          <div className="px-6 pb-6">
            <Tabs
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as 'login' | 'register')}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="login">登录</TabsTrigger>
                <TabsTrigger value="register">注册</TabsTrigger>
              </TabsList>

              {/* 登录标签页 */}
              <TabsContent value="login" className="space-y-4">
                {submitError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{submitError}</AlertDescription>
                  </Alert>
                )}

                <Form {...loginForm}>
                  <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                    {/* 邮箱输入 */}
                    <FormField
                      control={loginForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>邮箱地址</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type="email"
                                placeholder="请输入您的邮箱地址"
                                className="pl-10"
                                disabled={loginForm.formState.isSubmitting}
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 密码输入 */}
                    <FormField
                      control={loginForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>密码</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type={showPassword ? 'text' : 'password'}
                                placeholder="请输入您的密码"
                                className="pl-10 pr-10"
                                disabled={loginForm.formState.isSubmitting}
                                {...field}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={loginForm.formState.isSubmitting}>
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                                ) : (
                                  <Eye className="h-4 w-4 text-muted-foreground" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 忘记密码链接 */}
                    <div className="flex justify-end">
                      <Link
                        href="/forgot-password"
                        className="text-sm text-primary hover:underline"
                        onClick={onClose}>
                        忘记密码？
                      </Link>
                    </div>

                    {/* 登录按钮 */}
                    <Button
                      type="submit"
                      className="w-full h-11 bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] font-semibold"
                      disabled={loginForm.formState.isSubmitting}>
                      {loginForm.formState.isSubmitting ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                          <span className="animate-pulse">登录中...</span>
                        </>
                      ) : (
                        <>
                          <LogIn className="mr-2 h-4 w-4" />
                          立即登录
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              {/* 注册标签页 */}
              <TabsContent value="register" className="space-y-4">
                {submitError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{submitError}</AlertDescription>
                  </Alert>
                )}

                <Form {...registerForm}>
                  <form
                    onSubmit={registerForm.handleSubmit(onRegisterSubmit)}
                    className="space-y-4">
                    {/* 邮箱输入 */}
                    <FormField
                      control={registerForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>邮箱地址 *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type="email"
                                placeholder="请输入您的邮箱地址"
                                className="pl-10"
                                disabled={registerForm.formState.isSubmitting}
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 用户名输入 */}
                    <FormField
                      control={registerForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>用户名（可选）</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type="text"
                                placeholder="请输入用户名"
                                className="pl-10"
                                disabled={registerForm.formState.isSubmitting}
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 密码输入 */}
                    <FormField
                      control={registerForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>密码 *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type={showPassword ? 'text' : 'password'}
                                placeholder="请输入密码（至少8位，包含大小写字母和数字）"
                                className="pl-10 pr-10"
                                disabled={registerForm.formState.isSubmitting}
                                {...field}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={registerForm.formState.isSubmitting}>
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                                ) : (
                                  <Eye className="h-4 w-4 text-muted-foreground" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 确认密码输入 */}
                    <FormField
                      control={registerForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>确认密码 *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                type={showConfirmPassword ? 'text' : 'password'}
                                placeholder="请再次输入密码"
                                className="pl-10 pr-10"
                                disabled={registerForm.formState.isSubmitting}
                                {...field}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                disabled={registerForm.formState.isSubmitting}>
                                {showConfirmPassword ? (
                                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                                ) : (
                                  <Eye className="h-4 w-4 text-muted-foreground" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 服务条款和隐私政策 */}
                    <div className="space-y-3">
                      <FormField
                        control={registerForm.control}
                        name="agreeToTerms"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={registerForm.formState.isSubmitting}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-sm">
                                我同意{' '}
                                <Link
                                  href="/terms"
                                  className="text-primary hover:underline"
                                  onClick={onClose}>
                                  服务条款
                                </Link>
                              </FormLabel>
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={registerForm.control}
                        name="agreeToPrivacy"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={registerForm.formState.isSubmitting}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-sm">
                                我同意{' '}
                                <Link
                                  href="/privacy"
                                  className="text-primary hover:underline"
                                  onClick={onClose}>
                                  隐私政策
                                </Link>
                              </FormLabel>
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* 注册按钮 */}
                    <Button
                      type="submit"
                      className="w-full h-11 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-500/90 hover:to-blue-600/90 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] font-semibold"
                      disabled={registerForm.formState.isSubmitting}>
                      {registerForm.formState.isSubmitting ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                          <span className="animate-pulse">注册中...</span>
                        </>
                      ) : (
                        <>
                          <UserPlus className="mr-2 h-4 w-4" />
                          立即注册
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
