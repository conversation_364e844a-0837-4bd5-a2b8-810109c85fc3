// 链接类型枚举
export type LinkType = 'VIDEO' | 'SHORTS' | 'PLAYLIST' | 'CHANNEL' | 'MULTI_VIDEO_LIST' | 'INVALID';

// 从链接中提取的实体信息
export interface ExtractedEntity {
  type: LinkType;
  videoId?: string | null;
  playlistId?: string | null;
  channelId?: string | null;
}

// YouTube URL 正则表达式
const YOUTUBE_URL_REGEX =
  /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/(watch\?v=|embed\/|v\/|shorts\/|playlist\?list=|channel\/|user\/|c\/)?([a-zA-Z0-9\-_]+)/;

// 分析单个 URL，判断其类型并提取相关 ID
export function analyzeUrl(url: string): ExtractedEntity {
  if (!url || !YOUTUBE_URL_REGEX.test(url.trim())) {
    return { type: 'INVALID' };
  }

  const trimmedUrl = url.trim();

  try {
    const urlObj = new URL(trimmedUrl);
    const urlParams = new URLSearchParams(urlObj.search);

    // 检查 Shorts 链接 (youtube.com/shorts/VIDEO_ID)
    const shortsMatch = trimmedUrl.match(/\/shorts\/([a-zA-Z0-9\-_]+)/);
    if (shortsMatch && shortsMatch[1]) {
      return { type: 'SHORTS', videoId: shortsMatch[1] };
    }

    // 当 v= 和 list= 共存时，优先识别为视频链接
    if (urlParams.has('v')) {
      const videoId = urlParams.get('v');
      if (videoId) {
        return { type: 'VIDEO', videoId };
      }
    }

    // 检查播放列表链接
    if (urlParams.has('list')) {
      const playlistId = urlParams.get('list');
      if (playlistId) {
        return { type: 'PLAYLIST', playlistId };
      }
    }

    // 检查频道链接
    const channelMatch = trimmedUrl.match(/\/channel\/([a-zA-Z0-9\-_]+)/);
    if (channelMatch && channelMatch[1]) {
      return { type: 'CHANNEL', channelId: channelMatch[1] };
    }

    // 检查用户链接 (转换为频道)
    const userMatch = trimmedUrl.match(/\/(user|c)\/([a-zA-Z0-9\-_]+)/);
    if (userMatch && userMatch[2]) {
      return { type: 'CHANNEL', channelId: userMatch[2] };
    }

    // 检查 youtu.be/xxxx 格式
    const shortLinkMatch = trimmedUrl.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/);
    if (shortLinkMatch && shortLinkMatch[1]) {
      return { type: 'VIDEO', videoId: shortLinkMatch[1] };
    }

    return { type: 'INVALID' };
  } catch {
    return { type: 'INVALID' };
  }
}

// 将字节数转换为可读的文件大小格式
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 将字节数转换为可读的大小格式（别名函数）
export function bytesToSize(bytes: number): string {
  return formatFileSize(bytes);
}

// 将秒数转换为 hh:mm:ss 格式
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

// 将 YouTube 时长字符串转换为 hh:mm:ss 格式
export function formatYouTubeDuration(duration: string): string {
  // 如果已经是 mm:ss 或 hh:mm:ss 格式，转换为标准格式
  if (duration.includes(':')) {
    const parts = duration.split(':').map(Number);
    if (parts.length === 2) {
      // mm:ss 格式
      return `${parts[0].toString().padStart(2, '0')}:${parts[1].toString().padStart(2, '0')}`;
    } else if (parts.length === 3) {
      // hh:mm:ss 格式
      return `${parts[0].toString().padStart(2, '0')}:${parts[1].toString().padStart(2, '0')}:${parts[2].toString().padStart(2, '0')}`;
    }
  }

  // 如果是 ISO 8601 格式 (PT4M13S)
  if (duration.startsWith('PT')) {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (match) {
      const hours = parseInt(match[1] || '0');
      const minutes = parseInt(match[2] || '0');
      const seconds = parseInt(match[3] || '0');

      const totalSeconds = hours * 3600 + minutes * 60 + seconds;
      return formatDuration(totalSeconds);
    }
  }

  // 如果无法解析，返回原始字符串
  return duration;
}

// 格式化数字，添加千位分隔符
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

// 格式化日期
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

// 格式化相对时间
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months}个月前`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}年前`;
  }
}
