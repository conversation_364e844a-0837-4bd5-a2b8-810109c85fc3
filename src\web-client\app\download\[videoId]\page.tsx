import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { CalendarDays, Eye, Link as ExternalLink } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { ErrorDisplay } from '@/components/shared/error-display';

import { VideoDescription } from '@/components/features/download/video-description';
import { CommentDownloader } from '@/components/features/download/comment-downloader';
import { DownloadOptionsTabs } from '@/components/features/download/download-options-tabs';
import { VideoThumbnail } from '@/components/features/download/video-thumbnail';
import { getVideoData } from '@/lib/api';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ videoId: string }>;
}): Promise<Metadata> {
  const { videoId } = await params;

  try {
    const videoData = await getVideoData(videoId);
    if (videoData) {
      return {
        title: `下载 ${videoData.title} - YTDownloader`,
        description: `下载YouTube视频"${videoData.title}"，支持多种格式和画质选择，包含视频剪辑功能。`,
      };
    }
  } catch {
    // 忽略错误，使用默认metadata
  }

  return {
    title: '视频下载 - YTDownloader',
    description: '下载YouTube视频，支持多种格式和画质选择，包含视频剪辑功能。',
  };
}

export default async function DownloadPage({ params }: { params: Promise<{ videoId: string }> }) {
  const { videoId } = await params;
  const videoData = await getVideoData(videoId);

  // 如果 videoData 为 null，可以渲染一个错误组件或调用 notFound()
  if (!videoData) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
          <ErrorDisplay
            title="无法加载此视频"
            description="该视频可能已被删除、设为私有，或存在区域限制。请检查链接是否正确。"
          />
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-gradient-to-br from-background via-muted/10 to-background">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-10">
            {/* 左侧区域: 视频信息概览 */}
            <aside className="lg:col-span-3">
              <div className="sticky top-24 space-y-6">
                {/* 视频缩略图卡片 */}
                <VideoThumbnail videoData={videoData} />

                {/* 视频基本信息卡片 */}
                <Card className="border-2 border-muted/20 bg-gradient-to-br from-background to-muted/20">
                  <CardContent className="p-4">
                    {/* 视频标题 */}
                    <h1 className="text-lg font-bold leading-tight line-clamp-2 mb-3">
                      {videoData.title}
                    </h1>

                    {/* 紧凑的信息布局 */}
                    <div className="space-y-2">
                      {/* 频道信息 */}
                      <div className="flex items-center gap-2">
                        <a
                          href={videoData.channelUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-medium text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1">
                          {videoData.channelName}
                          <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" />
                        </a>
                      </div>

                      {/* 统计信息 */}
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{videoData.viewCount.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <CalendarDays className="h-4 w-4" />
                          <span>{new Date(videoData.uploadDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 视频描述 */}
                <VideoDescription description={videoData.description} />

                {/* 评论下载 */}
                <CommentDownloader
                  commentsDisabled={videoData.commentsDisabled}
                  commentCount={videoData.commentCount}
                />
              </div>
            </aside>

            {/* 右侧区域: 下载选项 */}
            <section className="lg:col-span-7">
              <div className="space-y-6">
                {/* 下载选项 */}
                <Card className="border-2 border-muted/20">
                  <CardContent>
                    <DownloadOptionsTabs videoData={videoData} />
                  </CardContent>
                </Card>
              </div>
            </section>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
