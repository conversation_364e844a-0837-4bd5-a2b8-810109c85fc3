import { LinkInputForm } from '@/components/shared/link-input-form';
import { Badge } from '@/components/ui/badge';
import { Shield, Users, Zap } from 'lucide-react';

export function HeroSection() {
  return (
    <section
      id="hero"
      className="relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
      <div className="absolute top-0 right-0 -translate-y-12 translate-x-12 transform">
        <div className="h-64 w-64 rounded-full bg-primary/10 blur-3xl" />
      </div>
      <div className="absolute bottom-0 left-0 translate-y-12 -translate-x-12 transform">
        <div className="h-64 w-64 rounded-full bg-blue-500/10 blur-3xl" />
      </div>

      <div className="container relative mx-auto flex flex-col items-center justify-center px-4 py-20 text-center">
        {/* 信任徽章 */}
        <div className="mb-6 flex flex-wrap items-center justify-center gap-3">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            100% 安全
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Zap className="h-3 w-3" />
            极速下载
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            用户信赖
          </Badge>
        </div>

        {/* 主标题 */}
        <h1 className="text-4xl font-bold tracking-tight md:text-6xl lg:text-7xl bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
          一键下载 无限可能
        </h1>

        {/* 副标题 */}
        <p className="mt-6 max-w-3xl text-lg text-muted-foreground md:text-xl leading-relaxed">
          专业的 YouTube 内容下载平台，支持高清视频、音频、字幕等多种格式下载。
          <br />
          安全快速，无需注册，完全免费使用。让您轻松保存喜爱的内容，随时随地享受精彩视频。
          <br />
          <span className="font-semibold text-foreground">立即体验，开启您的内容收藏之旅</span>
        </p>

        {/* 输入表单 */}
        <div className="mt-12 w-full max-w-4xl">
          <LinkInputForm />
        </div>

        {/* 支持格式提示 */}
        <div className="mt-6 flex flex-wrap items-center justify-center gap-2 text-sm text-muted-foreground">
          <span>支持格式：</span>
          <Badge variant="outline">MP4</Badge>
          <Badge variant="outline">MP3</Badge>
          <Badge variant="outline">SRT</Badge>
          <Badge variant="outline">4K</Badge>
          <Badge variant="outline">8K</Badge>
        </div>
      </div>
    </section>
  );
}
