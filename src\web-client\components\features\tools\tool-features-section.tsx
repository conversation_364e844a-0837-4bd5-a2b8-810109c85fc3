import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ir<PERSON>, Settings, Sparkles } from 'lucide-react';

interface ToolFeaturesSectionProps {
  features: string[];
  supportedFormats?: string[];
}

export function ToolFeaturesSection({ features, supportedFormats }: ToolFeaturesSectionProps) {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="grid gap-6 md:grid-cols-2 max-w-6xl mx-auto">
          {/* Features Card */}
          <div>
            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  核心功能
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-4">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3 group/item">
                      <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded-full mt-0.5">
                        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-sm font-medium group-hover/item:text-blue-600 transition-colors">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Supported Formats Card */}
          {supportedFormats && (
            <div>
              <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-green-50/30 dark:from-gray-900 dark:to-green-900/10 h-full">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg">
                      <Settings className="h-5 w-5 text-white" />
                    </div>
                    支持格式
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-3">
                    {supportedFormats.map((format, index) => (
                      <div key={index}>
                        <Badge
                          variant="secondary"
                          className="px-3 py-1 text-sm font-medium bg-gradient-to-r from-green-100 to-teal-100 text-green-700 hover:from-green-200 hover:to-teal-200 transition-all duration-200 cursor-default">
                          {format}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* How to Use Card - 如果没有支持格式，则占满两列 */}
          <div className={`${!supportedFormats ? 'md:col-span-2' : 'md:col-span-2'}`}>
            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-purple-50/30 dark:from-gray-900 dark:to-purple-900/10">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  使用方法
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="text-center group/step">
                    <div className="relative mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-2xl flex items-center justify-center mx-auto text-lg font-bold group-hover/step:scale-110 transition-transform duration-300">
                        1
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
                    </div>
                    <h3 className="font-semibold mb-2 text-lg">粘贴链接</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      复制YouTube链接并粘贴到上方输入框
                    </p>
                  </div>

                  <div className="text-center group/step">
                    <div className="relative mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl flex items-center justify-center mx-auto text-lg font-bold group-hover/step:scale-110 transition-transform duration-300">
                        2
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full animate-pulse" />
                    </div>
                    <h3 className="font-semibold mb-2 text-lg">选择选项</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      根据需要选择格式、质量等参数
                    </p>
                  </div>

                  <div className="text-center group/step">
                    <div className="relative mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-2xl flex items-center justify-center mx-auto text-lg font-bold group-hover/step:scale-110 transition-transform duration-300">
                        3
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-pulse" />
                    </div>
                    <h3 className="font-semibold mb-2 text-lg">开始处理</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      点击按钮开始处理并下载结果
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
