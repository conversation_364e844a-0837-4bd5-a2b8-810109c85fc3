'use client';

import { useState } from 'react';
import { BatchVideoItem } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { AlertCircle, Calendar, Clock, Download, Eye, FileText, RotateCcw, X } from 'lucide-react';
import Image from 'next/image';
import { formatDate, formatDuration, formatNumber } from '@/lib/youtube-utils';
import { cancelVideoProcessing, retryFailedVideo } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface VideoListItemProps {
  video: BatchVideoItem;
  index: number;
  isJobStarted: boolean;
  batchJobId: string;
  onSelect: (videoId: string, selected: boolean) => void;
  onUpdate: (videoId: string, updates: Partial<BatchVideoItem>) => void;
}

export function VideoListItem({
  video,
  index,
  isJobStarted,
  batchJobId,
  onSelect,
  onUpdate,
}: VideoListItemProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleRetry = async () => {
    setIsLoading(true);
    try {
      const success = await retryFailedVideo(batchJobId, video.id);
      if (success) {
        onUpdate(video.id, { status: 'queued', progress: 0, error: undefined });
        toast({
          title: '重试成功',
          description: '视频已重新加入处理队列',
        });
      }
    } catch (error) {
      toast({
        title: '重试失败',
        description: '无法重试该视频',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = async () => {
    setIsLoading(true);
    try {
      const success = await cancelVideoProcessing(batchJobId, video.id);
      if (success) {
        onUpdate(video.id, { status: 'cancelled', progress: 0 });
        toast({
          title: '已取消',
          description: '视频处理已取消',
        });
      }
    } catch (error) {
      toast({
        title: '取消失败',
        description: '无法取消该视频的处理',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">待处理</Badge>;
      case 'queued':
        return <Badge variant="outline">排队中</Badge>;
      case 'processing':
        return <Badge className="bg-blue-500">处理中</Badge>;
      case 'completed':
        return <Badge className="bg-green-500">已完成</Badge>;
      case 'failed':
        return <Badge variant="destructive">失败</Badge>;
      case 'cancelled':
        return <Badge variant="outline">已取消</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const renderActionButton = () => {
    if (!isJobStarted) return null;

    switch (video.status) {
      case 'completed':
        return (
          <Dialog>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline">
                <Download className="mr-1 h-4 w-4" />
                文件列表
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>下载文件</DialogTitle>
              </DialogHeader>
              <div className="space-y-3">
                {video.completedFiles?.map((file, idx) => (
                  <div
                    key={idx}
                    className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{file.filename}</p>
                      <p className="text-sm text-muted-foreground">
                        {file.format} • {formatNumber(file.fileSize)} bytes
                      </p>
                    </div>
                    <Button size="sm" asChild>
                      <a href={file.downloadUrl} download>
                        <Download className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                )) || <p className="text-muted-foreground">暂无可下载文件</p>}
              </div>
            </DialogContent>
          </Dialog>
        );

      case 'failed':
        return (
          <Button size="sm" variant="outline" onClick={handleRetry} disabled={isLoading}>
            <RotateCcw className="mr-1 h-4 w-4" />
            重试
          </Button>
        );

      case 'processing':
      case 'queued':
        return (
          <Button size="sm" variant="outline" onClick={handleCancel} disabled={isLoading}>
            <X className="mr-1 h-4 w-4" />
            取消
          </Button>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex items-center gap-2 rounded-lg border p-2 hover:bg-muted/50 transition-colors">
      {/* 选择框和序号 */}
      <div className="flex items-center gap-1">
        <Checkbox
          checked={video.selected}
          onCheckedChange={(checked) => onSelect(video.id, checked as boolean)}
          disabled={isJobStarted}
        />
        <span className="text-xs text-muted-foreground w-5 text-center">{index}</span>
      </div>

      {/* 缩略图 */}
      <div className="relative h-8 w-14 flex-shrink-0 overflow-hidden rounded bg-muted">
        {video.thumbnailUrl ? (
          <Image
            src={video.thumbnailUrl}
            alt={video.title || `Video ${video.id}`}
            fill
            className="object-cover"
            sizes="56px"
          />
        ) : (
          <div className="flex h-full items-center justify-center">
            <FileText className="h-3 w-3 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* 视频信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center text-sm leading-tight">
          <h4 className="font-medium truncate mr-2">{video.title || `视频 ID: ${video.id}`}</h4>

          <div className="flex items-center gap-3 text-muted-foreground">
            <div className="flex items-center gap-1 w-16 justify-end">
              {video.duration && (
                <>
                  <Clock className="h-3 w-3" />
                  <span>{formatDuration(video.duration)}</span>
                </>
              )}
            </div>

            <div className="flex items-center gap-1 w-20 justify-end">
              {video.viewCount && (
                <>
                  <Eye className="h-3 w-3" />
                  <span>{formatNumber(video.viewCount)}</span>
                </>
              )}
            </div>

            <div className="flex items-center gap-1 w-20 justify-end">
              {video.uploadDate && (
                <>
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(video.uploadDate)}</span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 进度条 (仅在处理中时显示) */}
        {video.status === 'processing' && video.progress > 0 && (
          <div className="mt-0.5">
            <div className="flex items-center justify-between text-xs mb-0.5">
              <span>处理进度</span>
              <span>{video.progress}%</span>
            </div>
            <Progress value={video.progress} className="h-1" />
          </div>
        )}

        {/* 错误信息 */}
        {video.status === 'failed' && video.error && (
          <div className="flex items-start gap-1 text-xs text-destructive mt-0.5">
            <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
            <span className="line-clamp-1">{video.error}</span>
          </div>
        )}
      </div>

      {/* 状态和操作 */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {getStatusBadge(video.status)}
        {renderActionButton()}
      </div>
    </div>
  );
}
