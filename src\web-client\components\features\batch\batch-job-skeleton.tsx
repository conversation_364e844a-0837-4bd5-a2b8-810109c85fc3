import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function BatchJobSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面头部信息条骨架 */}
      <div className="mb-6">
        <Skeleton className="h-6 w-96" />
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-12">
        {/* 左侧区域: 全局配置骨架 */}
        <aside className="lg:col-span-4">
          <div className="sticky top-24 space-y-6">
            {/* 任务源信息骨架 */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-16 w-16 rounded" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-full" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 全局配置骨架 */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 下载内容类型 */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-32" />
                  <div className="grid grid-cols-2 gap-3">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    ))}
                  </div>
                </div>

                {/* 质量设置 */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>

                {/* 筛选条件 */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-20" />
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                </div>

                {/* 启动按钮 */}
                <Skeleton className="h-12 w-full" />
              </CardContent>
            </Card>
          </div>
        </aside>

        {/* 右侧区域: 任务内容骨架 */}
        <main className="lg:col-span-8">
          <div className="space-y-6">
            {/* 任务源信息骨架 */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-64" />
              </CardContent>
            </Card>

            {/* 视频列表骨架 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <Skeleton className="h-6 w-32" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 列表头部控制 */}
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>

                  {/* 视频列表项骨架 */}
                  <div className="space-y-3">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-4 rounded-lg border p-4">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-3 w-8" />
                        <Skeleton className="h-16 w-28 rounded" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <div className="flex gap-4">
                            <Skeleton className="h-3 w-16" />
                            <Skeleton className="h-3 w-20" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        </div>
                        <Skeleton className="h-8 w-20" />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
