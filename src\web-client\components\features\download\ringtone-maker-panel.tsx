'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Alert<PERSON>ircle,
  CheckCircle,
  Clock,
  Download,
  Loader2,
  Minus,
  Phone,
  Play,
  Plus,
  QrCode,
  Smartphone,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { VideoPageData } from '@/lib/types';
import { formatDuration } from '@/lib/youtube-utils';

interface RingtoneMakerPanelProps {
  videoData: VideoPageData;
  onRingtoneGenerate: (
    startTime: number,
    endTime: number,
    format: string,
    fadeEffects: boolean,
  ) => void;
}

interface TimeInput {
  hours: number;
  minutes: number;
  seconds: number;
}

export function RingtoneMakerPanel({ videoData, onRingtoneGenerate }: RingtoneMakerPanelProps) {
  const [startTime, setStartTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 0 });
  const [endTime, setEndTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 15 });
  const [outputFormat, setOutputFormat] = useState<'m4r' | 'mp3'>('m4r');
  const [fadeEffects, setFadeEffects] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedRingtone, setGeneratedRingtone] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [estimatedSize, setEstimatedSize] = useState<number>(0);

  // 将时间对象转换为秒数
  const timeToSeconds = (time: TimeInput): number => {
    return time.hours * 3600 + time.minutes * 60 + time.seconds;
  };

  // 将秒数转换为时间对象
  const secondsToTime = (seconds: number): TimeInput => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return { hours, minutes, seconds: secs };
  };

  // 验证时间范围
  const validateTimeRange = () => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    const valid =
      startSeconds >= 0 &&
      endSeconds > startSeconds &&
      endSeconds <= videoData.duration &&
      duration >= 1 &&
      duration <= 30;

    setIsValid(valid);

    if (valid) {
      // 估算铃声文件大小 (128kbps)
      const bitrate = 128; // kbps
      const sizeInMB = (duration * bitrate) / (8 * 1024);
      setEstimatedSize(Math.round(sizeInMB * 100) / 100);
    } else {
      setEstimatedSize(0);
    }
  };

  // 时间输入变化处理
  const handleTimeChange = (
    type: 'start' | 'end',
    field: 'hours' | 'minutes' | 'seconds',
    value: number,
  ) => {
    const newTime = { ...(type === 'start' ? startTime : endTime) };
    newTime[field] = Math.max(0, value);

    // 限制范围
    if (field === 'hours') newTime[field] = Math.min(23, newTime[field]);
    if (field === 'minutes' || field === 'seconds') newTime[field] = Math.min(59, newTime[field]);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 微调时间
  const adjustTime = (type: 'start' | 'end', delta: number) => {
    const currentSeconds = timeToSeconds(type === 'start' ? startTime : endTime);
    const newSeconds = Math.max(0, Math.min(videoData.duration, currentSeconds + delta));
    const newTime = secondsToTime(newSeconds);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 格式化时间显示
  const formatTimeInput = (time: TimeInput): string => {
    return `${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
  };

  // 解析时间输入
  const parseTimeInput = (timeStr: string): TimeInput => {
    const parts = timeStr.split(':').map(Number);
    return {
      hours: parts[0] || 0,
      minutes: parts[1] || 0,
      seconds: parts[2] || 0,
    };
  };

  // 制作铃声
  const handleMakeRingtone = async () => {
    if (!isValid) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedRingtone(null);

    try {
      // 模拟制作过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setGenerationProgress(i);
      }

      // 模拟生成的铃声URL
      const mockRingtoneUrl = `data:audio/${outputFormat === 'm4r' ? 'mp4' : 'mp3'};base64,`;
      setGeneratedRingtone(mockRingtoneUrl);

      // 调用回调
      onRingtoneGenerate(
        timeToSeconds(startTime),
        timeToSeconds(endTime),
        outputFormat,
        fadeEffects,
      );
    } catch (error) {
      console.error('铃声制作失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    validateTimeRange();
  }, [startTime, endTime, videoData.duration]);

  // 获取验证错误信息
  const getValidationError = (): string | null => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    if (startSeconds < 0) return '开始时间不能为负数';
    if (endSeconds <= startSeconds) return '结束时间必须大于开始时间';
    if (endSeconds > videoData.duration) return '结束时间不能超过视频总时长';
    if (duration < 1) return '铃声时长至少为1秒';
    if (duration > 30) return '铃声时长不能超过30秒';

    return null;
  };

  const validationError = getValidationError();
  const duration = timeToSeconds(endTime) - timeToSeconds(startTime);

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Phone className="h-5 w-5 text-primary" />
          铃声制作
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 时间范围设置 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 开始时间 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">开始时间</Label>
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={formatTimeInput(startTime)}
                onChange={(e) => setStartTime(parseTimeInput(e.target.value))}
                placeholder="00:00:00"
                className="font-mono text-center"
              />
              <div className="flex flex-col gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('start', 1)}>
                  <Plus className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('start', -1)}>
                  <Minus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          {/* 结束时间 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">结束时间</Label>
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={formatTimeInput(endTime)}
                onChange={(e) => setEndTime(parseTimeInput(e.target.value))}
                placeholder="00:00:15"
                className="font-mono text-center"
              />
              <div className="flex flex-col gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('end', 1)}>
                  <Plus className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={() => adjustTime('end', -1)}>
                  <Minus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 铃声设置 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">铃声设置</Label>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 输出格式 */}
            <div className="space-y-2">
              <Label className="text-sm">输出格式</Label>
              <Select
                value={outputFormat}
                onValueChange={(value: 'm4r' | 'mp3') => setOutputFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="m4r">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4" />
                      M4R (iPhone)
                    </div>
                  </SelectItem>
                  <SelectItem value="mp3">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      MP3 (Android)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 音质设置 */}
            <div className="space-y-2">
              <Label className="text-sm">音质</Label>
              <div className="flex items-center h-10 px-3 border rounded-md bg-muted">
                <span className="text-sm">128kbps (固定)</span>
              </div>
            </div>
          </div>

          {/* 淡入淡出效果 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="fade-effects"
              checked={fadeEffects}
              onCheckedChange={(checked) => setFadeEffects(checked as boolean)}
            />
            <Label htmlFor="fade-effects" className="text-sm">
              启用淡入淡出效果 (2秒)
            </Label>
          </div>
        </div>

        {/* 时间和大小信息 */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>时长: {formatDuration(Math.max(0, duration))}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>限制: 1-30秒</span>
            </div>
          </div>
          {isValid && (
            <Badge variant="secondary" className="bg-orange-100 text-orange-800">
              预估大小: {estimatedSize} MB
            </Badge>
          )}
        </div>

        {/* 验证错误提示 */}
        {validationError && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
            <span className="text-sm text-red-700">{validationError}</span>
          </div>
        )}

        {/* 快捷时间设置 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">快捷设置</Label>
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setStartTime({ hours: 0, minutes: 0, seconds: 0 });
                setEndTime({ hours: 0, minutes: 0, seconds: 10 });
              }}>
              前10秒
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setStartTime({ hours: 0, minutes: 0, seconds: 0 });
                setEndTime({ hours: 0, minutes: 0, seconds: 20 });
              }}>
              前20秒
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setStartTime({ hours: 0, minutes: 0, seconds: 0 });
                setEndTime({ hours: 0, minutes: 0, seconds: 30 });
              }}>
              前30秒
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const totalSeconds = videoData.duration;
                const midPoint = Math.floor(totalSeconds / 2);
                setStartTime(secondsToTime(Math.max(0, midPoint - 10)));
                setEndTime(secondsToTime(Math.min(totalSeconds, midPoint + 10)));
              }}>
              中间20秒
            </Button>
          </div>
        </div>

        {/* 制作按钮和进度 */}
        <div className="space-y-3">
          <Button
            onClick={handleMakeRingtone}
            disabled={!isValid || isGenerating}
            className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
            size="lg">
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                制作中...
              </>
            ) : (
              <>
                <Phone className="h-4 w-4 mr-2" />
                制作铃声
              </>
            )}
          </Button>

          {/* 制作进度 */}
          {isGenerating && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>制作进度</span>
                <span>{generationProgress}%</span>
              </div>
              <Progress value={generationProgress} className="w-full" />
            </div>
          )}
        </div>

        {/* 制作结果 */}
        {generatedRingtone && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3">
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700">铃声制作完成！</span>
            </div>

            {/* 铃声预览和下载 */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="space-y-4">
                {/* 音频播放器 */}
                <div className="flex items-center justify-center p-4 bg-white rounded border">
                  <div className="flex items-center gap-4">
                    <Button size="sm" variant="outline">
                      <Play className="h-4 w-4 mr-2" />
                      试听铃声
                    </Button>
                    <div className="text-sm text-gray-600">
                      {formatDuration(duration)} • {outputFormat.toUpperCase()} • 128kbps
                      {fadeEffects && ' • 淡入淡出'}
                    </div>
                  </div>
                </div>

                {/* 下载选项 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Download className="h-4 w-4 mr-2" />
                    下载文件
                  </Button>
                  <Button variant="outline" className="border-gray-300">
                    <QrCode className="h-4 w-4 mr-2" />
                    扫码下载
                  </Button>
                </div>

                {/* 使用说明 */}
                <div className="text-xs text-gray-500 space-y-1">
                  <div className="font-medium">使用说明：</div>
                  <div>• iPhone: 下载后通过iTunes同步或AirDrop传输</div>
                  <div>• Android: 下载后放入 /Ringtones 文件夹</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
