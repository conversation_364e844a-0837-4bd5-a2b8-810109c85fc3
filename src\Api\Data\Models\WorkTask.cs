using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public enum WorkTaskType
{
    VideoDownload,
    AudioConvert,
    GifCreate,
    SubtitleDownload,
    CommentDownload,
    ThumbnailDownload
}

public enum WorkTaskStatus
{
    Pending,
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled
}

public class WorkTask
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid UserId { get; set; }
    public User User { get; set; } = null!;
    public Guid? BatchJobId { get; set; }
    public BatchJob? BatchJob { get; set; }
    public WorkTaskType Type { get; set; }
    public required string Params { get; set; }
    public WorkTaskStatus Status { get; set; } = WorkTaskStatus.Pending;
    public int Progress { get; set; }
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? Result { get; set; }
    public string? ErrorMessage { get; set; }
}

public class WorkTaskConfiguration : IEntityTypeConfiguration<WorkTask>
{
    public void Configure(EntityTypeBuilder<WorkTask> builder)
    {
        // 主键配置
        builder.HasKey(wt => wt.Id);
        // 基本属性配置
        builder.Property(wt => wt.UserId).IsRequired();
        builder.Property(wt => wt.Type).IsRequired().HasConversion<string>();
        builder.Property(wt => wt.Status).IsRequired().HasConversion<string>();
        builder.Property(wt => wt.Params).IsRequired().HasColumnType("jsonb");
        builder.Property(wt => wt.Result).HasColumnType("jsonb");
        builder.Property(wt => wt.Progress).HasDefaultValue(0);
        // 关系配置
        builder.HasOne(wt => wt.User).WithMany(u => u.WorkTasks).HasForeignKey(wt => wt.UserId).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(wt => wt.BatchJob).WithMany(bj => bj.WorkTasks).HasForeignKey(wt => wt.BatchJobId).OnDelete(DeleteBehavior.Cascade);
        // 索引配置
        builder.HasIndex(wt => wt.UserId);
        builder.HasIndex(wt => wt.Status);
        builder.HasIndex(wt => wt.CreatedAt);
        // 默认值配置
        builder.Property(wt => wt.Status).HasDefaultValue(WorkTaskStatus.Pending);
        builder.Property(wt => wt.CreatedAt).HasDefaultValueSql("NOW()");
    }
}