﻿"restore":{"projectUniqueName":"D:\\学习\\YTDownloader\\src\\Worker\\Worker.csproj","projectName":"Worker","projectPath":"D:\\学习\\YTDownloader\\src\\Worker\\Worker.csproj","outputPath":"D:\\学习\\YTDownloader\\src\\Worker\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\学习\\YTDownloader\\src\\Shared\\Shared.csproj":{"projectPath":"D:\\学习\\YTDownloader\\src\\Shared\\Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.8, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}