import { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar } from 'lucide-react';

interface LegalContentProps {
  title: string;
  subtitle?: string;
  lastUpdated: string;
  children: ReactNode;
}

export function LegalContent({ title, subtitle, lastUpdated, children }: LegalContentProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="container mx-auto px-4 py-12">
        {/* 页面头部 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 md:text-5xl">{title}</h1>

          {subtitle && (
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-6">{subtitle}</p>
          )}

          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>最后更新：{lastUpdated}</span>
          </div>
        </div>

        {/* 主要内容 */}
        <Card className="max-w-4xl mx-auto border-0 shadow-lg">
          <CardContent className="p-8 md:p-12">
            <div className="prose prose-gray dark:prose-invert max-w-none">{children}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// 法律文档章节组件
export function LegalSection({ title, children }: { title: string; children: ReactNode }) {
  return (
    <section className="mb-8">
      <h2 className="text-2xl font-semibold mb-4 text-foreground border-b pb-2">{title}</h2>
      <div className="space-y-4 text-muted-foreground leading-relaxed">{children}</div>
    </section>
  );
}

// 法律文档子章节组件
export function LegalSubSection({ title, children }: { title: string; children: ReactNode }) {
  return (
    <div className="mb-6">
      <h3 className="text-lg font-medium mb-3 text-foreground">{title}</h3>
      <div className="space-y-2 pl-4 border-l-2 border-muted">{children}</div>
    </div>
  );
}
