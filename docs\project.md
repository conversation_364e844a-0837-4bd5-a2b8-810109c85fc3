# 1. 项目概述

开发专业YouTube内容下载平台，提供免费及付费服务，支持单次与批量处理，实现高可用/可扩展/高效率。单次处理针对单个视频链接，下载视音频/字幕/评论/元数据等多维内容，并集成在线剪辑与格式转换等功能。批量处理支持输入播放列表/频道/自定义视频链接列表，进行自动化、可配置的批量内容获取。

# 2. 核心业务与规则

## 2.1. 核心实体

### 2.1.1. 工作任务（WorkTask）

- **定义**: 系统中最小的可调度执行单元，代表后台异步操作（如文件下载/格式转换）。某些任务包含多个内部顺序步骤（如“下载->剪辑->转换”），不再拆分，其进度基于步骤完成情况来更新。通过两种流程创建：
  - **单次请求**: 在单次下载页发起的独立操作，不属于任何批量作业。
  - **批量作业**: 一个批量作业根据其配置，为其视频集合中的每个处理项自动生成。
- **核心属性**:
  - `工作任务ID`: 全局唯一标识符。
  - `用户ID`: 任务发起者。
  - `批量作业ID`: 关联的父批量作业ID。若为单次任务，此值为null。
  - `工作任务类型`: 操作类型（如 `VIDEO_DOWNLOAD`, `AUDIO_CONVERT`, `GIF_CREATE` 等）。
  - `参数`: 任务具体参数（如: `{"videoId": "...", "resolution": "1080p", "startTime": "00:01:30"}`）。
  - `状态`: 待处理、排队中、处理中、完成、失败、已取消。
  - `进度`: 完成百分比。
  - `创建/开始/结束时间`
  - `结果`: 任务成功完成后的结果（如: `{"downloadUrl": "...", "filePath": "...", "fileSize": 123456}`）。
  - `错误信息`: 失败时的错误描述。

### 2.1.2 批量作业 (BatchJob)

- **定义**: 管理一组因同一次批量处理请求而产生的相关工作任务的逻辑容器，定义批量操作的源、配置并聚合展示整体状态。与工作任务是一对多关系，通过工作任务的批量作业ID关联。
- **核心属性**:
  - `批量作业ID`: 全局唯一标识符。
  - `用户ID`: 任务发起者。
  - `源类型`: 输入源类型，如播放列表（`PLAYLIST`）、频道（`CHANNEL`）、多视频列表（`MULTI_VIDEO_LIST`）。
  - `源标识符`: 输入源标识，如 `{"playlistId": "PL..."}` 或 `{"videoIds": ["id1", "id2"]}`。
  - `配置`: 全局下载配置。如 `{"videoQuality": "1080p", "formats": ["video", "subtitle"]}`。
  - `状态`: 由所有子工作任务的状态聚合得出的整体状态，如未开始、运行中、部分完成、已完成、失败、已取消。
  - `进度`: 完成百分比。
  - `创建/开始/结束时间`

## 2.2. 用户与商业规则

### 2.2.1. 用户模型

- **匿名用户**: 首次使用时通过 HttpOnly、Secure、SameSite=Lax 的 Cookie (`anonymous_id`) 持久化在客户端，有效期一年。匿名用户工作任务、批量作业及其他相关数据，都与此`anonymous_id`关联。支持在注册时将匿名数据迁移至新账户。
- **注册用户**: 邮箱密码认证，使用安全的会话 Cookie维持身份。

### 2.2.2. 商业模式与服务

- **商业模式**: 免费增值。基础功能免费，高级功能付费订阅。
- **服务限制**: 免费版（所有用户默认服务等级，包括匿名和注册），专业版（付费订阅服务等级）。超限选项灰显，引导升级。
  - **视频质量**: 免费版上限1080p分辨率、30fps帧率。专业版无限制。
  - **音频质量**: 免费版上限192kbps比特率。专业版最高320kbp及原始音频流格式。
  - **批量规模**: 免费版批量作业最多处理该源的前20个视频。专业版前1000个视频。
  - **评论下载**: 免费版上限100条/次。专业版1000条/次。
  - **并发作业**: 每个用户同一时间内只允许1个处于运行中状态的批量作业。

## 2.3. 核心业务流程

### 2.3.1. 单次处理

- **输入**: 输入单个视频或Shorts链接。
- **解析**: 后端同步解析元数据并返回。
- **交互**: 前端展示元数据和可用处理选项，供用户选择。
- **任务创建**: 前端将用户选择和配置参数发送后端，创建工作任务（批量作业ID为 null）。
- **执行与反馈**: 任务进入异步队列，前端通过WebSocket接收实时进度。
- **交付**: 任务完成后提供签名下载链接。

### 2.3.2. 批量处理

- **输入**: 输入播放列表/频道/多视频链接，后端创建批量作业并返回ID，前端导航至作业页。
- **源解析**: 后端异步解析输入源信息。对于播放列表/频道，分批获取视频列表数据更新前端。
- **配置**: 用户选择视频，设置应用于所有选定视频的全局规则。
- **作业启动**: 前端将视频选择列表和配置发送后端，后端批量生成关联的工作任务。
- **执行与监控**: 任务进入异步队列。前端监控整体进度和每个视频的聚合状态。
- **结果交付**: 每完成一个文件立即交付。

# 3. 系统需求

## 3.1. 功能需求

前端界面遵循UI/UX原则：全站统一视觉风格（配色/字体/图标），设计简洁现代专业；页面完美适配主流桌面和移动设备，遵循基本的Web可访问性标准。操作提供即时、清晰的视觉反馈，错误信息通俗易懂并提供建议。重要操作二次确认，付费引导优雅非侵入。

### 3.1.1. 主页 (/)

- **页面布局**
  - **导航栏**: 页面顶部固定，滚动时保持可见。左侧含Logo和产品名；右侧含功能、价格、登录/注册等链接。
  - **核心交互区**: 位于首屏，含H1主标题、副标题及链接输入框（见下文）。
  - **功能特性区**: 四列图文卡片形式展示“高清视频下载”、“强大批量处理”、“独家多语言字幕”、“在线剪辑与创作”等核心卖点。
  - **操作指南区**: 三步式（粘贴链接 -> 配置选项 -> 下载）图形化流程图展示平台易用性。
  - **FAQ区**: 可折叠列表解答SEO高价值问题，如“如何下载YouTube播放列表？”。
  - **页脚**: 含免责须知以及服务条款、隐私政策、联系我们等标准链接。
- **链接输入框**: 根据输入适配不同业务流程。
  - **通用链接输入 (默认)**
    - **界面**: 单行输入框，占位符为“粘贴视频、播放列表或频道链接...”。内容非空时，框内右侧提供[x]按钮用于快速清空。输入框旁提供视觉上稍弱化的[+批量添加] 链接。紧邻输入框的[搜索]按钮样式醒目，含放大镜图标。
    - **链接识别**: 接受单个YouTube视频/Shorts/播放列表/频道链接。当URL中v=和list=共存，识别v=按视频链接处理。
    - **实时验证**: 前端监听输入实时验证格式。输入框边框颜色根据验证结果变化（默认灰/有效绿/无效红）。输入框下方动态文本实时反馈链接检测结果（如“[✓]检测到单个视频链接”或“[✗]无效的链接”）。[搜索]按钮仅在链接验证有效时激活。
  - **多视频输入**
    - **切换模式**: 点击[+批量添加]，输入框平滑扩展为20行高的多行文本框。框内有[清除]和[上传TXT]按钮。输入方式：
    - **手动输入**: 在文本框内逐行粘贴多个视频/Shorts链接，有效链接数量2-100。
    - **文件上传**: 将TXT文件拖拽至文本框，或通过[上传TXT]选择文件。文件编码需UTF-8，内容为每行一个链接。在通过文件上传覆盖文本框现有内容前，弹出确认对话框：“即将覆盖当前输入，是否继续？”。
    - **前端处理**:
      - **内容过滤**: 实时验证每行内容，自动忽略非视频/Shorts链接（如播放列表/频道），提示已忽略的无效行数。
      - **数量限制**: 若输入的有效链接总数超过100个，自动截取前100个，明确提示超出部分已被忽略。
      - **显示**: 实时更新有效链接计数。[搜索] 按钮仅在有效链接数量在2-100区间内时激活。
- **链接导航**: 点击[搜索]后，禁用所有输入控件（输入框、按钮等），页面顶部显示全局加载进度条。执行以下逻辑：
  - **输入为单个视频/Shorts链接**: 前端提取videoId导航至`/download/{videoId}`，更新浏览器地址栏。
  - **输入为播放列表、频道链接或多个视频链接**: 前端提取playlistId/channelId/videoId发送后端，请求创建批量作业。成功后返回批量作业ID，前端导航至`/batch/{batchJobId}`。创建失败则页面不跳转，输入框下方的错误提示展示清晰、非技术性的失败原因。

### 3.1.2. 单次下载页(/download/{videoId})

- **页面加载**
  - **页面布局**: 桌面端“左侧 1/4 区域预览，右侧 3/4 区域选项卡”响应式布局。移动端自动堆叠为上下结构。
  - **初始加载**: 向后端请求指定videoId数据。后端获取并整合视频元数据、可用流、字幕等信息返回前端。等待期间显示骨架屏。
  - **加载失败**: 若无法获取视频信息（如视频不存在/私有/有区域限制），骨架屏变为错误状态组件，含醒目的错误图标（如感叹号三角）、清晰的错误标题（如“无法加载此视频”）、简短的非技术性解释（如“该视频可能已被删除或是私有视频。”）、[返回首页]按钮。
- **视频信息概览 - 左侧区域**
  - **视频缩略图**: 显示最高质量缩略图。点击后弹窗，内嵌YouTube播放器供在线预览。缩略图下方有带下拉菜单的[下载缩略图 ▼]按钮，主按钮默认下载JPG，下拉菜单提供PNG格式选项。
  - **核心元数据**: 显示视频标题，最多3行，超出用省略号截断，鼠标悬浮时显示完整标题。显示频道名称，可点击，在新标签页中打开其YouTube主页。以“小图标+文字”的形式并列展示时长、上传日期、观看次数和评论数量。
  - **视频描述**: 显示前4行，提供 [展开/收起] 按钮。提供两个按钮：[复制描述]（点击后显示短暂的成功提示）和 [下载描述 (TXT)]。
  - **评论下载**: 模块标题“下载评论”，提供排序（热门/最新）和数量选择。免费用户限100条。更高选项（500/1000）灰显不可用，点击后弹出提示引导升级，内容为：“升级到Pro版，即可下载最多1000条评论。”，附带 [了解详情] 链接，指向价格页面。提供[下载XLSX] [下载CSV] [下载TXT]。若视频评论被禁用，则提示（如“此视频的评论功能已关闭”）。
- **下载与处理选项 - 右侧区域**
  - **视频选项卡**:
    - **完整视频列表**:
      - 列表展示后端智能整合后所有可用的含音频的视频文件，按视频分辨率、帧率从高到低排序。整合指的是优先提供原生带音频的MP4流，其次是合并了视频流和音频流的MP4或MKV封装文件。
      - 每项标注质量等级（如 1080p HD）、分辨率、帧率 (FPS)、预估文件大小、文件格式(MP4/MKV)、[下载]按钮。
    - **视频剪辑面板**:
      - [启用剪辑] 开关。开启后旁边出现开始和结束时间输入框（格式HH:MM:SS），提供[+]/[- ]按钮用于微调时间。校验输入的时间范围：开始≥ 0，结束>开始，结束≤视频总时长，剪辑片段时长≥ 1秒。
      - 时间范围有效时，上方完整视频列表中的每行[下载]按钮旁出现一个[剪辑下载]按钮。该按钮文本实时显示剪辑片段的预估文件大小（如“剪辑下载(12.5MB)”）。
    - **GIF制作面板**:
      - 提供开始/结束时间输入框，剪辑时长限1到30秒。提供帧率（5-20 fps，默认10，或提供高/中/低选项，对应20/15/10）和宽度（100-800 px，默认400，高宽比固定，或提供大/中/小选项，对应800/600/400）选项。
      - 点击[生成GIF]按钮后，弹度显示进度条/状态。完成后显示GIF预览图和[下载GIF]按钮。
  - **音频选项卡**:
    - **完整音频列表**:
      - 列表展示可用音频，含两种最佳原始格式（M4A和WebM）及多种比特率的转码MP3格式（320/256/192/128/64）。
      - 每项标注格式、码率、预估文件大小，并有 [下载] 按钮。
    - **音频剪辑面板**: 同视频剪辑逻辑。
    - **铃声制作面板**:
      - 提供开始/结束时间输入框，剪辑时长限1到30秒。提供淡入/淡出效果复选框（默认勾选，效果时长2秒）。输出格式提供 M4R (iPhone) 和 MP3 (Android) ，输出质量固定128kbps。
      - 点击[制作铃声]按钮后，弹出模态进度窗口。完成后窗口内出现嵌入式音频播放器供在线试听，播放器下方提供[下载文件]按钮和供手机扫描下载的二维码。
  - **字幕选项卡**:
    - **原始字幕列表**:
      - 单一列表布局，将官方字幕和自动生成字幕分组展示，通过视觉分隔。每组字幕按语言名称A-Z排序。
      - 每行包含源语言、格式下拉框(SRT/VTT/TXT)、翻译目标语言下拉框（所有YouTube支持语言）以及操作按钮。操作按钮: [查看]（点击后弹窗预览字幕内容，窗内可切换“带时间轴/纯文本”视图及一键复制）和[下载]。在“翻译目标语言”下拉框中选择某个语言后，同一行的[查看]和[下载]按钮的行为自动作用于该翻译版本。
      - 列表最下方提供[一键下载所有原始字幕 (.zip)]按钮。
      - 若视频没有任何可用字幕，此列表区域替换为提示信息：“此视频没有任何可用字幕”。
    - **多语言字幕制作面板**:
      - “选择主要语言+第二/第三语言”进行配置。主要语言为原始字幕，第二/第三语言为选中的原始字幕的翻译版本。
      - 提供“查看”按钮点击弹窗预览，以上下分行的格式展示最终多语言字幕效果。
      - 提供格式下拉框(SRT/VTT/TXT) 和“生成并下载”按钮。
- **任务处理与反馈**
  - 后端耗时操作（下载/剪辑/生成等）创建工作任务。发起任务后，弹出锁定页面的模态窗口，窗口内展示任务名称、当前状态、进度条及[取消]按钮。任务完成或失败后，窗口内容更新以反映最终状态，提供相应后续操作。
  - 在任务进行中，若用户尝试关闭或离开当前页面，浏览器触发标准的“确认离开”警告。

### 3.1.3. 批量下载页 (/batch/{batchJobId})

- **页面加载**
  - **页面布局**: 桌面端“左侧 1/4 区域全局配置，右侧 3/4 区域任务内容”响应式布局。
  - **初始加载**: 前端向后端请求指定batchJobId详情。后端对于播放列表/频道来源，异步获取其基本信息（名称、视频总数）及分批次获取视频列表（上限1000）；对于多视频链接来源，仅验证作业存在性。等待期间显示骨架屏。
  - **加载失败处理**: 若后端无法加载指定批量作业，显示与单次下载页设计一致的错误状态组件。
  - **页面状态恢复**: 当刷新或重访此页面时，加载该batchJobId最新状态，并无缝恢复其配置和所有子任务进度。
- **任务内容 - 右侧区域**
  - **任务源信息**: 页面顶部展示本次批量作业来源信息，如播放列表: [播放列表名称] (共X个视频)、频道: [频道名称] (共Y个视频)、自定义任务: [任务ID] (共Z个视频)。旁有小字提示：“注意：系统最多处理前1000个视频。”
  - **视频列表区**:
    - **加载策略 (针对播放列表/频道)**:
      - 在后端分批获取视频列表数据期间，此区域显示加载动画和状态文本，如“正在获取视频列表 (250/1000)...”。
      - 前端每接收一批数据即渲染到列表中，实现流式加载，在数据完全加载完之前就可滚动和浏览已加载的部分。
      - 加载期间，列表上方的批量操作控件（如[全选]）和左侧的筛选器处于禁用状态。列表长度受服务等级限制。
    - **列表展示**:
      - 以列表形式展示所有视频。每行包含复选框、索引号、缩略图、视频标题、时长、上传日期和观看数。
      - 对于多视频链接来源的作业，在任务启动前，列表项仅显示输入的视频ID作为占位符。详细的视频元数据（标题、缩略图等）将在该视频的工作任务实际开始处理时，才动态获取更新到UI。
      - 列表操作：列表顶部提供 [全选] / [全不选] 按钮，实时显示已选中的视频计数，如“已选中[x]/[y]个视频”。
- **全局配置 - 左侧区域**: 任务源信息加载完毕后即可显示，提供[重置]按钮以恢复所有默认设置。
  - **下载内容类型**: 提供一组开关控件，包括视频/音频/字幕/评论/缩略图/描述。默认仅开启视频，至少打开一项才能启动任务。
  - **全局格式/质量设置**: 下方配置菜单的内容根据上方开启的下载内容类型动态显示。超限选项灰显，并附带升级提示。
    - **视频**: 提供“最佳质量”、“最低质量”以及具体分辨率选项（如 1080p, 720p）。
    - **音频**: 提供“最佳可用原始格式(M4A/WebM)”和多种转码 MP3 比特率选项。
    - **字幕**: 提供语言选择（如优先下载指定语言的原始字幕）或“下载所有可用语言”的选项。
    - **评论**: 提供排序方式、下载数量和输出格式的选择。
    - **缩略图**: 提供JPG和PNG格式选择。
    - **描述**: 无额外配置项。
  - **筛选条件(仅对播放列表/频道生效)**:
    - 可折叠面板，标题为[+]筛选视频 (可选)。展开后，提供基于时长 (大于/小于)、上传日期 (在...之后/之前)、观看次数 (大于/小于) 和标题关键词的筛选控件。所有筛选条件之间必须是逻辑与(AND)的关系。
    - 面板仅在视频列表数据完全加载后才启用。筛选条件实时更新右侧的视频列表和“已选中”计数。
  - **下载目的地**: 提供两种模式的单选按钮：
    - **增强本地文件夹保存 (默认)**: 提供[选择本地目录]按钮，点击后调用File System Access API授权并选择一个本地文件夹。需及时检查兼容性及授权提示。界面显示选定路径，提供为每个视频创建单独子文件夹的复选框（默认勾选）。未勾选则所有文件平铺，文件名需包含足够信息避免冲突。保存路径：用户选择目录/[源名称]。
    - **标准浏览器下载**: 每个文件完成后逐一自动触发浏览器下载。
  - **启动按钮**: 底部有[开始下载]按钮。按钮文本动态更新以反映当前选择，如[开始下载 (已选850个视频)]。仅在至少一个视频被选中且至少选择了一种下载内容类型时才可用。按钮旁有提示文本：“每个用户同时只能进行一个批量任务”。
- **任务启动与监控**
  - **任务启动**: 点击[开始下载]，前端将选定的视频ID列表和全局配置发送至后端。后端验证后标记批量作业为运行中，为其创建所有对应的工作任务并推入消息队列。
  - **界面状态锁定**: 任务启动后，页面所有配置项（全局配置、筛选器、列表复选框等）全部禁用。[开始下载]按钮变为加载状态，然后被替换为全局的[暂停]和[取消任务]按钮。
  - **实时监控**:
    - **视频列表项变化**: 每行视频的右侧区域变为动态的状态显示区，显示该视频的聚合状态（如排队中, 下载中, 合并中, 完成, 失败）和动态进度条。正在处理的视频行可以高亮显示。
    - **文件交付**: 当一个视频的所有任务都完成后，其状态变为[文件列表]按钮。点击后弹窗显示该视频所有可下载文件的链接。
    - **单项控制**: 每个未完成的视频项旁提供[取消]按钮；失败项提供[重试]按钮。
  - **整体进度**: 列表上方有总体进度摘要，实时更新关键统计数据（如“整体进度: 35% | 已完成: 35, 处理中: 3, 失败: 2, 排队中: 60”）。
  - **任务完成**: 所有任务结束后，页面顶部显示最终总结（成功数、失败数）。若有失败项，提供[下载失败列表 (TXT)]按钮。

### 3.1.4. 用户中心

- **用户认证与账户管理**
  - **注册**: 注册表单含邮箱地址、密码和确认密码以及指向服务条款和隐私政策的链接，要求用户勾选表示同意后才可注册。
  - **登录**: 使用邮箱和密码进行认证。登录成功后，导航栏的登录/注册链接被替换为包含用户头像的下拉菜单。
  - **密码找回**: 提供“忘记密码”功能。输入注册邮箱后，系统向该邮箱发送一封包含有时效性重置链接的邮件。
  - **账户设置页面**: 通过导航栏的用户头像下拉菜单访问。
    - **修改密码**: 允许已登录用户更改密码。
    - **邮箱管理**: 显示当前绑定的邮箱地址，可扩展支持绑定/解绑邮箱。
    - **订阅管理**: 展示当前订阅计划（免费/专业）、订阅起始和到期日期、付费记录。提供[升级]或[管理订阅]按钮。
    - **账户删除**: 提供永久删除账户的选项。此操作要求用户输入密码或点击确认邮件链接的二次确认流程来防止误操作。
- **任务历史页面 (/my-downloads)**: 通过导航栏（如用户头像下拉菜单中的我的下载链接）访问。
  - **列表展示**: 展示用户发起过的所有任务，包括单次任务和批量父任务。每行任务记录展示以下核心信息：
    - **任务名称/源**: 单次任务显示其内容简述（如“下载：[视频标题]”）；批量父任务显示其来源（如“播放列表：[列表名称]”）。
    - **任务类型**: 标识为“单次下载”或“批量下载”。
    - **创建时间**: 任务或作业创建的日期和时间。
    - **状态**: 显示任务当前状态（如 处理中、完成、失败、已取消）。
    - **进度**: 对于进行中的任务，显示进度条。
  - **列表操作**:
    - **筛选与排序**: 列表顶部提供筛选控件，允许按任务类型（单次/批量）和状态过滤。提供按创建时间排序的选项。
    - **任务导航**: 点击列表任意一项，导航至对应的详情页面。
    - **单次任务跳转至其`/download/{videoId}`页面。**
    - **批量父任务跳转至其`/batch/{batchJobId}`页面，展示该作业的最新状态和进度。**
    - **任务操作**:
      - **已完成的任务**提供[重新下载]按钮，允许再次获取结果文件（在文件保留期内）。
      - **失败的任务**提供[重试]按钮，允许重新发起该任务。
      - **进行中的任务**提供[查看进度]（即导航功能）和[取消]按钮。
  - **统计信息**: 页面顶部展示关键的统计信息，如“总任务数”、“成功任务数”、“本月已用流量”等。

## 3.2. 非功能性需求

### 3.2.1. 性能

- **前端响应**: 页面加载<3s (缓存时<1s)。链接解析<5s (缓存时<500ms)。核心API<200ms。
- **后端处理效率**: 视频处理时长<1.5倍视频时长。1000条评论20秒内完成获取并生成文件。
- **并发与吞吐量**: 支持500并发用户。消息队列积压>10000。

### 3.2.2. 可靠性

- **可用性**: 核心服务单节点部署。工作节点手动水平扩缩容。单节点服务故障10分钟内人工恢复。
- **任务成功率**: 下载与处理任务成功率≥95%。工作节点内针对可恢复错误自动重试2次。
- **数据完整性**: 用户数据（账户信息、订阅状态）和任务数据保证一致和完整。
- **依赖稳定性**: yt-dlp、ffmpeg定期更新。

### 3.2.3. 安全性

- **认证与授权**: HttpOnly Secure Cookie会话管理。严格校验访问受保护资源用户身份（UserId匹配）。后台管理面板独立认证。
- **数据传输**: 强制HTTPS (TLS 1.2+)。对客户端输入进行严格的服务端验证、清理和转义。
- **滥用防治**: 对API端点实施基于IP地址和/或用户ID的速率限制，利用CDN抵御DDoS/Bot。高风险操作启用验证码。
- **文件安全**: 临时文件存储在工作节点非Web直接可访问的路径下，通过带时效性和签名的临时URL访问。过期文件自动安全删除。
- **内部API安全**: 核心服务与工作节点之间内部API使用共享密钥或简单的Token认证。

### 3.2.4. 可维护性

- **日志记录**: 实现结构化日志记录，覆盖应用日志、任务执行日志和错误日志，便于查询。
- **监控与告警**: 实时监控关键指标（CPU/内存使用率、API响应时间、API错误率、消息队列长度、代理池健康度、存储空间），配置告警。

### 3.2.5. 法律与合规性

- **用户协议**: 提供并要求同意《服务条款》和《隐私政策》（符合GDPR/CCPA）。
- **使用声明**: 明确个人非商业用途，用户需自行承担因使用服务产生的版权风险。快速处理内容移除请求。
- **数据策略**: 明确用户数据和临时文件的保留策略，提供数据删除权。长期不活跃的匿名用户超180天自动清理。

# 4. 系统设计与架构

## 4.1. 高层架构

- **面向服务的分布式架构**（前端/核心服务/消息队列/工作节点），通过异步消息解耦。集成第三方支付网关，自身不处理或存储敏感支付信息。前端集成支付网关安全支付组件，后端通过Webhook接收支付网关的订阅状态变更事件，更新用户订阅状态。
- **用户请求流程**:
  - 浏览器通过CDN加载前端应用。
  - 前端应用通过Nginx反向代理向核心服务发送API请求（如获取元数据、创建任务）。
  - 对于需要异步处理的请求，核心服务在数据库中创建WorkTask或BatchJob记录，将任务信息封装成消息发送到消息队列。
  - 多个工作节点竞争性地从消息队列中消费任务消息。
  - 工作节点在执行任务（如下载、转换）过程中，调用核心服务内部API汇报进度，将最终结果（如文件路径）更新回数据库。
  - 核心服务通过WebSocket将任务实时进度更新推送给已连接的前端。
  - 任务完成后，通过前端向核心服务请求文件下载链接，核心服务生成签名URL，通过该URL从工作节点下载文件。

## 4.2. 核心组件

### 4.2.1. 前端 (Next.js 15)

界面渲染、客户端交互、路由管理以及前端状态管理。

- 调用核心服务的公共API发起业务操作请求。
- 通过WebSocket订阅并处理来自核心服务的任务状态和进度实时更新。
- 处理文件下载，通过浏览器标准下载流程或使用File System Access API直接写入本地文件系统。

### 4.2.2. 核心服务 (ASP.NET Core 9 Minimal API)

系统“大脑”，所有业务逻辑的协调中心和前端的唯一入口。

- **API网关**: 提供所有面向前端的公共API，实现速率限制、输入验证等中间件逻辑。
- **同步解析**: 接收前端元数据解析请求，通过内部负载均衡机制选择可用工作节点，调用其内部API解析，接收结果并缓存，返回前端。
- **任务调度**: 在数据库中创建和管理WorkTask和BatchJob。根据负载分批获取待处理WorkTask，发送至消息队列，更新其状态为排队中。
- **进度更新**: 提供WebSocket服务端，维护用户ID到WebSocket连接的映射。提供API供工作节点定期汇报任务进度，收到进度后通过WebSocket推送给对应前端。
- **文件下载**: 提供生成签名下载URL的API，验证用户权限，查询文件位置，使用 HMAC 算法生成包含文件标识、用户ID和过期时间戳的安全签名URL。
- **代理池管理**: 维护和管理代理池信息。提供内部API供工作节点查询可用代理。所有对YouTube的yt-dlp调用都通过此服务获取代理。每个代理IP限制5秒内最多1次请求。代理池目前100个代理，地址p.webshare.io:80，用户名vakzyshu-1到100，密码xx9skjnzudof。

### 4.2.3. 消息队列 (RabbitMQ)

核心服务与工作节点之间的异步通信桥梁，实现任务负载均衡与系统解耦。

- 为不同优先级的任务类型（如付费用户的任务、即时性强的GIF制作任务）配置不同的队列，实现差异化服务。

### 4.2.4. 工作节点 (ASP.NET Core 9 Minimal API)

系统“四肢”，所有耗时、密集型任务的实际执行者。

- **同步解析**: 暴露内部API，接收核心服务的同步解析请求，调用yt-dlp或付费API获取元数据并同步返回。
- **异步任务处理**: 作为后台服务，持续从RabbitMQ拉取任务消息。收到消息后，立即更新数据库中对应WorkTask的状态为处理中。
- **任务执行**: 根据工作任务类型调用yt-dlp、ffmpeg等工具执行具体的文件下载、剪辑、格式转换等操作。
- **进度汇报**: 在任务执行过程中，以固定间隔（如每5秒）调用核心服务的内部API汇报当前任务的进度百分比和状态详情。
- **结果处理**: 任务完成后或失败后，将最终状态、结果链接或错误信息更新到数据库，向RabbitMQ发送ack信号，确认消息处理完毕。
- **文件存储**: 任务完成后，将生成文件存储本地硬盘，更新数据库任务记录的文件路径。处理带签名URL的文件下载请求（支持HTTP Range Requests）。定时扫描本地文件，删除已过期且关联任务已完成的文件。
- **容错处理**:
  - 消费RabbitMQ消息时采用手动ACK模式。只有当任务成功完成并记录结果后，才发送 ack。
  - 若工作节点在处理中崩溃，未被ack的消息会在超时后由RabbitMQ重新入队，交由其他工作节点处理。
  - 对于可预测的内部错误（如yt-dlp下载失败），在节点内部进行最多2次的即时重试。
  - 若内部重试均失败，则将任务在数据库中标记为失败，记录详细错误，并ack消息。

### 4.2.5. 数据库 (PostgreSQL 17)

持久化存储用户、任务和作业数据、代理池信息等。任务状态和进度写入对应任务表。

### 4.2.6. 反向代理与CDN (Nginx & CDN服务商)

HTTPS处理、负载均衡、静态资源全球分发。

# 5. 管理与运维

## 5.1. 后台管理面板

### 5.1.1. 仪表盘

- **用户与任务**: 实时在线用户、新增注册、各类任务状态（进行中、排队中、失败）的统计。
- **基础设施**: 消息队列深度、工作节点状态、代理池可用性及存储资源使用率。

### 5.1.2. 用户管理

- **查询与查看**: 通过可搜索、筛选的列表（按邮箱、ID、订阅等级）快速定位用户，并查看其详细资料、任务历史和订阅信息。
- **操作与干预**: 编辑用户信息（如手动调整订阅计划）、启用/禁用账户，以及按数据策略安全删除用户。

### 5.1.3. 任务管理

- **监控与详情**: 通过独立的、可筛选的列表视图监控所有任务。可深入查看任何任务的详细信息，包括其参数、执行日志和结果/错误报告。
- **手动操作**: 强制取消正在进行或排队中的任务，以及为失败的任务触发重试。

### 5.1.4. 系统配置

- **代理池管理**: 支持代理的增删改查，并集成自动健康检查与状态显示。
- **服务集成**: 管理第三方服务的API密钥与策略。

### 5.1.5. 工作节点管理

- **状态监控**: 实时展示各节点的运行状态（在线/离线）、任务负载和系统资源使用率。
- **安全维护**: 支持将节点设置为排空状态。在该状态下，节点将停止接收新任务，并在完成现有任务后安全下线，以便进行维护或缩容。

### 5.1.6. 内容审查

管理视频、频道、播放列表的黑名单。
