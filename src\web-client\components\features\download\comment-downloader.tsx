'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertCircle,
  Check,
  Clock,
  Download,
  FileText,
  Loader2,
  MessageCircle,
  Users,
} from 'lucide-react';

interface CommentDownloaderProps {
  commentsDisabled: boolean;
  commentCount?: number;
}

export function CommentDownloader({ commentsDisabled, commentCount }: CommentDownloaderProps) {
  const [selectedSort, setSelectedSort] = useState('hot');
  const [selectedCount, setSelectedCount] = useState('100');
  const [selectedFormat, setSelectedFormat] = useState('xlsx');
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDownloaded, setIsDownloaded] = useState(false);

  const handleDownload = async (format: string) => {
    setSelectedFormat(format);
    setIsDownloading(true);

    try {
      // 模拟下载过程
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setIsDownloaded(true);
      setTimeout(() => setIsDownloaded(false), 3000);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  if (commentsDisabled) {
    return (
      <div className="flex items-center justify-center p-6 bg-muted/30 rounded-lg border-2 border-dashed">
        <div className="text-center">
          <AlertCircle className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
          <h3 className="font-medium mb-2">评论功能已关闭</h3>
          <p className="text-sm text-muted-foreground">
            此视频的创作者已关闭评论功能，无法下载评论数据。
          </p>
        </div>
      </div>
    );
  }

  const formatOptions = [
    { value: 'xlsx', label: 'XLSX 格式', description: '适合Excel分析', icon: FileText },
    { value: 'csv', label: 'CSV 格式', description: '通用表格格式', icon: FileText },
  ];

  return (
    <Card className="border-2 border-muted/20 bg-gradient-to-br from-background to-muted/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          评论下载
          {commentCount && (
            <Badge variant="secondary" className="text-xs">
              {commentCount.toLocaleString()} 条评论
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 选项配置 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <MessageCircle className="h-4 w-4 text-primary" />
              排序方式
            </label>
            <Select value={selectedSort} onValueChange={setSelectedSort}>
              <SelectTrigger className="h-10">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hot">
                  <div className="flex items-center gap-2">
                    <span>🔥</span>
                    <span>热门评论</span>
                  </div>
                </SelectItem>
                <SelectItem value="new">
                  <div className="flex items-center gap-2">
                    <span>🕒</span>
                    <span>最新评论</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-primary" />
              下载数量
            </label>
            <Select value={selectedCount} onValueChange={setSelectedCount}>
              <SelectTrigger className="h-10">
                <SelectValue placeholder="下载数量" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="100">
                  <div className="flex items-center justify-between w-full">
                    <span>前 100 条</span>
                    <Badge variant="secondary" className="text-xs ml-2">
                      免费
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="500" disabled>
                  <div className="flex items-center justify-between w-full">
                    <span>前 500 条</span>
                    <Badge variant="outline" className="text-xs ml-2">
                      Pro
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="1000" disabled>
                  <div className="flex items-center justify-between w-full">
                    <span>前 1000 条</span>
                    <Badge variant="outline" className="text-xs ml-2">
                      Pro
                    </Badge>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 下载按钮 */}
        <div className="flex gap-2 pt-2">
          {formatOptions.map((format) => (
            <motion.div
              key={format.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownload(format.value)}
                disabled={isDownloading}
                className="w-full h-12 flex-col gap-1">
                <AnimatePresence mode="wait">
                  {isDownloading && selectedFormat === format.value ? (
                    <motion.div
                      key="downloading"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      下载中...
                    </motion.div>
                  ) : isDownloaded && selectedFormat === format.value ? (
                    <motion.div
                      key="downloaded"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center">
                      <Check className="mr-2 h-4 w-4 text-green-600" />
                      已下载
                    </motion.div>
                  ) : (
                    <motion.div
                      key="download"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex flex-col items-center">
                      <div className="flex items-center">
                        <Download className="mr-2 h-4 w-4" />
                        {format.label}
                      </div>
                      <div className="text-xs text-muted-foreground">{format.description}</div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </motion.div>
          ))}
        </div>

        {/* 提示信息 */}
        <div className="bg-muted/30 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Clock className="h-4 w-4 text-primary mt-0.5 shrink-0" />
            <div className="text-xs text-muted-foreground">
              <p className="font-medium mb-1">下载说明：</p>
              <ul className="space-y-0.5">
                <li>• 下载时间取决于评论数量，通常需要1-3分钟</li>
                <li>• 免费用户限制下载前100条评论</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
