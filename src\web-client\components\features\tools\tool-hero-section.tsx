import { Badge } from '@/components/ui/badge';
import { Shield, Star, Users } from 'lucide-react';
import { LinkInputForm } from '@/components/shared/link-input-form';

interface ToolHeroSectionProps {
  title: string;
  description: string;
  category: string;
  inputPlaceholder: string;
}

export function ToolHeroSection({
  title,
  description,
  category,
  inputPlaceholder,
}: ToolHeroSectionProps) {
  const getCategoryLabel = (cat: string) => {
    switch (cat) {
      case 'converter':
        return '格式转换';
      case 'downloader':
        return '内容下载';
      case 'tool':
        return '创作工具';
      default:
        return '工具';
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50/30">
      <div className="container mx-auto px-4">
        <div className="text-center">
          {/* 类别标签 */}
          <div className="inline-flex items-center gap-2 mb-6">
            <Badge className="text-sm px-3 py-1">{getCategoryLabel(category)}</Badge>
          </div>

          {/* 标题 */}
          <h1 className="text-4xl font-bold tracking-tight md:text-6xl mb-6 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
            {title}
          </h1>

          {/* 描述 */}
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8 leading-relaxed">
            {description}
          </p>

          {/* 统计信息 */}
          <div className="flex items-center justify-center gap-8 mb-10 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>10万+用户</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span>4.9评分</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-500" />
              <span>安全可靠</span>
            </div>
          </div>

          {/* 输入表单 */}
          <div className="max-w-2xl mx-auto">
            <LinkInputForm placeholder={inputPlaceholder} />
          </div>
        </div>
      </div>
    </section>
  );
}
