'use client';

import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Clock, Minus, Play, Plus, Scissors } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { VideoPageData } from '@/lib/types';
import { formatDuration } from '@/lib/youtube-utils';

interface VideoClipPanelProps {
  videoData: VideoPageData;
  onClipDownload: (startTime: number, endTime: number, format: string) => void;
}

interface TimeInput {
  hours: number;
  minutes: number;
  seconds: number;
}

export function VideoClipPanel({ videoData, onClipDownload }: VideoClipPanelProps) {
  const [isClipEnabled, setIsClipEnabled] = useState(false);
  const [startTime, setStartTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 0 });
  const [endTime, setEndTime] = useState<TimeInput>({ hours: 0, minutes: 0, seconds: 30 });
  const [isValid, setIsValid] = useState(false);
  const [estimatedSize, setEstimatedSize] = useState<number>(0);

  // 将时间对象转换为秒数
  const timeToSeconds = (time: TimeInput): number => {
    return time.hours * 3600 + time.minutes * 60 + time.seconds;
  };

  // 将秒数转换为时间对象
  const secondsToTime = (seconds: number): TimeInput => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return { hours, minutes, seconds: secs };
  };

  // 验证时间范围
  const validateTimeRange = () => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    const valid =
      startSeconds >= 0 &&
      endSeconds > startSeconds &&
      endSeconds <= videoData.duration &&
      duration >= 1;

    setIsValid(valid);

    if (valid) {
      // 估算文件大小 (假设平均码率)
      const avgBitrate = 2000; // kbps
      const sizeInMB = (duration * avgBitrate) / (8 * 1024);
      setEstimatedSize(Math.round(sizeInMB * 100) / 100);
    } else {
      setEstimatedSize(0);
    }
  };

  // 时间输入变化处理
  const handleTimeChange = (
    type: 'start' | 'end',
    field: 'hours' | 'minutes' | 'seconds',
    value: number,
  ) => {
    const newTime = { ...(type === 'start' ? startTime : endTime) };
    newTime[field] = Math.max(0, value);

    // 限制范围
    if (field === 'hours') newTime[field] = Math.min(23, newTime[field]);
    if (field === 'minutes' || field === 'seconds') newTime[field] = Math.min(59, newTime[field]);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 微调时间
  const adjustTime = (type: 'start' | 'end', delta: number) => {
    const currentSeconds = timeToSeconds(type === 'start' ? startTime : endTime);
    const newSeconds = Math.max(0, Math.min(videoData.duration, currentSeconds + delta));
    const newTime = secondsToTime(newSeconds);

    if (type === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // 格式化时间显示
  const formatTimeInput = (time: TimeInput): string => {
    return `${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
  };

  // 解析时间输入
  const parseTimeInput = (timeStr: string): TimeInput => {
    const parts = timeStr.split(':').map(Number);
    return {
      hours: parts[0] || 0,
      minutes: parts[1] || 0,
      seconds: parts[2] || 0,
    };
  };

  useEffect(() => {
    validateTimeRange();
  }, [startTime, endTime, videoData.duration]);

  // 获取验证错误信息
  const getValidationError = (): string | null => {
    const startSeconds = timeToSeconds(startTime);
    const endSeconds = timeToSeconds(endTime);
    const duration = endSeconds - startSeconds;

    if (startSeconds < 0) return '开始时间不能为负数';
    if (endSeconds <= startSeconds) return '结束时间必须大于开始时间';
    if (endSeconds > videoData.duration) return '结束时间不能超过视频总时长';
    if (duration < 1) return '剪辑片段时长至少为1秒';

    return null;
  };

  const validationError = getValidationError();

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Scissors className="h-5 w-5 text-primary" />
            视频剪辑
          </CardTitle>
          <div className="flex items-center gap-2">
            <Label htmlFor="clip-enabled" className="text-sm">
              启用剪辑
            </Label>
            <Switch id="clip-enabled" checked={isClipEnabled} onCheckedChange={setIsClipEnabled} />
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        {isClipEnabled && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}>
            <CardContent className="space-y-4">
              {/* 时间范围设置 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 开始时间 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">开始时间</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="text"
                      value={formatTimeInput(startTime)}
                      onChange={(e) => setStartTime(parseTimeInput(e.target.value))}
                      placeholder="00:00:00"
                      className="font-mono text-center"
                    />
                    <div className="flex flex-col gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 w-6 p-0"
                        onClick={() => adjustTime('start', 1)}>
                        <Plus className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 w-6 p-0"
                        onClick={() => adjustTime('start', -1)}>
                        <Minus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 结束时间 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">结束时间</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="text"
                      value={formatTimeInput(endTime)}
                      onChange={(e) => setEndTime(parseTimeInput(e.target.value))}
                      placeholder="00:00:30"
                      className="font-mono text-center"
                    />
                    <div className="flex flex-col gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 w-6 p-0"
                        onClick={() => adjustTime('end', 1)}>
                        <Plus className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 w-6 p-0"
                        onClick={() => adjustTime('end', -1)}>
                        <Minus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 时间信息显示 */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>
                      片段时长:{' '}
                      {formatDuration(
                        Math.max(0, timeToSeconds(endTime) - timeToSeconds(startTime)),
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>视频总长: {formatDuration(videoData.duration)}</span>
                  </div>
                </div>
                {isValid && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    预估大小: {estimatedSize} MB
                  </Badge>
                )}
              </div>

              {/* 验证错误提示 */}
              {validationError && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                  <span className="text-sm text-red-700">{validationError}</span>
                </div>
              )}

              {/* 快捷时间设置 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">快捷设置</Label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setStartTime({ hours: 0, minutes: 0, seconds: 0 });
                      setEndTime({ hours: 0, minutes: 0, seconds: 30 });
                    }}>
                    前30秒
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setStartTime({ hours: 0, minutes: 0, seconds: 0 });
                      setEndTime({ hours: 0, minutes: 1, seconds: 0 });
                    }}>
                    前1分钟
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const totalSeconds = videoData.duration;
                      setStartTime(secondsToTime(Math.max(0, totalSeconds - 30)));
                      setEndTime(secondsToTime(totalSeconds));
                    }}>
                    后30秒
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const totalSeconds = videoData.duration;
                      const midPoint = Math.floor(totalSeconds / 2);
                      setStartTime(secondsToTime(Math.max(0, midPoint - 15)));
                      setEndTime(secondsToTime(Math.min(totalSeconds, midPoint + 15)));
                    }}>
                    中间30秒
                  </Button>
                </div>
              </div>

              {/* 预览控制 */}
              {isValid && (
                <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                    <span className="text-sm text-blue-700">
                      剪辑范围已设置：{formatTimeInput(startTime)} - {formatTimeInput(endTime)}
                    </span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100">
                    <Play className="h-3 w-3 mr-1" />
                    预览片段
                  </Button>
                </div>
              )}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}
