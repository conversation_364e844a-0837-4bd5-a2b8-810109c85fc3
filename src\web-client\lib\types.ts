// 工作任务类型枚举
export type WorkTaskType =
  | 'VIDEO_DOWNLOAD'
  | 'AUDIO_CONVERT'
  | 'GIF_CREATE'
  | 'SUBTITLE_DOWNLOAD'
  | 'COMMENT_DOWNLOAD'
  | 'THUMBNAIL_DOWNLOAD'
  | 'METADATA_DOWNLOAD';

// 工作任务状态枚举
export type WorkTaskStatus =
  | 'pending'
  | 'queued'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

// 批量作业源类型枚举
export type BatchJobSourceType = 'Playlist' | 'Channel' | 'MultiVideoList';

// 批量作业状态枚举
export type BatchJobStatus =
  | 'pending'
  | 'running'
  | 'partially_completed'
  | 'completed'
  | 'failed'
  | 'cancelled';

// 用户订阅等级枚举
export type SubscriptionTier = 'Free' | 'Pro';

// 工作任务实体
export interface WorkTask {
  id: string;
  userId: string;
  batchJobId: string | null;
  type: WorkTaskType;
  params: Record<string, unknown> | string;
  status: WorkTaskStatus;
  progress: number;
  result: Record<string, unknown> | null;
  error: string | null;
  createdAt: string;
  startedAt: string | null;
  finishedAt: string | null;
}

// 批量作业实体
export interface BatchJob {
  id: string;
  userId: string;
  sourceType: BatchJobSourceType;
  sourceIdentifier: Record<string, unknown> | string;
  config: Record<string, unknown> | string;
  status: BatchJobStatus;
  progress: number;
  createdAt: string;
  startedAt: string | null;
  finishedAt: string | null;
  tasks?: WorkTask[];
}

// 视频元数据
export interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  duration: number;
  channelName: string;
  channelUrl: string;
  uploadDate: string;
  viewCount: number;
  commentCount: number;
  commentsDisabled: boolean;
  thumbnailUrl: string;
  thumbnails: { format: 'jpg' | 'png'; url: string }[];
}

// 媒体流信息
export interface MediaStream {
  qualityLabel: string;
  resolution: string;
  fps: number;
  fileSize: number;
  format: string;
  bitrate?: number;
  downloadId: string;
}

// 字幕信息
export interface SubtitleInfo {
  langCode: string;
  langName: string;
  isAutoGenerated: boolean;
}

// 视频页面数据（包含所有可用流和字幕）
export interface VideoPageData extends VideoMetadata {
  videoStreams: MediaStream[];
  audioStreams: MediaStream[];
  subtitles: SubtitleInfo[];
}

// 批量作业中的视频项
export interface BatchVideoItem {
  id: string;
  title?: string;
  thumbnailUrl?: string;
  duration?: number;
  uploadDate?: string;
  viewCount?: number;
  selected: boolean;
  status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  tasks?: WorkTask[];
  error?: string;
  completedFiles?: CompletedFile[];
}

// 完成的文件信息
export interface CompletedFile {
  type: 'video' | 'audio' | 'subtitle' | 'thumbnail' | 'comment' | 'description';
  filename: string;
  fileSize: number;
  downloadUrl: string;
  format: string;
}

// 批量下载全局配置
export interface BatchDownloadConfig {
  contentTypes: {
    video: boolean;
    audio: boolean;
    subtitle: boolean;
    comment: boolean;
    thumbnail: boolean;
    description: boolean;
  };
  videoQuality: 'best' | 'worst' | '1080p' | '720p' | '480p' | '360p';
  audioQuality: 'best' | 'mp3_320' | 'mp3_192' | 'mp3_128';
  subtitleLanguages: string[] | 'all';
  commentSettings: {
    sortBy: 'top' | 'newest';
    maxCount: number;
    format: 'xlsx' | 'csv' | 'txt';
  };
  thumbnailFormat: 'jpg' | 'png';
  destination: {
    type: 'browser' | 'local_folder';
    path?: string;
    createSubfolders: boolean;
  };
}

// 批量作业筛选条件
export interface BatchJobFilters {
  duration?: {
    operator: 'gt' | 'lt';
    value: number;
  };
  uploadDate?: {
    operator: 'after' | 'before';
    value: string;
  };
  viewCount?: {
    operator: 'gt' | 'lt';
    value: number;
  };
  titleKeywords?: string[];
}

// 批量作业详细信息（重写而非继承以避免类型冲突）
export interface BatchJobDetails {
  id: string;
  userId: string;
  sourceType: BatchJobSourceType;
  sourceIdentifier: Record<string, unknown> | string;
  status: BatchJobStatus;
  progress: number;
  createdAt: string;
  startedAt: string | null;
  finishedAt: string | null;
  tasks?: WorkTask[];
  sourceInfo: {
    name: string;
    totalVideos: number;
    description?: string;
    thumbnailUrl?: string;
  };
  videos: BatchVideoItem[];
  config: BatchDownloadConfig;
  filters?: BatchJobFilters;
  stats: {
    totalSelected: number;
    completed: number;
    processing: number;
    failed: number;
    queued: number;
    pending: number;
  };
}

// 批量作业创建请求
export interface CreateBatchJobRequest {
  sourceType: BatchJobSourceType;
  sourceIdentifier?: any; // 可以是任意对象，根据sourceType而定
  config?: any; // 可选的配置对象
}

// 批量作业创建响应
export interface CreateBatchJobResponse {
  batchJobId: string;
}

// 启动批量作业请求
export interface StartBatchJobRequest {
  selectedVideoIds: string[];
  config: BatchDownloadConfig;
  filters?: BatchJobFilters;
}

// 用户信息
export interface User {
  id: string;
  email: string;
  username?: string;
  avatarUrl?: string;
  subscriptionTier: SubscriptionTier;
  createdAt: string;
  lastLoginAt?: string;
  emailVerified: boolean;
}

// 订阅信息
export interface Subscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  startDate: string;
  endDate?: string;
  autoRenew: boolean;
  paymentHistory: PaymentRecord[];
}

// 付费记录
export interface PaymentRecord {
  id: string;
  amount: number;
  currency: string;
  paidAt: string;
  paymentMethod: string;
  invoiceUrl?: string;
}

// 任务历史项
export interface TaskHistoryItem {
  id: string;
  type: 'single' | 'batch';
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  completedAt?: string;
  progress: number;
  sourceUrl?: string;
  videoCount?: number;
  completedVideoCount?: number;
  failedVideoCount?: number;
  totalFileSize?: number;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// 登录响应
export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken?: string;
}

// 注册请求
export interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  username?: string;
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;
}

// 注册响应
export interface RegisterResponse {
  user: User;
  accessToken: string;
  requiresEmailVerification: boolean;
}

// 密码重置请求
export interface ForgotPasswordRequest {
  email: string;
}

// 密码重置响应
export interface ForgotPasswordResponse {
  message: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// 修改密码请求
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 更新用户信息请求
export interface UpdateUserRequest {
  username?: string;
  avatarUrl?: string;
}

// 删除账户请求
export interface DeleteAccountRequest {
  password: string;
  reason?: string;
}
