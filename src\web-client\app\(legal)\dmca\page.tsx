import { Metadata } from 'next';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { LegalContent, LegalSection, LegalSubSection } from '@/components/shared/legal-component';

export const metadata: Metadata = {
  title: '版权政策 (DMCA) - YTDownloader',
  description: 'YTDownloader的数字千年版权法(DMCA)政策，版权投诉流程和知识产权保护条款。',
};

export default function DMCAPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow">
        <LegalContent
          title="版权政策 (DMCA)"
          subtitle="我们尊重知识产权，严格遵守数字千年版权法 (DMCA)"
          lastUpdated="2024年1月1日">
          <LegalSection title="1. DMCA 合规声明">
            <p>
              YTDownloader 严格遵守《数字千年版权法》(Digital Millennium Copyright Act, DMCA)
              和其他适用的版权法律。我们尊重版权所有者的权利，并会及时响应有效的版权侵权通知。
            </p>
            <p>
              我们不存储、托管或分发任何受版权保护的内容。我们的服务仅作为技术工具，
              帮助用户访问和下载公开可用的内容。用户有责任确保其使用行为符合相关法律法规。
            </p>
          </LegalSection>

          <LegalSection title="2. 版权侵权通知">
            <p>如果您认为您的版权作品在我们的服务中被侵权，请向我们发送包含以下信息的书面通知：</p>

            <LegalSubSection title="2.1 必需信息">
              <ul className="list-disc pl-6 space-y-2">
                <li>版权所有者或其授权代理人的物理或电子签名</li>
                <li>被侵权的版权作品的详细描述</li>
                <li>涉嫌侵权材料的具体位置信息</li>
                <li>您的联系信息（姓名、地址、电话号码、电子邮件）</li>
                <li>声明您有合理理由相信使用该材料未经版权所有者授权</li>
                <li>声明通知中的信息准确无误，并且您是版权所有者或其授权代理人</li>
              </ul>
            </LegalSubSection>

            <LegalSubSection title="2.2 通知发送方式">
              <p>请将DMCA通知发送至：</p>
              <ul className="list-none space-y-2 mt-4">
                <li>
                  <strong>DMCA邮箱：</strong> <EMAIL>
                </li>
                <li>
                  <strong>法务邮箱：</strong> <EMAIL>
                </li>
                <li>
                  <strong>邮寄地址：</strong> [公司法务部门地址]
                </li>
              </ul>
            </LegalSubSection>
          </LegalSection>

          <LegalSection title="3. 反通知程序">
            <p>如果您认为您的内容被错误移除，您可以发送反通知。反通知必须包含：</p>

            <LegalSubSection title="3.1 反通知要求">
              <ul className="list-disc pl-6 space-y-2">
                <li>您的物理或电子签名</li>
                <li>被移除内容的描述和原位置</li>
                <li>声明您有合理理由相信内容被错误移除</li>
                <li>您的姓名、地址、电话号码</li>
                <li>同意接受法院管辖权的声明</li>
              </ul>
            </LegalSubSection>
          </LegalSection>

          <LegalSection title="4. 重复侵权者政策">
            <p>
              我们对重复侵权者采取零容忍政策。对于多次违反版权的用户，
              我们将根据情况暂停或终止其使用我们服务的权利。
            </p>
          </LegalSection>

          <LegalSection title="5. 免责声明">
            <p>
              YTDownloader 不对用户使用我们的服务下载的内容承担责任。
              用户必须确保其下载行为符合适用的版权法和其他法律。
            </p>
          </LegalSection>

          <LegalSection title="6. 联系信息">
            <p>如有版权相关问题，请联系我们：</p>
            <ul className="list-none space-y-2 mt-4">
              <li>
                <strong>版权事务：</strong> <EMAIL>
              </li>
              <li>
                <strong>法务咨询：</strong> <EMAIL>
              </li>
              <li>
                <strong>客服支持：</strong> <EMAIL>
              </li>
              <li>
                <strong>公司地址：</strong> [公司注册地址]
              </li>
            </ul>
          </LegalSection>
        </LegalContent>
      </main>
      <Footer />
    </div>
  );
}
