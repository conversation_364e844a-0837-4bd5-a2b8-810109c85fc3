import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, CheckCircle, Download, Languages, Play, Scissors, Zap } from 'lucide-react';

import Link from 'next/link';

const detailedFeatures = [
  {
    icon: <Download className="h-12 w-12" />,
    title: '专业级视频下载',
    subtitle: '高质量、多格式、批量处理',
    description:
      '我们的视频下载功能支持从YouTube获取最高8K分辨率的视频内容，提供MP4、WebM、AVI等多种格式选择。无论是单个视频还是整个播放列表，都能快速、稳定地完成下载任务。',
    features: [
      '支持最高8K分辨率下载',
      '多种视频格式：MP4、WebM、AVI、MOV',
      '音频提取：MP3、AAC、FLAC、WAV',
      '批量下载播放列表和频道',
      '自动选择最佳质量',
      '断点续传功能',
    ],
    stats: {
      quality: '最高8K',
      formats: '15+格式',
      speed: '极速下载',
    },
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30',
    link: '/youtube-to-mp4-converter',
  },
  {
    icon: <Zap className="h-12 w-12" />,
    title: '智能批量处理',
    subtitle: '一键处理，自动化工作流',
    description:
      '强大的批量处理引擎能够同时处理多达1000个视频链接，支持播放列表、频道和自定义链接列表。智能队列管理确保处理效率，实时进度跟踪让您随时了解任务状态。',
    features: [
      '同时处理最多1000个链接',
      '支持播放列表和频道批量下载',
      '智能队列管理和优先级设置',
      '实时进度跟踪和状态更新',
      '失败重试和错误恢复机制',
      '批量任务历史记录',
    ],
    stats: {
      capacity: '1000个链接',
      efficiency: '并行处理',
      reliability: '99.9%成功率',
    },
    color: 'from-purple-500 to-pink-500',
    bgColor: 'from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30',
    link: '/batch-downloader',
  },
  {
    icon: <Languages className="h-12 w-12" />,
    title: '多语言字幕系统',
    subtitle: '全球化内容，本地化体验',
    description:
      '独家的多语言字幕功能支持100+种语言的字幕下载和处理。不仅可以获取官方字幕和自动生成字幕，还能创建双语或三语合并字幕，满足语言学习和国际化需求。',
    features: [
      '支持100+种语言字幕下载',
      '官方字幕和自动生成字幕',
      '双语/三语字幕合并功能',
      '多种字幕格式：SRT、VTT、ASS、TXT',
      '字幕时间轴调整和编辑',
      '批量字幕下载和打包',
    ],
    stats: {
      languages: '100+语言',
      formats: '4种格式',
      accuracy: '高精度翻译',
    },
    color: 'from-green-500 to-emerald-500',
    bgColor: 'from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30',
    link: '/subtitle-downloader',
  },
  {
    icon: <Scissors className="h-12 w-12" />,
    title: '在线创作工具',
    subtitle: '无需软件，云端创作',
    description:
      '集成的在线创作工具套件让您无需下载任何软件即可完成视频剪辑、GIF制作、音频提取和铃声创作。所有处理都在云端完成，支持实时预览和高质量输出。',
    features: [
      '在线视频剪辑和片段提取',
      'GIF动图制作，支持自定义帧率',
      '音频提取和格式转换',
      '手机铃声制作（M4R/MP3）',
      '实时预览和质量调整',
      '云端处理，无需本地软件',
    ],
    stats: {
      tools: '6种工具',
      processing: '云端处理',
      preview: '实时预览',
    },
    color: 'from-orange-500 to-red-500',
    bgColor: 'from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30',
    link: '/video-editor',
  },
];

export function DetailedFeaturesSection() {
  return (
    <section className="container mx-auto px-4 py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold md:text-5xl mb-6 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
          专业工具，无限可能
        </h2>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          深入了解我们的核心功能，每一个工具都经过精心设计，为您提供最专业、最高效的YouTube内容处理体验
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
        {detailedFeatures.map((feature, index) => (
          <div
            key={feature.title}
            className="transform transition-all duration-300 hover:scale-105">
            <Card
              className={`group relative overflow-hidden border-0 bg-gradient-to-br ${feature.bgColor} shadow-xl hover:shadow-2xl transition-all duration-500 h-full`}>
              {/* 背景装饰 */}
              <div
                className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${feature.color} opacity-10 rounded-full blur-3xl group-hover:opacity-20 transition-opacity duration-500`}
              />
              <div
                className={`absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr ${feature.color} opacity-5 rounded-full blur-2xl`}
              />

              <CardHeader className="relative pb-4">
                {/* 图标和统计信息 */}
                <div className="flex items-start justify-between mb-6">
                  <div
                    className={`p-4 rounded-2xl bg-gradient-to-br ${feature.color} text-white shadow-lg group-hover:shadow-xl group-hover:scale-110 group-hover:rotate-6 transition-transform duration-300`}>
                    {feature.icon}
                  </div>
                  <div className="flex flex-col gap-2">
                    {Object.entries(feature.stats).map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="text-xs font-medium">
                        {value}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* 标题和副标题 */}
                <CardTitle className="text-2xl mb-2 group-hover:text-primary transition-colors">
                  {feature.title}
                </CardTitle>
                <p className="text-sm text-muted-foreground font-medium mb-4">{feature.subtitle}</p>

                {/* 描述 */}
                <CardDescription className="text-base leading-relaxed mb-6">
                  {feature.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="relative pt-0">
                {/* 功能列表 */}
                <div className="space-y-3 mb-8">
                  {feature.features.map((item, idx) => (
                    <div
                      key={idx}
                      className="flex items-start gap-3 group/item animate-fade-in-left opacity-0"
                      style={{
                        animationDelay: `${idx * 100}ms`,
                        animationFillMode: 'forwards',
                      }}>
                      <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded-full mt-0.5 group-hover/item:bg-green-200 dark:group-hover/item:bg-green-800/50 transition-colors">
                        <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-sm font-medium group-hover/item:text-primary transition-colors">
                        {item}
                      </span>
                    </div>
                  ))}
                </div>

                {/* 行动按钮 */}
                <Link href={feature.link}>
                  <Button
                    className={`w-full bg-gradient-to-r ${feature.color} hover:opacity-90 text-white font-semibold py-3 group-hover:shadow-lg transition-all duration-300`}
                    size="lg">
                    <Play className="mr-2 h-5 w-5" />
                    立即体验
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>

      {/* 底部统计和信任指标 */}
      <div className="mt-20 text-center">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">10万+</div>
            <div className="text-sm text-muted-foreground">活跃用户</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">99.9%</div>
            <div className="text-sm text-muted-foreground">成功率</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">24/7</div>
            <div className="text-sm text-muted-foreground">在线服务</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-primary">100+</div>
            <div className="text-sm text-muted-foreground">支持语言</div>
          </div>
        </div>
      </div>
    </section>
  );
}
